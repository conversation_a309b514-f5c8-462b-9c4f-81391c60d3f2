{"nameShort": "<PERSON><PERSON>", "nameLong": "Ponder IDE", "applicationName": "ponder", "dataFolderName": ".ponder", "win32MutexName": "ponder", "licenseName": "MIT", "licenseUrl": "https://github.com/microsoft/vscode/blob/main/LICENSE.txt", "serverLicenseUrl": "https://github.com/microsoft/vscode/blob/main/LICENSE.txt", "serverGreeting": [], "serverLicense": [], "serverLicensePrompt": "", "serverApplicationName": "ponder-server", "serverDataFolderName": ".ponder-server", "tunnelApplicationName": "ponder-tunnel", "win32DirName": "Ponder IDE", "win32NameVersion": "Ponder IDE", "win32RegValueName": "<PERSON><PERSON>", "win32x64AppId": "{{D77B7E06-80BA-4137-BCF4-654B95CCEBC5}", "win32arm64AppId": "{{D1ACE434-89C5-48D1-88D3-E2991DF85475}", "win32x64UserAppId": "{{CC6B787D-37A0-49E8-AE24-8559A032BE0C}", "win32arm64UserAppId": "{{3AEBF0C8-F733-4AD4-BADE-FDB816D53D7B}", "win32AppUserModelId": "Ponder.IDE", "win32ShellNameShort": "P&onder", "win32TunnelServiceMutex": "ponder-tunnelservice", "win32TunnelMutex": "ponder-tunnel", "darwinBundleIdentifier": "com.ponder.ide", "darwinProfileUUID": "47827DD9-4734-49A0-AF80-7E19B11495CC", "darwinProfilePayloadUUID": "CF808BE7-53F3-46C6-A7E2-7EDB98A5E959", "linuxIconName": "ponder", "licenseFileName": "LICENSE.txt", "reportIssueUrl": "https://github.com/microsoft/vscode/issues/new", "nodejsRepository": "https://nodejs.org", "urlProtocol": "ponder", "webviewContentExternalBaseUrlTemplate": "https://{{uuid}}.vscode-cdn.net/insider/ef65ac1ba57f57f2a3961bfe94aa20481caca4c6/out/vs/workbench/contrib/webview/browser/pre/", "builtInExtensions": [{"name": "ms-vscode.js-debug-companion", "version": "1.1.3", "sha256": "7380a890787452f14b2db7835dfa94de538caf358ebc263f9d46dd68ac52de93", "repo": "https://github.com/microsoft/vscode-js-debug-companion", "metadata": {"id": "99cb0b7f-7354-4278-b8da-6cc79972169d", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}, {"name": "ms-vscode.js-debug", "version": "1.102.0", "sha256": "0e8ed27ba2d707bcfb008e89e490c2d287d9537d84893b0792a4ee418274fa0b", "repo": "https://github.com/microsoft/vscode-js-debug", "metadata": {"id": "25629058-ddac-4e17-abba-74678e126c5d", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}, {"name": "ms-vscode.vscode-js-profile-table", "version": "1.0.10", "sha256": "7361748ddf9fd09d8a2ed1f2a2d7376a2cf9aae708692820b799708385c38e08", "repo": "https://github.com/microsoft/vscode-js-profile-visualizer", "metadata": {"id": "7e52b41b-71ad-457b-ab7e-0620f1fc4feb", "publisherId": {"publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherName": "ms-vscode", "displayName": "Microsoft", "flags": "verified"}, "publisherDisplayName": "Microsoft"}}], "extensionsGallery": {"serviceUrl": "https://marketplace.visualstudio.com/_apis/public/gallery", "controlUrl": "https://az764295.vo.msecnd.net/extensions/marketplace.json", "mcpUrl": "https://az764295.vo.msecnd.net/extensions/marketplace.json", "extensionUrlTemplate": "https://marketplace.visualstudio.com/_apis/public/gallery/publishers/{publisher}/vsextensions/{name}/{version}/vspackage", "resourceUrlTemplate": "https://marketplace.visualstudio.com/_apis/public/gallery/publisher/{publisher}/extension/{name}/{version}/assetbyname/{target}", "nlsBaseUrl": "https://www.vscode-unpkg.net/_pkg"}, "commit": "9df2e0f1ef56cd7bcef40a92d8f4f46ca576ee3f", "date": "2025-07-26T07:56:03.193Z", "checksums": {"vs/base/parts/sandbox/electron-browser/preload.js": "LYfoH1nP31wzxQTEgGjSTS7zAqTNBOLJSsg+bXR9nCE", "vs/workbench/workbench.desktop.main.js": "oA0xEfsXXQRuMlNOrBBmjGQelZP48RGzneNzjjvWd2Y", "vs/workbench/workbench.desktop.main.css": "9d+xg9oArUjuz88MDNqBVHmgubu8RxBaYYjcybRXCLA", "vs/workbench/api/node/extensionHostProcess.js": "3LAdOCTivqFEytt9C0i/tMAGWZqDIJUfcduL+fzjE5E", "vs/code/electron-browser/workbench/workbench.html": "XxZmhkafEA0koIRMeDfutEDrKbofWef2nrfCWhdYL14", "vs/code/electron-browser/workbench/workbench.js": "mmhBoiE6or629GnDuBA4DLZ+UZBoTKNaULWo9HVz5Gw"}, "version": "1.103.0"}