/*
 * Application Insights JavaScript SDK - Core, 2.8.15
 * Copyright (c) Microsoft and contributors. All rights reserved.
 */
// 
// 
import { __extendsFn as __extends } from "@microsoft/applicationinsights-shims";
import dynamicProto from "@microsoft/dynamicproto-js";
import { _DYN_APPLY, _DYN_DIAG_LOG, _DYN_LENGTH, _DYN_PROCESS_NEXT, _DYN_PUSH, _DYN_SPLICE, _DYN__DO_TEARDOWN } from "../__DynamicConstants";
import { BaseTelemetryPlugin } from "./BaseTelemetryPlugin";
import { _throwInternal } from "./DiagnosticLogger";
import { dumpObj } from "./EnvUtils";
import { arrForEach, getExceptionName } from "./HelperFuncs";
import { STR_PROCESS_TELEMETRY } from "./InternalConstants";
var TelemetryInitializerPlugin = /** @class */ (function (_super) {
    __extends(TelemetryInitializerPlugin, _super);
    function TelemetryInitializerPlugin() {
        var _this = _super.call(this) || this;
        _this.identifier = "TelemetryInitializerPlugin";
        _this.priority = 199;
        // NOTE!: DON'T set default values here, instead set them in the _initDefaults() function as it is also called during teardown()
        var _id;
        var _initializers;
        _initDefaults();
        dynamicProto(TelemetryInitializerPlugin, _this, function (_self, _base) {
            _self.addTelemetryInitializer = function (telemetryInitializer) {
                var theInitializer = {
                    id: _id++,
                    fn: telemetryInitializer
                };
                _initializers[_DYN_PUSH /* @min:%2epush */](theInitializer);
                var handler = {
                    remove: function () {
                        arrForEach(_initializers, function (initializer, idx) {
                            if (initializer.id === theInitializer.id) {
                                _initializers[_DYN_SPLICE /* @min:%2esplice */](idx, 1);
                                return -1;
                            }
                        });
                    }
                };
                return handler;
            };
            _self[STR_PROCESS_TELEMETRY /* @min:%2eprocessTelemetry */] = function (item, itemCtx) {
                var doNotSendItem = false;
                var telemetryInitializersCount = _initializers[_DYN_LENGTH /* @min:%2elength */];
                for (var i = 0; i < telemetryInitializersCount; ++i) {
                    var telemetryInitializer = _initializers[i];
                    if (telemetryInitializer) {
                        try {
                            if (telemetryInitializer.fn[_DYN_APPLY /* @min:%2eapply */](null, [item]) === false) {
                                doNotSendItem = true;
                                break;
                            }
                        }
                        catch (e) {
                            // log error but dont stop executing rest of the telemetry initializers
                            // doNotSendItem = true;
                            _throwInternal(itemCtx[_DYN_DIAG_LOG /* @min:%2ediagLog */](), 1 /* eLoggingSeverity.CRITICAL */, 64 /* _eInternalMessageId.TelemetryInitializerFailed */, "One of telemetry initializers failed, telemetry item will not be sent: " + getExceptionName(e), { exception: dumpObj(e) }, true);
                        }
                    }
                }
                if (!doNotSendItem) {
                    _self[_DYN_PROCESS_NEXT /* @min:%2eprocessNext */](item, itemCtx);
                }
            };
            _self[_DYN__DO_TEARDOWN /* @min:%2e_doTeardown */] = function () {
                _initDefaults();
            };
        });
        function _initDefaults() {
            _id = 0;
            _initializers = [];
        }
        return _this;
    }
// Removed Stub for TelemetryInitializerPlugin.prototype.addTelemetryInitializer.
// Removed Stub for TelemetryInitializerPlugin.prototype.processTelemetry.
    // This is a workaround for an IE8 bug when using dynamicProto() with classes that don't have any
    // non-dynamic functions or static properties/functions when using uglify-js to minify the resulting code.
    // this will be removed when ES3 support is dropped.
    TelemetryInitializerPlugin.__ieDyn=1;

    return TelemetryInitializerPlugin;
}(BaseTelemetryPlugin));
export { TelemetryInitializerPlugin };
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/9df2e0f1ef56cd7bcef40a92d8f4f46ca576ee3f/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK/TelemetryInitializerPlugin.js.map