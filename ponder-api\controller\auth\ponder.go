package auth

import (
	"errors"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"

	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/random"
	"github.com/songquanpeng/one-api/model"
)

type PonderOAuthResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
	Scope       string `json:"scope"`
}

type AuthCodeData struct {
	UserID      int    `json:"user_id"`
	ClientID    string `json:"client_id"`
	RedirectURI string `json:"redirect_uri"`
	Scope       string `json:"scope"`
	ExpiresAt   int64  `json:"expires_at"`
}

type TokenData struct {
	UserID    int    `json:"user_id"`
	Scope     string `json:"scope"`
	ExpiresAt int64  `json:"expires_at"`
}

// In-memory storage for OAuth codes and tokens (should use Redis in production)
var (
	authCodes    = make(map[string]AuthCodeData)
	accessTokens = make(map[string]TokenData)
)

type PonderUser struct {
	ID          string `json:"id"`
	Username    string `json:"username"`
	DisplayName string `json:"display_name"`
	Email       string `json:"email"`
}

type PonderTokenResponse struct {
	AccessToken string     `json:"access_token"`
	TokenType   string     `json:"token_type"`
	ExpiresIn   int        `json:"expires_in"`
	Scope       string     `json:"scope"`
	User        PonderUser `json:"user"`
}

type PonderAuthRequest struct {
	Username    string `json:"username" binding:"required"`
	Password    string `json:"password" binding:"required"`
	ClientID    string `json:"client_id" binding:"required"`
	RedirectURI string `json:"redirect_uri" binding:"required"`
	State       string `json:"state" binding:"required"`
	Scope       string `json:"scope"`
}

type PonderTokenRequest struct {
	Code         string `json:"code" binding:"required"`
	ClientID     string `json:"client_id" binding:"required"`
	ClientSecret string `json:"client_secret"`
	RedirectURI  string `json:"redirect_uri" binding:"required"`
	GrantType    string `json:"grant_type" binding:"required"`
}

func getPonderUserInfoByCredentials(username, password string) (*model.User, error) {
	if username == "" || password == "" {
		return nil, errors.New("用户名或密码不能为空")
	}

	user := model.User{
		Username: username,
		Password: password,
	}

	// Use ValidateAndFill to check password and user status
	err := user.ValidateAndFill()
	if err != nil {
		return nil, err
	}

	return &user, nil
}

func PonderAuthorize(c *gin.Context) {
	var req PonderAuthRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// Validate client_id
	if req.ClientID != config.PonderClientId {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的客户端ID",
		})
		return
	}

	// Validate state (relaxed validation for internal OAuth)
	session := sessions.Default(c)
	storedState := session.Get("oauth_state")

	// If we have a stored state, validate it; otherwise, accept the provided state
	// This allows for both session-based and direct OAuth flows
	if storedState != nil && req.State != storedState.(string) {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的state参数",
		})
		return
	}

	// If no stored state, validate that state is not empty (basic security)
	if storedState == nil && req.State == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "state参数不能为空",
		})
		return
	}

	// Authenticate user
	user, err := getPonderUserInfoByCredentials(req.Username, req.Password)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	// Generate authorization code
	authCode := random.GetRandomString(32)

	// Store authorization code with user info and expiration in memory
	// In production, this should be stored in Redis with TTL
	codeData := AuthCodeData{
		UserID:      user.Id,
		ClientID:    req.ClientID,
		RedirectURI: req.RedirectURI,
		Scope:       req.Scope,
		ExpiresAt:   time.Now().Add(10 * time.Minute).Unix(),
	}

	authCodes[authCode] = codeData

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    authCode,
	})
}

func PonderToken(c *gin.Context) {
	var req PonderTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// Validate grant type
	if req.GrantType != "authorization_code" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "不支持的授权类型",
		})
		return
	}

	// Validate client
	if req.ClientID != config.PonderClientId {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的客户端ID",
		})
		return
	}

	// Get authorization code data from memory storage
	codeData, exists := authCodes[req.Code]
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的授权码",
		})
		return
	}

	// Check expiration
	if time.Now().Unix() > codeData.ExpiresAt {
		delete(authCodes, req.Code)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "授权码已过期",
		})
		return
	}

	// Validate redirect URI
	if req.RedirectURI != codeData.RedirectURI {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "重定向URI不匹配",
		})
		return
	}

	// Get user info
	userId := codeData.UserID
	user := model.User{Id: userId}
	err := user.FillUserById()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户信息失败",
		})
		return
	}

	// Generate access token
	accessToken := random.GetRandomString(64)
	scope := codeData.Scope

	// Store access token in memory storage
	tokenData := TokenData{
		UserID:    userId,
		Scope:     scope,
		ExpiresAt: time.Now().Add(24 * time.Hour).Unix(),
	}
	accessTokens[accessToken] = tokenData

	// Clean up authorization code
	delete(authCodes, req.Code)

	// Prepare response
	ponderUser := PonderUser{
		ID:          strconv.Itoa(user.Id),
		Username:    user.Username,
		DisplayName: user.DisplayName,
		Email:       user.Email,
	}

	tokenResponse := PonderTokenResponse{
		AccessToken: accessToken,
		TokenType:   "Bearer",
		ExpiresIn:   86400, // 24 hours
		Scope:       scope,
		User:        ponderUser,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    tokenResponse,
	})
}

func PonderUserInfo(c *gin.Context) {
	// Get access token from Authorization header
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "缺少Authorization头",
		})
		return
	}

	// Extract token from "Bearer <token>"
	if len(authHeader) < 7 || authHeader[:7] != "Bearer " {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "无效的Authorization头格式",
		})
		return
	}

	accessToken := authHeader[7:]

	// Validate access token from memory storage
	tokenData, exists := accessTokens[accessToken]
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "无效的访问令牌",
		})
		return
	}

	// Check expiration
	if time.Now().Unix() > tokenData.ExpiresAt {
		delete(accessTokens, accessToken)
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "访问令牌已过期",
		})
		return
	}

	// Get user info
	userId := tokenData.UserID
	user := model.User{Id: userId}
	err := user.FillUserById()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户信息失败",
		})
		return
	}

	ponderUser := PonderUser{
		ID:          strconv.Itoa(user.Id),
		Username:    user.Username,
		DisplayName: user.DisplayName,
		Email:       user.Email,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    ponderUser,
	})
}

func GeneratePonderOAuthState(c *gin.Context) {
	session := sessions.Default(c)
	state := random.GetRandomString(12)
	session.Set("oauth_state", state)
	err := session.Save()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "",
		"data":    state,
	})
}
