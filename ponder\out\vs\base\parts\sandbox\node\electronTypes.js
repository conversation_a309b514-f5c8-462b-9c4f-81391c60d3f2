/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
export function isUtilityProcess(process) {
    return !!process.parentPort;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZWxlY3Ryb25UeXBlcy5qcyIsInNvdXJjZVJvb3QiOiJmaWxlOi8vL0Q6L1Byb2plY3RzL3BvbmRlci1hbGwvcG9uZGVyL3NyYy8iLCJzb3VyY2VzIjpbInZzL2Jhc2UvcGFydHMvc2FuZGJveC9ub2RlL2VsZWN0cm9uVHlwZXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7OztnR0FHZ0c7QUF1RWhHLE1BQU0sVUFBVSxnQkFBZ0IsQ0FBQyxPQUF1QjtJQUN2RCxPQUFPLENBQUMsQ0FBRSxPQUFnQyxDQUFDLFVBQVUsQ0FBQztBQUN2RCxDQUFDIn0=