/*!
 * Microsoft Application Insights JavaScript SDK - Shim functions, 2.0.2
 * Copyright (c) Microsoft and contributors. All rights reserved.
 */
var e=this,n=function(e){"use strict";var l="function",r="object",n="undefined",o="prototype",i="hasOwnProperty",a="default",u=Object,f=u[o],s=u.assign,c=u.create,_=u.defineProperty,p=f[i],t=null;function d(e){e=!1===(e=void 0===e||e)?null:t;return e||((e=(e=(e=typeof globalThis!=n?globalThis:e)||typeof self==n?e:self)||typeof window==n?e:window)||typeof global==n||(e=global),t=e),e}function y(e){throw new TypeError(e)}function g(e){if(c)return c(e);if(null==e)return{};var n=typeof e;function t(){}return n!==r&&n!==l&&y("Object prototype may only be an Object:"+e),t[o]=e,new t}var v=(d()||{}).Symbol,b=(d()||{}).Reflect,h=!!b,m="decorate",O="metadata",w="getOwnPropertySymbols",j="iterator",F=s||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])f[i].call(n,o)&&(e[o]=n[o]);return e},x=function(e,n){return(x=u.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,n){e.__proto__=n}||function(e,n){for(var t in n)n[i](t)&&(e[t]=n[t])})(e,n)};function P(e,n){function t(){this.constructor=e}typeof n!==l&&null!==n&&y("Class extends value "+n+" is not a constructor or null"),x(e,n),e[o]=null===n?g(n):(t[o]=n[o],new t)}function S(e,n,t,r){r===undefined&&(r=t),c?_(e,r,{enumerable:!0,get:function(){return n[t]}}):e[r]=n[t]}e.ObjAssign=s,e.ObjClass=u,e.ObjCreate=c,e.ObjDefineProperty=_,e.ObjHasOwnProperty=p,e.ObjProto=f,e.__assignFn=F,e.__createBindingFn=S,e.__decorateFn=function(e,n,t,r){var o,i=arguments.length,a=i<3?n:null===r?r=u.getOwnPropertyDescriptor(n,t):r;if(h&&typeof b[m]===l)a=b[m](e,n,t,r);else for(var f=e.length-1;0<=f;f--)(o=e[f])&&(a=(i<3?o(a):3<i?o(n,t,a):o(n,t))||a);return 3<i&&a&&_(n,t,a),a},e.__exportStarFn=function(e,n){for(var t in e)t===a||p.call(n,t)||S(n,e,t)},e.__exposeGlobalTsLib=function(){var e,n=d()||{},t=P,r=S;(e=n).__assign||(e.__assign=s||F),e.__extends||(e.__extends=t),e.__createBinding||(e.__createBinding=r),__assign=__assign||n.__assign,__extends=__extends||n.__extends,__createBinding=__createBinding||n.__createBinding},e.__extendsFn=P,e.__importDefaultFn=function(e){return e&&e.__esModule?e:{strDefault:e}},e.__importStarFn=function(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var t in e)t!==a&&Object.prototype.hasOwnProperty.call(e,t)&&S(n,e,t);return c?_(n,a,{enumerable:!0,value:e}):n[a]=e,n},e.__makeTemplateObjectFn=function(e,n){return _?_(e,"raw",{value:n}):e.raw=n,e},e.__metadataFn=function(e,n){if(h&&b[O]===l)return b[O](e,n)},e.__paramFn=function(t,r){return function(e,n){r(e,n,t)}},e.__readFn=function(e,n){var t=typeof v===l&&e[v[j]];if(!t)return e;var r,o,i=t.call(e),a=[];try{for(;(void 0===n||0<n--)&&!(r=i.next()).done;)a.push(r.value)}catch(f){o={error:f}}finally{try{r&&!r.done&&(t=i["return"])&&t.call(i)}finally{if(o)throw o.error}}return a},e.__restFn=function(e,n){var t,r={};for(t in e)p.call(e,t)&&!~n.indexOf(t)&&(r[t]=e[t]);if(null!=e&&typeof u[w]===l)for(var o=0,i=u[w](e);o<i.length;o++)!~n.indexOf(i[o])&&f.propertyIsEnumerable.call(e,i[o])&&(r[i[o]]=e[i[o]]);return r},e.__spreadArrayFn=function(e,n){for(var t=0,r=n.length,o=e.length;t<r;t++,o++)e[o]=n[t];return e},e.__spreadArraysFn=function(){for(var e=arguments,n=0,t=0,r=e.length;t<r;t++)n+=e[t].length;for(var o=Array(n),i=0,t=0;t<r;t++)for(var a=e[t],f=0,l=a.length;f<l;f++,i++)o[i]=a[f];return o},e.__valuesFn=function(e){var n=typeof v===l&&v[j],t=n&&e[n],r=0;return t?t.call(e):e&&"number"==typeof e.length?{next:function(){return{value:(e=e&&r>=e.length?void 0:e)&&e[r++],done:!e}}}:void y(n?"Object is not iterable.":"Symbol.iterator is not defined.")},e.getGlobal=d,e.objCreateFn=g,e.strDefault=a,e.strShimFunction=l,e.strShimHasOwnProperty=i,e.strShimObject=r,e.strShimPrototype=o,e.strShimUndefined=n,e.throwTypeError=y;var A="__esModule",M={value:!0},T=Object.defineProperty;if(T)try{return void T(e,A,M)}catch(B){}typeof M.value!==undefined&&(e[A]=M.value)};"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n(((e="undefined"!=typeof globalThis?globalThis:e||self).Microsoft=e.Microsoft||{},e.Microsoft.ApplicationInsights=e.Microsoft.ApplicationInsights||{},e.Microsoft.ApplicationInsights.Shims={}));
