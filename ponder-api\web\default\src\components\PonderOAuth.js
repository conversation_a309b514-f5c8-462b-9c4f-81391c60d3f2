import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Segment, Dimmer, Loader, Form, Button, Message, Header, Container, Grid, Card } from 'semantic-ui-react';
import { API } from '../helpers';
import { showError, showSuccess } from '../helpers';

const PonderOAuth = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [prompt, setPrompt] = useState('');
  const [showLoginForm, setShowLoginForm] = useState(true);
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [currentState, setCurrentState] = useState('');

  // OAuth parameters from URL
  const clientId = searchParams.get('client_id');
  const redirectUri = searchParams.get('redirect_uri');
  const originalState = searchParams.get('state');
  const scope = searchParams.get('scope');

  useEffect(() => {
    // Validate OAuth parameters
    if (!clientId || !redirectUri || !originalState) {
      setPrompt('无效的OAuth参数');
      setShowLoginForm(false);
      return;
    }

    if (clientId !== 'ponder-client') {
      setPrompt('无效的客户端ID');
      setShowLoginForm(false);
      return;
    }

    // Get a fresh state from the server to ensure session consistency
    API.get('/api/oauth/ponder/state')
      .then(response => {
        if (response.data.success) {
          setCurrentState(response.data.data);
          setPrompt('请登录以授权Ponder访问您的账户');
        } else {
          setPrompt('获取授权状态失败');
          setShowLoginForm(false);
        }
      })
      .catch(error => {
        console.error('Failed to get OAuth state:', error);
        setPrompt('获取授权状态失败');
        setShowLoginForm(false);
      });
  }, [clientId, redirectUri, originalState]);

  const handleInputChange = (e, { name, value }) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.username || !formData.password) {
      showError('请输入用户名和密码');
      return;
    }

    setLoading(true);
    setPrompt('正在验证...');

    try {
      const response = await API.post('/api/oauth/ponder/authorize', {
        username: formData.username,
        password: formData.password,
        client_id: clientId,
        redirect_uri: redirectUri,
        state: currentState,
        scope: scope || 'read write'
      });

      if (response.data.success) {
        const authCode = response.data.code;
        setPrompt('授权成功，正在跳转...');
        
        // Redirect to callback URL with authorization code
        const callbackUrl = new URL(redirectUri);
        callbackUrl.searchParams.set('code', authCode);
        callbackUrl.searchParams.set('state', originalState); // Use original state for callback
        
        window.location.href = callbackUrl.toString();
      } else {
        showError(response.data.message || '授权失败');
        setLoading(false);
        setPrompt('请登录以授权Ponder访问您的账户');
      }
    } catch (error) {
      console.error('Authorization error:', error);
      showError('授权过程中发生错误，请重试');
      setLoading(false);
      setPrompt('请登录以授权Ponder访问您的账户');
    }
  };

  const handleCancel = () => {
    // Redirect back with error
    if (redirectUri) {
      const callbackUrl = new URL(redirectUri);
      callbackUrl.searchParams.set('error', 'access_denied');
      callbackUrl.searchParams.set('state', originalState || '');
      window.location.href = callbackUrl.toString();
    } else {
      navigate('/login');
    }
  };

  if (!showLoginForm) {
    return (
      <Container style={{ marginTop: '2em' }}>
        <Grid centered>
          <Grid.Column style={{ maxWidth: 450 }}>
            <Card fluid>
              <Card.Content>
                <Header as='h2' textAlign='center' color='red'>
                  授权失败
                </Header>
                <Message error>
                  <Message.Header>错误</Message.Header>
                  <p>{prompt}</p>
                </Message>
                <Button 
                  fluid 
                  color='blue' 
                  onClick={() => navigate('/login')}
                >
                  返回登录页面
                </Button>
              </Card.Content>
            </Card>
          </Grid.Column>
        </Grid>
      </Container>
    );
  }

  return (
    <Container style={{ marginTop: '2em' }}>
      <Grid centered>
        <Grid.Column style={{ maxWidth: 450 }}>
          <Card fluid>
            <Card.Content>
              <Header as='h2' textAlign='center' color='blue'>
                Ponder 授权
              </Header>
              
              <Message info>
                <Message.Header>授权请求</Message.Header>
                <p>Ponder 客户端请求访问您的账户。请登录以确认授权。</p>
                {scope && (
                  <p><strong>请求权限:</strong> {scope}</p>
                )}
              </Message>

              <Form onSubmit={handleSubmit} loading={loading}>
                <Form.Input
                  fluid
                  icon='user'
                  iconPosition='left'
                  placeholder='用户名'
                  name='username'
                  value={formData.username}
                  onChange={handleInputChange}
                  required
                />
                <Form.Input
                  fluid
                  icon='lock'
                  iconPosition='left'
                  placeholder='密码'
                  type='password'
                  name='password'
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                />

                <Grid columns={2}>
                  <Grid.Column>
                    <Button 
                      type='button'
                      fluid
                      onClick={handleCancel}
                      disabled={loading}
                    >
                      取消
                    </Button>
                  </Grid.Column>
                  <Grid.Column>
                    <Button 
                      color='blue' 
                      fluid 
                      type='submit'
                      disabled={loading}
                    >
                      授权
                    </Button>
                  </Grid.Column>
                </Grid>
              </Form>

              {prompt && (
                <Message style={{ marginTop: '1em' }}>
                  {prompt}
                </Message>
              )}
            </Card.Content>
          </Card>
        </Grid.Column>
      </Grid>
      
      {loading && (
        <Dimmer active>
          <Loader size='large'>处理中...</Loader>
        </Dimmer>
      )}
    </Container>
  );
};

export default PonderOAuth;
