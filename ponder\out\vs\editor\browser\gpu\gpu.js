/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
export var BindingId;
(function (BindingId) {
    BindingId[BindingId["GlyphInfo"] = 0] = "GlyphInfo";
    BindingId[BindingId["Cells"] = 1] = "Cells";
    BindingId[BindingId["TextureSampler"] = 2] = "TextureSampler";
    BindingId[BindingId["Texture"] = 3] = "Texture";
    BindingId[BindingId["LayoutInfoUniform"] = 4] = "LayoutInfoUniform";
    BindingId[BindingId["AtlasDimensionsUniform"] = 5] = "AtlasDimensionsUniform";
    BindingId[BindingId["ScrollOffset"] = 6] = "ScrollOffset";
})(BindingId || (BindingId = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZ3B1LmpzIiwic291cmNlUm9vdCI6ImZpbGU6Ly8vRDovUHJvamVjdHMvcG9uZGVyLWFsbC9wb25kZXIvc3JjLyIsInNvdXJjZXMiOlsidnMvZWRpdG9yL2Jyb3dzZXIvZ3B1L2dwdS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7O2dHQUdnRztBQVFoRyxNQUFNLENBQU4sSUFBa0IsU0FRakI7QUFSRCxXQUFrQixTQUFTO0lBQzFCLG1EQUFTLENBQUE7SUFDVCwyQ0FBSyxDQUFBO0lBQ0wsNkRBQWMsQ0FBQTtJQUNkLCtDQUFPLENBQUE7SUFDUCxtRUFBaUIsQ0FBQTtJQUNqQiw2RUFBc0IsQ0FBQTtJQUN0Qix5REFBWSxDQUFBO0FBQ2IsQ0FBQyxFQVJpQixTQUFTLEtBQVQsU0FBUyxRQVExQiJ9