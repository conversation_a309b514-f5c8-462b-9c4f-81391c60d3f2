{"name": "@vscode/windows-ca-certs", "version": "0.3.3", "description": "Get Windows System Root certificates", "os": ["win32"], "gypfile": true, "main": "build/Release/crypt32", "files": ["binding.gyp", "crypt32.cc"], "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/vscode-windows-ca-certs.git"}, "keywords": ["ssl", "tls", "ca", "root", "windows"], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Microsoft Corporation"}], "license": "BSD", "bugs": {"url": "https://github.com/microsoft/vscode-windows-ca-certs/issues"}, "homepage": "https://github.com/microsoft/vscode-windows-ca-certs", "dependencies": {"node-addon-api": "^8.2.0"}, "devDependencies": {"bindings": "1.5.0", "node-forge": "^1.3.0"}}