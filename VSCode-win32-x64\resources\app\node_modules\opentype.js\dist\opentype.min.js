!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(e.opentype=e.opentype||{})}(this,function(E){"use strict";var l=0,o=-3;function s(){this.table=new Uint16Array(16),this.trans=new Uint16Array(288)}var i=new s,u=new s,p=new Uint8Array(30),c=new Uint16Array(30),h=new Uint8Array(30),f=new Uint16Array(30),d=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),v=new s,g=new Uint8Array(320);function e(e,t,r,a){var n,o;for(n=0;n<r;++n)e[n]=0;for(n=0;n<30-r;++n)e[n+r]=n/r|0;for(o=a,n=0;n<30;++n)t[n]=o,o+=1<<e[n]}var m=new Uint16Array(16);function y(e,t,r,a){var n,o;for(n=0;n<16;++n)e.table[n]=0;for(n=0;n<a;++n)e.table[t[r+n]]++;for(n=o=e.table[0]=0;n<16;++n)m[n]=o,o+=e.table[n];for(n=0;n<a;++n)t[r+n]&&(e.trans[m[t[r+n]]++]=n)}function b(e){e.bitcount--||(e.tag=e.source[e.sourceIndex++],e.bitcount=7);var t=1&e.tag;return e.tag>>>=1,t}function S(e,t,r){if(!t)return r;for(;e.bitcount<24;)e.tag|=e.source[e.sourceIndex++]<<e.bitcount,e.bitcount+=8;var a=e.tag&65535>>>16-t;return e.tag>>>=t,e.bitcount-=t,a+r}function x(e,t){for(;e.bitcount<24;)e.tag|=e.source[e.sourceIndex++]<<e.bitcount,e.bitcount+=8;for(var r=0,a=0,n=0,o=e.tag;a=2*a+(1&o),o>>>=1,++n,r+=t.table[n],0<=(a-=t.table[n]););return e.tag=o,e.bitcount-=n,t.trans[r+a]}function U(e,t,r){var a,n,o,s,i,l;for(a=S(e,5,257),n=S(e,5,1),o=S(e,4,4),s=0;s<19;++s)g[s]=0;for(s=0;s<o;++s){var u=S(e,3,0);g[d[s]]=u}for(y(v,g,0,19),i=0;i<a+n;){var p=x(e,v);switch(p){case 16:var c=g[i-1];for(l=S(e,2,3);l;--l)g[i++]=c;break;case 17:for(l=S(e,3,3);l;--l)g[i++]=0;break;case 18:for(l=S(e,7,11);l;--l)g[i++]=0;break;default:g[i++]=p}}y(t,g,0,a),y(r,g,a,n)}function T(e,t,r){for(;;){var a,n,o,s,i=x(e,t);if(256===i)return l;if(i<256)e.dest[e.destLen++]=i;else for(a=S(e,p[i-=257],c[i]),n=x(e,r),s=o=e.destLen-S(e,h[n],f[n]);s<o+a;++s)e.dest[e.destLen++]=e.dest[s]}}function O(e){for(var t,r;8<e.bitcount;)e.sourceIndex--,e.bitcount-=8;if((t=256*(t=e.source[e.sourceIndex+1])+e.source[e.sourceIndex])!==(65535&~(256*e.source[e.sourceIndex+3]+e.source[e.sourceIndex+2])))return o;for(e.sourceIndex+=4,r=t;r;--r)e.dest[e.destLen++]=e.source[e.sourceIndex++];return e.bitcount=0,l}!function(e,t){var r;for(r=0;r<7;++r)e.table[r]=0;for(e.table[7]=24,e.table[8]=152,e.table[9]=112,r=0;r<24;++r)e.trans[r]=256+r;for(r=0;r<144;++r)e.trans[24+r]=r;for(r=0;r<8;++r)e.trans[168+r]=280+r;for(r=0;r<112;++r)e.trans[176+r]=144+r;for(r=0;r<5;++r)t.table[r]=0;for(t.table[5]=32,r=0;r<32;++r)t.trans[r]=r}(i,u),e(p,c,4,3),e(h,f,2,1),p[28]=0,c[28]=258;var n=function(e,t){var r,a,n=new function(e,t){this.source=e,this.sourceIndex=0,this.tag=0,this.bitcount=0,this.dest=t,this.destLen=0,this.ltree=new s,this.dtree=new s}(e,t);do{switch(r=b(n),S(n,2,0)){case 0:a=O(n);break;case 1:a=T(n,i,u);break;case 2:U(n,n.ltree,n.dtree),a=T(n,n.ltree,n.dtree);break;default:a=o}if(a!==l)throw new Error("Data error")}while(!r);return n.destLen<n.dest.length?"function"==typeof n.dest.slice?n.dest.slice(0,n.destLen):n.dest.subarray(0,n.destLen):n.dest};function k(e,t,r,a,n){return Math.pow(1-n,3)*e+3*Math.pow(1-n,2)*n*t+3*(1-n)*Math.pow(n,2)*r+Math.pow(n,3)*a}function R(){this.x1=Number.NaN,this.y1=Number.NaN,this.x2=Number.NaN,this.y2=Number.NaN}function M(){this.commands=[],this.fill="black",this.stroke=null,this.strokeWidth=1}function r(e){throw new Error(e)}function t(e,t){e||r(t)}R.prototype.isEmpty=function(){return isNaN(this.x1)||isNaN(this.y1)||isNaN(this.x2)||isNaN(this.y2)},R.prototype.addPoint=function(e,t){"number"==typeof e&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=e,this.x2=e),e<this.x1&&(this.x1=e),e>this.x2&&(this.x2=e)),"number"==typeof t&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=t,this.y2=t),t<this.y1&&(this.y1=t),t>this.y2&&(this.y2=t))},R.prototype.addX=function(e){this.addPoint(e,null)},R.prototype.addY=function(e){this.addPoint(null,e)},R.prototype.addBezier=function(e,t,r,a,n,o,s,i){var l=this,u=[e,t],p=[r,a],c=[n,o],h=[s,i];this.addPoint(e,t),this.addPoint(s,i);for(var f=0;f<=1;f++){var d=6*u[f]-12*p[f]+6*c[f],v=-3*u[f]+9*p[f]-9*c[f]+3*h[f],g=3*p[f]-3*u[f];if(0!==v){var m=Math.pow(d,2)-4*g*v;if(!(m<0)){var y=(-d+Math.sqrt(m))/(2*v);0<y&&y<1&&(0===f&&l.addX(k(u[f],p[f],c[f],h[f],y)),1===f&&l.addY(k(u[f],p[f],c[f],h[f],y)));var b=(-d-Math.sqrt(m))/(2*v);0<b&&b<1&&(0===f&&l.addX(k(u[f],p[f],c[f],h[f],b)),1===f&&l.addY(k(u[f],p[f],c[f],h[f],b)))}}else{if(0===d)continue;var S=-g/d;0<S&&S<1&&(0===f&&l.addX(k(u[f],p[f],c[f],h[f],S)),1===f&&l.addY(k(u[f],p[f],c[f],h[f],S)))}}},R.prototype.addQuad=function(e,t,r,a,n,o){var s=e+2/3*(r-e),i=t+2/3*(a-t),l=s+1/3*(n-e),u=i+1/3*(o-t);this.addBezier(e,t,s,i,l,u,n,o)},M.prototype.moveTo=function(e,t){this.commands.push({type:"M",x:e,y:t})},M.prototype.lineTo=function(e,t){this.commands.push({type:"L",x:e,y:t})},M.prototype.curveTo=M.prototype.bezierCurveTo=function(e,t,r,a,n,o){this.commands.push({type:"C",x1:e,y1:t,x2:r,y2:a,x:n,y:o})},M.prototype.quadTo=M.prototype.quadraticCurveTo=function(e,t,r,a){this.commands.push({type:"Q",x1:e,y1:t,x:r,y:a})},M.prototype.close=M.prototype.closePath=function(){this.commands.push({type:"Z"})},M.prototype.extend=function(e){if(e.commands)e=e.commands;else if(e instanceof R){var t=e;return this.moveTo(t.x1,t.y1),this.lineTo(t.x2,t.y1),this.lineTo(t.x2,t.y2),this.lineTo(t.x1,t.y2),void this.close()}Array.prototype.push.apply(this.commands,e)},M.prototype.getBoundingBox=function(){for(var e=new R,t=0,r=0,a=0,n=0,o=0;o<this.commands.length;o++){var s=this.commands[o];switch(s.type){case"M":e.addPoint(s.x,s.y),t=a=s.x,r=n=s.y;break;case"L":e.addPoint(s.x,s.y),a=s.x,n=s.y;break;case"Q":e.addQuad(a,n,s.x1,s.y1,s.x,s.y),a=s.x,n=s.y;break;case"C":e.addBezier(a,n,s.x1,s.y1,s.x2,s.y2,s.x,s.y),a=s.x,n=s.y;break;case"Z":a=t,n=r;break;default:throw new Error("Unexpected path command "+s.type)}}return e.isEmpty()&&e.addPoint(0,0),e},M.prototype.draw=function(e){e.beginPath();for(var t=0;t<this.commands.length;t+=1){var r=this.commands[t];"M"===r.type?e.moveTo(r.x,r.y):"L"===r.type?e.lineTo(r.x,r.y):"C"===r.type?e.bezierCurveTo(r.x1,r.y1,r.x2,r.y2,r.x,r.y):"Q"===r.type?e.quadraticCurveTo(r.x1,r.y1,r.x,r.y):"Z"===r.type&&e.closePath()}this.fill&&(e.fillStyle=this.fill,e.fill()),this.stroke&&(e.strokeStyle=this.stroke,e.lineWidth=this.strokeWidth,e.stroke())},M.prototype.toPathData=function(o){function e(){for(var e,t=arguments,r="",a=0;a<arguments.length;a+=1){var n=t[a];0<=n&&0<a&&(r+=" "),r+=(e=n,Math.round(e)===e?""+Math.round(e):e.toFixed(o))}return r}o=void 0!==o?o:2;for(var t="",r=0;r<this.commands.length;r+=1){var a=this.commands[r];"M"===a.type?t+="M"+e(a.x,a.y):"L"===a.type?t+="L"+e(a.x,a.y):"C"===a.type?t+="C"+e(a.x1,a.y1,a.x2,a.y2,a.x,a.y):"Q"===a.type?t+="Q"+e(a.x1,a.y1,a.x,a.y):"Z"===a.type&&(t+="Z")}return t},M.prototype.toSVG=function(e){var t='<path d="';return t+=this.toPathData(e),t+='"',this.fill&&"black"!==this.fill&&(null===this.fill?t+=' fill="none"':t+=' fill="'+this.fill+'"'),this.stroke&&(t+=' stroke="'+this.stroke+'" stroke-width="'+this.strokeWidth+'"'),t+="/>"},M.prototype.toDOMElement=function(e){var t=this.toPathData(e),r=document.createElementNS("http://www.w3.org/2000/svg","path");return r.setAttribute("d",t),r};var L={fail:r,argument:t,assert:t},a=2147483648,D={},w={},C={};function G(e){return function(){return e}}w.BYTE=function(e){return L.argument(0<=e&&e<=255,"Byte value should be between 0 and 255."),[e]},C.BYTE=G(1),w.CHAR=function(e){return[e.charCodeAt(0)]},C.CHAR=G(1),w.CHARARRAY=function(e){for(var t=[],r=0;r<e.length;r+=1)t[r]=e.charCodeAt(r);return t},C.CHARARRAY=function(e){return e.length},w.USHORT=function(e){return[e>>8&255,255&e]},C.USHORT=G(2),w.SHORT=function(e){return 32768<=e&&(e=-(65536-e)),[e>>8&255,255&e]},C.SHORT=G(2),w.UINT24=function(e){return[e>>16&255,e>>8&255,255&e]},C.UINT24=G(3),w.ULONG=function(e){return[e>>24&255,e>>16&255,e>>8&255,255&e]},C.ULONG=G(4),w.LONG=function(e){return a<=e&&(e=-(2*a-e)),[e>>24&255,e>>16&255,e>>8&255,255&e]},C.LONG=G(4),w.FIXED=w.ULONG,C.FIXED=C.ULONG,w.FWORD=w.SHORT,C.FWORD=C.SHORT,w.UFWORD=w.USHORT,C.UFWORD=C.USHORT,w.LONGDATETIME=function(e){return[0,0,0,0,e>>24&255,e>>16&255,e>>8&255,255&e]},C.LONGDATETIME=G(8),w.TAG=function(e){return L.argument(4===e.length,"Tag should be exactly 4 ASCII characters."),[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]},C.TAG=G(4),w.Card8=w.BYTE,C.Card8=C.BYTE,w.Card16=w.USHORT,C.Card16=C.USHORT,w.OffSize=w.BYTE,C.OffSize=C.BYTE,w.SID=w.USHORT,C.SID=C.USHORT,w.NUMBER=function(e){return-107<=e&&e<=107?[e+139]:108<=e&&e<=1131?[247+((e-=108)>>8),255&e]:-1131<=e&&e<=-108?[251+((e=-e-108)>>8),255&e]:-32768<=e&&e<=32767?w.NUMBER16(e):w.NUMBER32(e)},C.NUMBER=function(e){return w.NUMBER(e).length},w.NUMBER16=function(e){return[28,e>>8&255,255&e]},C.NUMBER16=G(3),w.NUMBER32=function(e){return[29,e>>24&255,e>>16&255,e>>8&255,255&e]},C.NUMBER32=G(5),w.REAL=function(e){var t=e.toString(),r=/\.(\d*?)(?:9{5,20}|0{5,20})\d{0,2}(?:e(.+)|$)/.exec(t);if(r){var a=parseFloat("1e"+((r[2]?+r[2]:0)+r[1].length));t=(Math.round(e*a)/a).toString()}for(var n="",o=0,s=t.length;o<s;o+=1){var i=t[o];n+="e"===i?"-"===t[++o]?"c":"b":"."===i?"a":"-"===i?"e":i}for(var l=[30],u=0,p=(n+=1&n.length?"f":"ff").length;u<p;u+=2)l.push(parseInt(n.substr(u,2),16));return l},C.REAL=function(e){return w.REAL(e).length},w.NAME=w.CHARARRAY,C.NAME=C.CHARARRAY,w.STRING=w.CHARARRAY,C.STRING=C.CHARARRAY,D.UTF8=function(e,t,r){for(var a=[],n=r,o=0;o<n;o++,t+=1)a[o]=e.getUint8(t);return String.fromCharCode.apply(null,a)},D.UTF16=function(e,t,r){for(var a=[],n=r/2,o=0;o<n;o++,t+=2)a[o]=e.getUint16(t);return String.fromCharCode.apply(null,a)},w.UTF16=function(e){for(var t=[],r=0;r<e.length;r+=1){var a=e.charCodeAt(r);t[t.length]=a>>8&255,t[t.length]=255&a}return t},C.UTF16=function(e){return 2*e.length};var I={"x-mac-croatian":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®Š™´¨≠ŽØ∞±≤≥∆µ∂∑∏š∫ªºΩžø¿¡¬√ƒ≈Ć«Č… ÀÃÕŒœĐ—“”‘’÷◊©⁄€‹›Æ»–·‚„‰ÂćÁčÈÍÎÏÌÓÔđÒÚÛÙıˆ˜¯πË˚¸Êæˇ","x-mac-cyrillic":"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ†°Ґ£§•¶І®©™Ђђ≠Ѓѓ∞±≤≥іµґЈЄєЇїЉљЊњјЅ¬√ƒ≈∆«»… ЋћЌќѕ–—“”‘’÷„ЎўЏџ№Ёёяабвгдежзийклмнопрстуфхцчшщъыьэю","x-mac-gaelic":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØḂ±≤≥ḃĊċḊḋḞḟĠġṀæøṁṖṗɼƒſṠ«»… ÀÃÕŒœ–—“”‘’ṡẛÿŸṪ€‹›Ŷŷṫ·Ỳỳ⁊ÂÊÁËÈÍÎÏÌÓÔ♣ÒÚÛÙıÝýŴŵẄẅẀẁẂẃ","x-mac-greek":"Ä¹²É³ÖÜ΅àâä΄¨çéèêë£™îï•½‰ôö¦€ùûü†ΓΔΘΛΞΠß®©ΣΪ§≠°·Α±≤≥¥ΒΕΖΗΙΚΜΦΫΨΩάΝ¬ΟΡ≈Τ«»… ΥΧΆΈœ–―“”‘’÷ΉΊΌΎέήίόΏύαβψδεφγηιξκλμνοπώρστθωςχυζϊϋΐΰ­","x-mac-icelandic":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûüÝ°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€ÐðÞþý·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ","x-mac-inuit":"ᐃᐄᐅᐆᐊᐋᐱᐲᐳᐴᐸᐹᑉᑎᑏᑐᑑᑕᑖᑦᑭᑮᑯᑰᑲᑳᒃᒋᒌᒍᒎᒐᒑ°ᒡᒥᒦ•¶ᒧ®©™ᒨᒪᒫᒻᓂᓃᓄᓅᓇᓈᓐᓯᓰᓱᓲᓴᓵᔅᓕᓖᓗᓘᓚᓛᓪᔨᔩᔪᔫᔭ… ᔮᔾᕕᕖᕗ–—“”‘’ᕘᕙᕚᕝᕆᕇᕈᕉᕋᕌᕐᕿᖀᖁᖂᖃᖄᖅᖏᖐᖑᖒᖓᖔᖕᙱᙲᙳᙴᙵᙶᖖᖠᖡᖢᖣᖤᖥᖦᕼŁł","x-mac-ce":"ÄĀāÉĄÖÜáąČäčĆćéŹźĎíďĒēĖóėôöõúĚěü†°Ę£§•¶ß®©™ę¨≠ģĮįĪ≤≥īĶ∂∑łĻļĽľĹĺŅņŃ¬√ńŇ∆«»… ňŐÕőŌ–—“”‘’÷◊ōŔŕŘ‹›řŖŗŠ‚„šŚśÁŤťÍŽžŪÓÔūŮÚůŰűŲųÝýķŻŁżĢˇ",macintosh:"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€‹›ﬁﬂ‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ","x-mac-romanian":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ĂȘ∞±≤≥¥µ∂∑∏π∫ªºΩăș¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸ⁄€‹›Țț‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙıˆ˜¯˘˙˚¸˝˛ˇ","x-mac-turkish":"ÄÅÇÉÑÖÜáàâäãåçéèêëíìîïñóòôöõúùûü†°¢£§•¶ß®©™´¨≠ÆØ∞±≤≥¥µ∂∑∏π∫ªºΩæø¿¡¬√ƒ≈∆«»… ÀÃÕŒœ–—“”‘’÷◊ÿŸĞğİıŞş‡·‚„‰ÂÊÁËÈÍÎÏÌÓÔÒÚÛÙˆ˜¯˘˙˚¸˝˛ˇ"};D.MACSTRING=function(e,t,r,a){var n=I[a];if(void 0!==n){for(var o="",s=0;s<r;s++){var i=e.getUint8(t+s);o+=i<=127?String.fromCharCode(i):n[127&i]}return o}};var B,N="function"==typeof WeakMap&&new WeakMap;function P(e){return-128<=e&&e<=127}function A(e,t,r){for(var a=0,n=e.length;t<n&&a<64&&0===e[t];)++t,++a;return r.push(128|a-1),t}function F(e,t,r){for(var a=0,n=e.length,o=t;o<n&&a<64;){var s=e[o];if(!P(s))break;if(0===s&&o+1<n&&0===e[o+1])break;++o,++a}r.push(a-1);for(var i=t;i<o;++i)r.push(e[i]+256&255);return o}function H(e,t,r){for(var a=0,n=e.length,o=t;o<n&&a<64;){var s=e[o];if(0===s)break;if(P(s)&&o+1<n&&P(e[o+1]))break;++o,++a}r.push(64|a-1);for(var i=t;i<o;++i){var l=e[i];r.push(l+65536>>8&255,l+256&255)}return o}w.MACSTRING=function(e,t){var r=function(e){if(!B)for(var t in B={},I)B[t]=new String(t);var r=B[e];if(void 0!==r){if(N){var a=N.get(r);if(void 0!==a)return a}var n=I[e];if(void 0!==n){for(var o={},s=0;s<n.length;s++)o[n.charCodeAt(s)]=s+128;return N&&N.set(r,o),o}}}(t);if(void 0!==r){for(var a=[],n=0;n<e.length;n++){var o=e.charCodeAt(n);if(128<=o&&void 0===(o=r[o]))return;a[n]=o}return a}},C.MACSTRING=function(e,t){var r=w.MACSTRING(e,t);return void 0!==r?r.length:0},w.VARDELTAS=function(e){for(var t=0,r=[];t<e.length;){var a=e[t];t=0===a?A(e,t,r):-128<=a&&a<=127?F(e,t,r):H(e,t,r)}return r},w.INDEX=function(e){for(var t=1,r=[t],a=[],n=0;n<e.length;n+=1){var o=w.OBJECT(e[n]);Array.prototype.push.apply(a,o),t+=o.length,r.push(t)}if(0===a.length)return[0,0];for(var s=[],i=1+Math.floor(Math.log(t)/Math.log(2))/8|0,l=[void 0,w.BYTE,w.USHORT,w.UINT24,w.ULONG][i],u=0;u<r.length;u+=1){var p=l(r[u]);Array.prototype.push.apply(s,p)}return Array.prototype.concat(w.Card16(e.length),w.OffSize(i),s,a)},C.INDEX=function(e){return w.INDEX(e).length},w.DICT=function(e){for(var t=[],r=Object.keys(e),a=r.length,n=0;n<a;n+=1){var o=parseInt(r[n],0),s=e[o];t=(t=t.concat(w.OPERAND(s.value,s.type))).concat(w.OPERATOR(o))}return t},C.DICT=function(e){return w.DICT(e).length},w.OPERATOR=function(e){return e<1200?[e]:[12,e-1200]},w.OPERAND=function(e,t){var r=[];if(Array.isArray(t))for(var a=0;a<t.length;a+=1)L.argument(e.length===t.length,"Not enough arguments given for type"+t),r=r.concat(w.OPERAND(e[a],t[a]));else if("SID"===t)r=r.concat(w.NUMBER(e));else if("offset"===t)r=r.concat(w.NUMBER32(e));else if("number"===t)r=r.concat(w.NUMBER(e));else{if("real"!==t)throw new Error("Unknown operand type "+t);r=r.concat(w.REAL(e))}return r},w.OP=w.BYTE,C.OP=C.BYTE;var z="function"==typeof WeakMap&&new WeakMap;function W(e,t,r){for(var a=0;a<t.length;a+=1){var n=t[a];this[n.name]=n.value}if(this.tableName=e,this.fields=t,r)for(var o=Object.keys(r),s=0;s<o.length;s+=1){var i=o[s],l=r[i];void 0!==this[i]&&(this[i]=l)}}function q(e,t,r){void 0===r&&(r=t.length);var a=new Array(t.length+1);a[0]={name:e+"Count",type:"USHORT",value:r};for(var n=0;n<t.length;n++)a[n+1]={name:e+n,type:"USHORT",value:t[n]};return a}function X(e,t,r){var a=t.length,n=new Array(a+1);n[0]={name:e+"Count",type:"USHORT",value:a};for(var o=0;o<a;o++)n[o+1]={name:e+o,type:"TABLE",value:r(t[o],o)};return n}function _(e,t,r){var a=t.length,n=[];n[0]={name:e+"Count",type:"USHORT",value:a};for(var o=0;o<a;o++)n=n.concat(r(t[o],o));return n}function V(e){1===e.format?W.call(this,"coverageTable",[{name:"coverageFormat",type:"USHORT",value:1}].concat(q("glyph",e.glyphs))):L.assert(!1,"Can't create coverage table format 2 yet.")}function Y(e){W.call(this,"scriptListTable",_("scriptRecord",e,function(e,t){var r=e.script,a=r.defaultLangSys;return L.assert(!!a,"Unable to write GSUB: script "+e.tag+" has no default language system."),[{name:"scriptTag"+t,type:"TAG",value:e.tag},{name:"script"+t,type:"TABLE",value:new W("scriptTable",[{name:"defaultLangSys",type:"TABLE",value:new W("defaultLangSys",[{name:"lookupOrder",type:"USHORT",value:0},{name:"reqFeatureIndex",type:"USHORT",value:a.reqFeatureIndex}].concat(q("featureIndex",a.featureIndexes)))}].concat(_("langSys",r.langSysRecords,function(e,t){var r=e.langSys;return[{name:"langSysTag"+t,type:"TAG",value:e.tag},{name:"langSys"+t,type:"TABLE",value:new W("langSys",[{name:"lookupOrder",type:"USHORT",value:0},{name:"reqFeatureIndex",type:"USHORT",value:r.reqFeatureIndex}].concat(q("featureIndex",r.featureIndexes)))}]})))}]}))}function j(e){W.call(this,"featureListTable",_("featureRecord",e,function(e,t){var r=e.feature;return[{name:"featureTag"+t,type:"TAG",value:e.tag},{name:"feature"+t,type:"TABLE",value:new W("featureTable",[{name:"featureParams",type:"USHORT",value:r.featureParams}].concat(q("lookupListIndex",r.lookupListIndexes)))}]}))}function Z(e,r){W.call(this,"lookupListTable",X("lookup",e,function(e){var t=r[e.lookupType];return L.assert(!!t,"Unable to write GSUB lookup type "+e.lookupType+" tables."),new W("lookupTable",[{name:"lookupType",type:"USHORT",value:e.lookupType},{name:"lookupFlag",type:"USHORT",value:e.lookupFlag}].concat(X("subtable",e.subtables,t)))}))}w.CHARSTRING=function(e){if(z){var t=z.get(e);if(void 0!==t)return t}for(var r=[],a=e.length,n=0;n<a;n+=1){var o=e[n];r=r.concat(w[o.type](o.value))}return z&&z.set(e,r),r},C.CHARSTRING=function(e){return w.CHARSTRING(e).length},w.OBJECT=function(e){var t=w[e.type];return L.argument(void 0!==t,"No encoding function for type "+e.type),t(e.value)},C.OBJECT=function(e){var t=C[e.type];return L.argument(void 0!==t,"No sizeOf function for type "+e.type),t(e.value)},w.TABLE=function(e){for(var t=[],r=e.fields.length,a=[],n=[],o=0;o<r;o+=1){var s=e.fields[o],i=w[s.type];L.argument(void 0!==i,"No encoding function for field type "+s.type+" ("+s.name+")");var l=e[s.name];void 0===l&&(l=s.value);var u=i(l);"TABLE"===s.type?(n.push(t.length),t=t.concat([0,0]),a.push(u)):t=t.concat(u)}for(var p=0;p<a.length;p+=1){var c=n[p],h=t.length;L.argument(h<65536,"Table "+e.tableName+" too big."),t[c]=h>>8,t[c+1]=255&h,t=t.concat(a[p])}return t},C.TABLE=function(e){for(var t=0,r=e.fields.length,a=0;a<r;a+=1){var n=e.fields[a],o=C[n.type];L.argument(void 0!==o,"No sizeOf function for field type "+n.type+" ("+n.name+")");var s=e[n.name];void 0===s&&(s=n.value),t+=o(s),"TABLE"===n.type&&(t+=2)}return t},w.RECORD=w.TABLE,C.RECORD=C.TABLE,w.LITERAL=function(e){return e},C.LITERAL=function(e){return e.length},W.prototype.encode=function(){return w.TABLE(this)},W.prototype.sizeOf=function(){return C.TABLE(this)};var Q={Table:W,Record:W,Coverage:(V.prototype=Object.create(W.prototype)).constructor=V,ScriptList:(Y.prototype=Object.create(W.prototype)).constructor=Y,FeatureList:(j.prototype=Object.create(W.prototype)).constructor=j,LookupList:(Z.prototype=Object.create(W.prototype)).constructor=Z,ushortList:q,tableList:X,recordList:_};function K(e,t){return e.getUint8(t)}function J(e,t){return e.getUint16(t,!1)}function $(e,t){return e.getUint32(t,!1)}function ee(e,t){return e.getInt16(t,!1)+e.getUint16(t+2,!1)/65535}var te={byte:1,uShort:2,short:2,uLong:4,fixed:4,longDateTime:8,tag:4};function re(e,t){this.data=e,this.offset=t,this.relativeOffset=0}re.prototype.parseByte=function(){var e=this.data.getUint8(this.offset+this.relativeOffset);return this.relativeOffset+=1,e},re.prototype.parseChar=function(){var e=this.data.getInt8(this.offset+this.relativeOffset);return this.relativeOffset+=1,e},re.prototype.parseCard8=re.prototype.parseByte,re.prototype.parseCard16=re.prototype.parseUShort=function(){var e=this.data.getUint16(this.offset+this.relativeOffset);return this.relativeOffset+=2,e},re.prototype.parseSID=re.prototype.parseUShort,re.prototype.parseOffset16=re.prototype.parseUShort,re.prototype.parseShort=function(){var e=this.data.getInt16(this.offset+this.relativeOffset);return this.relativeOffset+=2,e},re.prototype.parseF2Dot14=function(){var e=this.data.getInt16(this.offset+this.relativeOffset)/16384;return this.relativeOffset+=2,e},re.prototype.parseOffset32=re.prototype.parseULong=function(){var e=$(this.data,this.offset+this.relativeOffset);return this.relativeOffset+=4,e},re.prototype.parseFixed=function(){var e=ee(this.data,this.offset+this.relativeOffset);return this.relativeOffset+=4,e},re.prototype.parseString=function(e){var t=this.data,r=this.offset+this.relativeOffset,a="";this.relativeOffset+=e;for(var n=0;n<e;n++)a+=String.fromCharCode(t.getUint8(r+n));return a},re.prototype.parseTag=function(){return this.parseString(4)},re.prototype.parseLongDateTime=function(){var e=$(this.data,this.offset+this.relativeOffset+4);return e-=2082844800,this.relativeOffset+=8,e},re.prototype.parseVersion=function(e){var t=J(this.data,this.offset+this.relativeOffset),r=J(this.data,this.offset+this.relativeOffset+2);return this.relativeOffset+=4,void 0===e&&(e=4096),t+r/e/10},re.prototype.skip=function(e,t){void 0===t&&(t=1),this.relativeOffset+=te[e]*t},re.prototype.parseULongList=function(e){void 0===e&&(e=this.parseULong());for(var t=new Array(e),r=this.data,a=this.offset+this.relativeOffset,n=0;n<e;n++)t[n]=r.getUint32(a),a+=4;return this.relativeOffset+=4*e,t},re.prototype.parseOffset16List=re.prototype.parseUShortList=function(e){void 0===e&&(e=this.parseUShort());for(var t=new Array(e),r=this.data,a=this.offset+this.relativeOffset,n=0;n<e;n++)t[n]=r.getUint16(a),a+=2;return this.relativeOffset+=2*e,t},re.prototype.parseShortList=function(e){for(var t=new Array(e),r=this.data,a=this.offset+this.relativeOffset,n=0;n<e;n++)t[n]=r.getInt16(a),a+=2;return this.relativeOffset+=2*e,t},re.prototype.parseByteList=function(e){for(var t=new Array(e),r=this.data,a=this.offset+this.relativeOffset,n=0;n<e;n++)t[n]=r.getUint8(a++);return this.relativeOffset+=e,t},re.prototype.parseList=function(e,t){t||(t=e,e=this.parseUShort());for(var r=new Array(e),a=0;a<e;a++)r[a]=t.call(this);return r},re.prototype.parseList32=function(e,t){t||(t=e,e=this.parseULong());for(var r=new Array(e),a=0;a<e;a++)r[a]=t.call(this);return r},re.prototype.parseRecordList=function(e,t){t||(t=e,e=this.parseUShort());for(var r=new Array(e),a=Object.keys(t),n=0;n<e;n++){for(var o={},s=0;s<a.length;s++){var i=a[s],l=t[i];o[i]=l.call(this)}r[n]=o}return r},re.prototype.parseRecordList32=function(e,t){t||(t=e,e=this.parseULong());for(var r=new Array(e),a=Object.keys(t),n=0;n<e;n++){for(var o={},s=0;s<a.length;s++){var i=a[s],l=t[i];o[i]=l.call(this)}r[n]=o}return r},re.prototype.parseStruct=function(e){if("function"==typeof e)return e.call(this);for(var t=Object.keys(e),r={},a=0;a<t.length;a++){var n=t[a],o=e[n];r[n]=o.call(this)}return r},re.prototype.parseValueRecord=function(e){if(void 0===e&&(e=this.parseUShort()),0!==e){var t={};return 1&e&&(t.xPlacement=this.parseShort()),2&e&&(t.yPlacement=this.parseShort()),4&e&&(t.xAdvance=this.parseShort()),8&e&&(t.yAdvance=this.parseShort()),16&e&&(t.xPlaDevice=void 0,this.parseShort()),32&e&&(t.yPlaDevice=void 0,this.parseShort()),64&e&&(t.xAdvDevice=void 0,this.parseShort()),128&e&&(t.yAdvDevice=void 0,this.parseShort()),t}},re.prototype.parseValueRecordList=function(){for(var e=this.parseUShort(),t=this.parseUShort(),r=new Array(t),a=0;a<t;a++)r[a]=this.parseValueRecord(e);return r},re.prototype.parsePointer=function(e){var t=this.parseOffset16();if(0<t)return new re(this.data,this.offset+t).parseStruct(e)},re.prototype.parsePointer32=function(e){var t=this.parseOffset32();if(0<t)return new re(this.data,this.offset+t).parseStruct(e)},re.prototype.parseListOfLists=function(e){for(var t=this,r=this.parseOffset16List(),a=r.length,n=this.relativeOffset,o=new Array(a),s=0;s<a;s++){var i=r[s];if(0!==i)if(t.relativeOffset=i,e){for(var l=t.parseOffset16List(),u=new Array(l.length),p=0;p<l.length;p++)t.relativeOffset=i+l[p],u[p]=e.call(t);o[s]=u}else o[s]=t.parseUShortList();else o[s]=void 0}return this.relativeOffset=n,o},re.prototype.parseCoverage=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort(),r=this.parseUShort();if(1===t)return{format:1,glyphs:this.parseUShortList(r)};if(2===t){for(var a=new Array(r),n=0;n<r;n++)a[n]={start:this.parseUShort(),end:this.parseUShort(),index:this.parseUShort()};return{format:2,ranges:a}}throw new Error("0x"+e.toString(16)+": Coverage format must be 1 or 2.")},re.prototype.parseClassDef=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort();if(1===t)return{format:1,startGlyph:this.parseUShort(),classes:this.parseUShortList()};if(2===t)return{format:2,ranges:this.parseRecordList({start:re.uShort,end:re.uShort,classId:re.uShort})};throw new Error("0x"+e.toString(16)+": ClassDef format must be 1 or 2.")},re.list=function(e,t){return function(){return this.parseList(e,t)}},re.list32=function(e,t){return function(){return this.parseList32(e,t)}},re.recordList=function(e,t){return function(){return this.parseRecordList(e,t)}},re.recordList32=function(e,t){return function(){return this.parseRecordList32(e,t)}},re.pointer=function(e){return function(){return this.parsePointer(e)}},re.pointer32=function(e){return function(){return this.parsePointer32(e)}},re.tag=re.prototype.parseTag,re.byte=re.prototype.parseByte,re.uShort=re.offset16=re.prototype.parseUShort,re.uShortList=re.prototype.parseUShortList,re.uLong=re.offset32=re.prototype.parseULong,re.uLongList=re.prototype.parseULongList,re.struct=re.prototype.parseStruct,re.coverage=re.prototype.parseCoverage,re.classDef=re.prototype.parseClassDef;var ae={reserved:re.uShort,reqFeatureIndex:re.uShort,featureIndexes:re.uShortList};re.prototype.parseScriptList=function(){return this.parsePointer(re.recordList({tag:re.tag,script:re.pointer({defaultLangSys:re.pointer(ae),langSysRecords:re.recordList({tag:re.tag,langSys:re.pointer(ae)})})}))||[]},re.prototype.parseFeatureList=function(){return this.parsePointer(re.recordList({tag:re.tag,feature:re.pointer({featureParams:re.offset16,lookupListIndexes:re.uShortList})}))||[]},re.prototype.parseLookupList=function(a){return this.parsePointer(re.list(re.pointer(function(){var e=this.parseUShort();L.argument(1<=e&&e<=9,"GPOS/GSUB lookup type "+e+" unknown.");var t=this.parseUShort(),r=16&t;return{lookupType:e,lookupFlag:t,subtables:this.parseList(re.pointer(a[e])),markFilteringSet:r?this.parseUShort():void 0}})))||[]},re.prototype.parseFeatureVariationsList=function(){return this.parsePointer32(function(){var e=this.parseUShort(),t=this.parseUShort();return L.argument(1===e&&t<1,"GPOS/GSUB feature variations table unknown."),this.parseRecordList32({conditionSetOffset:re.offset32,featureTableSubstitutionOffset:re.offset32})})||[]};var ne={getByte:K,getCard8:K,getUShort:J,getCard16:J,getShort:function(e,t){return e.getInt16(t,!1)},getULong:$,getFixed:ee,getTag:function(e,t){for(var r="",a=t;a<t+4;a+=1)r+=String.fromCharCode(e.getInt8(a));return r},getOffset:function(e,t,r){for(var a=0,n=0;n<r;n+=1)a<<=8,a+=e.getUint8(t+n);return a},getBytes:function(e,t,r){for(var a=[],n=t;n<r;n+=1)a.push(e.getUint8(n));return a},bytesToString:function(e){for(var t="",r=0;r<e.length;r+=1)t+=String.fromCharCode(e[r]);return t},Parser:re};var oe={parse:function(e,t){var r={};r.version=ne.getUShort(e,t),L.argument(0===r.version,"cmap table version should be 0."),r.numTables=ne.getUShort(e,t+2);for(var a=-1,n=r.numTables-1;0<=n;n-=1){var o=ne.getUShort(e,t+4+8*n),s=ne.getUShort(e,t+4+8*n+2);if(3===o&&(0===s||1===s||10===s)){a=ne.getULong(e,t+4+8*n+4);break}}if(-1===a)throw new Error("No valid cmap sub-tables found.");var i=new ne.Parser(e,t+a);if(r.format=i.parseUShort(),12===r.format)!function(e,t){var r;t.parseUShort(),e.length=t.parseULong(),e.language=t.parseULong(),e.groupCount=r=t.parseULong(),e.glyphIndexMap={};for(var a=0;a<r;a+=1)for(var n=t.parseULong(),o=t.parseULong(),s=t.parseULong(),i=n;i<=o;i+=1)e.glyphIndexMap[i]=s,s++}(r,i);else{if(4!==r.format)throw new Error("Only format 4 and 12 cmap tables are supported (found format "+r.format+").");!function(e,t,r,a,n){var o;e.length=t.parseUShort(),e.language=t.parseUShort(),e.segCount=o=t.parseUShort()>>1,t.skip("uShort",3),e.glyphIndexMap={};for(var s=new ne.Parser(r,a+n+14),i=new ne.Parser(r,a+n+16+2*o),l=new ne.Parser(r,a+n+16+4*o),u=new ne.Parser(r,a+n+16+6*o),p=a+n+16+8*o,c=0;c<o-1;c+=1)for(var h=void 0,f=s.parseUShort(),d=i.parseUShort(),v=l.parseShort(),g=u.parseUShort(),m=d;m<=f;m+=1)0!==g?(p=u.offset+u.relativeOffset-2,p+=g,p+=2*(m-d),0!==(h=ne.getUShort(r,p))&&(h=h+v&65535)):h=m+v&65535,e.glyphIndexMap[m]=h}(r,i,e,t,a)}return r},make:function(e){var t,r=!0;for(t=e.length-1;0<t;t-=1)if(65535<e.get(t).unicode){console.log("Adding CMAP format 12 (needed!)"),r=!1;break}var a=[{name:"version",type:"USHORT",value:0},{name:"numTables",type:"USHORT",value:r?1:2},{name:"platformID",type:"USHORT",value:3},{name:"encodingID",type:"USHORT",value:1},{name:"offset",type:"ULONG",value:r?12:20}];r||(a=a.concat([{name:"cmap12PlatformID",type:"USHORT",value:3},{name:"cmap12EncodingID",type:"USHORT",value:10},{name:"cmap12Offset",type:"ULONG",value:0}])),a=a.concat([{name:"format",type:"USHORT",value:4},{name:"cmap4Length",type:"USHORT",value:0},{name:"language",type:"USHORT",value:0},{name:"segCountX2",type:"USHORT",value:0},{name:"searchRange",type:"USHORT",value:0},{name:"entrySelector",type:"USHORT",value:0},{name:"rangeShift",type:"USHORT",value:0}]);var n,o,s,i=new Q.Table("cmap",a);for(i.segments=[],t=0;t<e.length;t+=1){for(var l=e.get(t),u=0;u<l.unicodes.length;u+=1)n=i,o=l.unicodes[u],s=t,n.segments.push({end:o,start:o,delta:-(o-s),offset:0,glyphIndex:s});i.segments=i.segments.sort(function(e,t){return e.start-t.start})}i.segments.push({end:65535,start:65535,delta:1,offset:0});var p=i.segments.length,c=0,h=[],f=[],d=[],v=[],g=[],m=[];for(t=0;t<p;t+=1){var y=i.segments[t];y.end<=65535&&y.start<=65535?(h=h.concat({name:"end_"+t,type:"USHORT",value:y.end}),f=f.concat({name:"start_"+t,type:"USHORT",value:y.start}),d=d.concat({name:"idDelta_"+t,type:"SHORT",value:y.delta}),v=v.concat({name:"idRangeOffset_"+t,type:"USHORT",value:y.offset}),void 0!==y.glyphId&&(g=g.concat({name:"glyph_"+t,type:"USHORT",value:y.glyphId}))):c+=1,r||void 0===y.glyphIndex||(m=(m=(m=m.concat({name:"cmap12Start_"+t,type:"ULONG",value:y.start})).concat({name:"cmap12End_"+t,type:"ULONG",value:y.end})).concat({name:"cmap12Glyph_"+t,type:"ULONG",value:y.glyphIndex}))}if(i.segCountX2=2*(p-c),i.searchRange=2*Math.pow(2,Math.floor(Math.log(p-c)/Math.log(2))),i.entrySelector=Math.log(i.searchRange/2)/Math.log(2),i.rangeShift=i.segCountX2-i.searchRange,i.fields=i.fields.concat(h),i.fields.push({name:"reservedPad",type:"USHORT",value:0}),i.fields=i.fields.concat(f),i.fields=i.fields.concat(d),i.fields=i.fields.concat(v),i.fields=i.fields.concat(g),i.cmap4Length=14+2*h.length+2+2*f.length+2*d.length+2*v.length+2*g.length,!r){var b=16+4*m.length;i.cmap12Offset=20+i.cmap4Length,i.fields=i.fields.concat([{name:"cmap12Format",type:"USHORT",value:12},{name:"cmap12Reserved",type:"USHORT",value:0},{name:"cmap12Length",type:"ULONG",value:b},{name:"cmap12Language",type:"ULONG",value:0},{name:"cmap12nGroups",type:"ULONG",value:m.length/3}]),i.fields=i.fields.concat(m)}return i}},se=[".notdef","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quoteright","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","quoteleft","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","exclamdown","cent","sterling","fraction","yen","florin","section","currency","quotesingle","quotedblleft","guillemotleft","guilsinglleft","guilsinglright","fi","fl","endash","dagger","daggerdbl","periodcentered","paragraph","bullet","quotesinglbase","quotedblbase","quotedblright","guillemotright","ellipsis","perthousand","questiondown","grave","acute","circumflex","tilde","macron","breve","dotaccent","dieresis","ring","cedilla","hungarumlaut","ogonek","caron","emdash","AE","ordfeminine","Lslash","Oslash","OE","ordmasculine","ae","dotlessi","lslash","oslash","oe","germandbls","onesuperior","logicalnot","mu","trademark","Eth","onehalf","plusminus","Thorn","onequarter","divide","brokenbar","degree","thorn","threequarters","twosuperior","registered","minus","eth","multiply","threesuperior","copyright","Aacute","Acircumflex","Adieresis","Agrave","Aring","Atilde","Ccedilla","Eacute","Ecircumflex","Edieresis","Egrave","Iacute","Icircumflex","Idieresis","Igrave","Ntilde","Oacute","Ocircumflex","Odieresis","Ograve","Otilde","Scaron","Uacute","Ucircumflex","Udieresis","Ugrave","Yacute","Ydieresis","Zcaron","aacute","acircumflex","adieresis","agrave","aring","atilde","ccedilla","eacute","ecircumflex","edieresis","egrave","iacute","icircumflex","idieresis","igrave","ntilde","oacute","ocircumflex","odieresis","ograve","otilde","scaron","uacute","ucircumflex","udieresis","ugrave","yacute","ydieresis","zcaron","exclamsmall","Hungarumlautsmall","dollaroldstyle","dollarsuperior","ampersandsmall","Acutesmall","parenleftsuperior","parenrightsuperior","266 ff","onedotenleader","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","commasuperior","threequartersemdash","periodsuperior","questionsmall","asuperior","bsuperior","centsuperior","dsuperior","esuperior","isuperior","lsuperior","msuperior","nsuperior","osuperior","rsuperior","ssuperior","tsuperior","ff","ffi","ffl","parenleftinferior","parenrightinferior","Circumflexsmall","hyphensuperior","Gravesmall","Asmall","Bsmall","Csmall","Dsmall","Esmall","Fsmall","Gsmall","Hsmall","Ismall","Jsmall","Ksmall","Lsmall","Msmall","Nsmall","Osmall","Psmall","Qsmall","Rsmall","Ssmall","Tsmall","Usmall","Vsmall","Wsmall","Xsmall","Ysmall","Zsmall","colonmonetary","onefitted","rupiah","Tildesmall","exclamdownsmall","centoldstyle","Lslashsmall","Scaronsmall","Zcaronsmall","Dieresissmall","Brevesmall","Caronsmall","Dotaccentsmall","Macronsmall","figuredash","hypheninferior","Ogoneksmall","Ringsmall","Cedillasmall","questiondownsmall","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","zerosuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior","Agravesmall","Aacutesmall","Acircumflexsmall","Atildesmall","Adieresissmall","Aringsmall","AEsmall","Ccedillasmall","Egravesmall","Eacutesmall","Ecircumflexsmall","Edieresissmall","Igravesmall","Iacutesmall","Icircumflexsmall","Idieresissmall","Ethsmall","Ntildesmall","Ogravesmall","Oacutesmall","Ocircumflexsmall","Otildesmall","Odieresissmall","OEsmall","Oslashsmall","Ugravesmall","Uacutesmall","Ucircumflexsmall","Udieresissmall","Yacutesmall","Thornsmall","Ydieresissmall","001.000","001.001","001.002","001.003","Black","Bold","Book","Light","Medium","Regular","Roman","Semibold"],ie=["","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quoteright","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","quoteleft","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","exclamdown","cent","sterling","fraction","yen","florin","section","currency","quotesingle","quotedblleft","guillemotleft","guilsinglleft","guilsinglright","fi","fl","","endash","dagger","daggerdbl","periodcentered","","paragraph","bullet","quotesinglbase","quotedblbase","quotedblright","guillemotright","ellipsis","perthousand","","questiondown","","grave","acute","circumflex","tilde","macron","breve","dotaccent","dieresis","","ring","cedilla","","hungarumlaut","ogonek","caron","emdash","","","","","","","","","","","","","","","","","AE","","ordfeminine","","","","","Lslash","Oslash","OE","ordmasculine","","","","","","ae","","","","dotlessi","","","lslash","oslash","oe","germandbls"],le=["","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","space","exclamsmall","Hungarumlautsmall","","dollaroldstyle","dollarsuperior","ampersandsmall","Acutesmall","parenleftsuperior","parenrightsuperior","twodotenleader","onedotenleader","comma","hyphen","period","fraction","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","colon","semicolon","commasuperior","threequartersemdash","periodsuperior","questionsmall","","asuperior","bsuperior","centsuperior","dsuperior","esuperior","","","isuperior","","","lsuperior","msuperior","nsuperior","osuperior","","","rsuperior","ssuperior","tsuperior","","ff","fi","fl","ffi","ffl","parenleftinferior","","parenrightinferior","Circumflexsmall","hyphensuperior","Gravesmall","Asmall","Bsmall","Csmall","Dsmall","Esmall","Fsmall","Gsmall","Hsmall","Ismall","Jsmall","Ksmall","Lsmall","Msmall","Nsmall","Osmall","Psmall","Qsmall","Rsmall","Ssmall","Tsmall","Usmall","Vsmall","Wsmall","Xsmall","Ysmall","Zsmall","colonmonetary","onefitted","rupiah","Tildesmall","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","exclamdownsmall","centoldstyle","Lslashsmall","","","Scaronsmall","Zcaronsmall","Dieresissmall","Brevesmall","Caronsmall","","Dotaccentsmall","","","Macronsmall","","","figuredash","hypheninferior","","","Ogoneksmall","Ringsmall","Cedillasmall","","","","onequarter","onehalf","threequarters","questiondownsmall","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","","","zerosuperior","onesuperior","twosuperior","threesuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior","Agravesmall","Aacutesmall","Acircumflexsmall","Atildesmall","Adieresissmall","Aringsmall","AEsmall","Ccedillasmall","Egravesmall","Eacutesmall","Ecircumflexsmall","Edieresissmall","Igravesmall","Iacutesmall","Icircumflexsmall","Idieresissmall","Ethsmall","Ntildesmall","Ogravesmall","Oacutesmall","Ocircumflexsmall","Otildesmall","Odieresissmall","OEsmall","Oslashsmall","Ugravesmall","Uacutesmall","Ucircumflexsmall","Udieresissmall","Yacutesmall","Thornsmall","Ydieresissmall"],ue=[".notdef",".null","nonmarkingreturn","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quotesingle","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","grave","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","Adieresis","Aring","Ccedilla","Eacute","Ntilde","Odieresis","Udieresis","aacute","agrave","acircumflex","adieresis","atilde","aring","ccedilla","eacute","egrave","ecircumflex","edieresis","iacute","igrave","icircumflex","idieresis","ntilde","oacute","ograve","ocircumflex","odieresis","otilde","uacute","ugrave","ucircumflex","udieresis","dagger","degree","cent","sterling","section","bullet","paragraph","germandbls","registered","copyright","trademark","acute","dieresis","notequal","AE","Oslash","infinity","plusminus","lessequal","greaterequal","yen","mu","partialdiff","summation","product","pi","integral","ordfeminine","ordmasculine","Omega","ae","oslash","questiondown","exclamdown","logicalnot","radical","florin","approxequal","Delta","guillemotleft","guillemotright","ellipsis","nonbreakingspace","Agrave","Atilde","Otilde","OE","oe","endash","emdash","quotedblleft","quotedblright","quoteleft","quoteright","divide","lozenge","ydieresis","Ydieresis","fraction","currency","guilsinglleft","guilsinglright","fi","fl","daggerdbl","periodcentered","quotesinglbase","quotedblbase","perthousand","Acircumflex","Ecircumflex","Aacute","Edieresis","Egrave","Iacute","Icircumflex","Idieresis","Igrave","Oacute","Ocircumflex","apple","Ograve","Uacute","Ucircumflex","Ugrave","dotlessi","circumflex","tilde","macron","breve","dotaccent","ring","cedilla","hungarumlaut","ogonek","caron","Lslash","lslash","Scaron","scaron","Zcaron","zcaron","brokenbar","Eth","eth","Yacute","yacute","Thorn","thorn","minus","multiply","onesuperior","twosuperior","threesuperior","onehalf","onequarter","threequarters","franc","Gbreve","gbreve","Idotaccent","Scedilla","scedilla","Cacute","cacute","Ccaron","ccaron","dcroat"];function pe(e){this.font=e}function ce(e){this.cmap=e}function he(e,t){this.encoding=e,this.charset=t}function fe(e){switch(e.version){case 1:this.names=ue.slice();break;case 2:this.names=new Array(e.numberOfGlyphs);for(var t=0;t<e.numberOfGlyphs;t++)e.glyphNameIndex[t]<ue.length?this.names[t]=ue[e.glyphNameIndex[t]]:this.names[t]=e.names[e.glyphNameIndex[t]-ue.length];break;case 2.5:this.names=new Array(e.numberOfGlyphs);for(var r=0;r<e.numberOfGlyphs;r++)this.names[r]=ue[r+e.glyphNameIndex[r]];break;case 3:default:this.names=[]}}pe.prototype.charToGlyphIndex=function(e){var t=e.charCodeAt(0),r=this.font.glyphs;if(r)for(var a=0;a<r.length;a+=1)for(var n=r.get(a),o=0;o<n.unicodes.length;o+=1)if(n.unicodes[o]===t)return a;return null},ce.prototype.charToGlyphIndex=function(e){return this.cmap.glyphIndexMap[e.charCodeAt(0)]||0},he.prototype.charToGlyphIndex=function(e){var t=e.charCodeAt(0),r=this.encoding[t];return this.charset.indexOf(r)},fe.prototype.nameToGlyphIndex=function(e){return this.names.indexOf(e)},fe.prototype.glyphIndexToName=function(e){return this.names[e]};var de={line:function(e,t,r,a,n){e.beginPath(),e.moveTo(t,r),e.lineTo(a,n),e.stroke()}};function ve(e,t,r,a,n){var o;return 0<(t&a)?(o=e.parseByte(),0==(t&n)&&(o=-o),o=r+o):o=0<(t&n)?r:r+e.parseShort(),o}function ge(e,t,r){var a,n,o=new ne.Parser(t,r);if(e.numberOfContours=o.parseShort(),e._xMin=o.parseShort(),e._yMin=o.parseShort(),e._xMax=o.parseShort(),e._yMax=o.parseShort(),0<e.numberOfContours){for(var s=e.endPointIndices=[],i=0;i<e.numberOfContours;i+=1)s.push(o.parseUShort());e.instructionLength=o.parseUShort(),e.instructions=[];for(var l=0;l<e.instructionLength;l+=1)e.instructions.push(o.parseByte());var u=s[s.length-1]+1;a=[];for(var p=0;p<u;p+=1)if(n=o.parseByte(),a.push(n),0<(8&n))for(var c=o.parseByte(),h=0;h<c;h+=1)a.push(n),p+=1;if(L.argument(a.length===u,"Bad flags."),0<s.length){var f,d=[];if(0<u){for(var v=0;v<u;v+=1)n=a[v],(f={}).onCurve=!!(1&n),f.lastPointOfContour=0<=s.indexOf(v),d.push(f);for(var g=0,m=0;m<u;m+=1)n=a[m],(f=d[m]).x=ve(o,n,g,2,16),g=f.x;for(var y=0,b=0;b<u;b+=1)n=a[b],(f=d[b]).y=ve(o,n,y,4,32),y=f.y}e.points=d}else e.points=[]}else if(0===e.numberOfContours)e.points=[];else{e.isComposite=!0,e.points=[],e.components=[];for(var S=!0;S;){a=o.parseUShort();var x={glyphIndex:o.parseUShort(),xScale:1,scale01:0,scale10:0,yScale:1,dx:0,dy:0};0<(1&a)?0<(2&a)?(x.dx=o.parseShort(),x.dy=o.parseShort()):x.matchedPoints=[o.parseUShort(),o.parseUShort()]:0<(2&a)?(x.dx=o.parseChar(),x.dy=o.parseChar()):x.matchedPoints=[o.parseByte(),o.parseByte()],0<(8&a)?x.xScale=x.yScale=o.parseF2Dot14():0<(64&a)?(x.xScale=o.parseF2Dot14(),x.yScale=o.parseF2Dot14()):0<(128&a)&&(x.xScale=o.parseF2Dot14(),x.scale01=o.parseF2Dot14(),x.scale10=o.parseF2Dot14(),x.yScale=o.parseF2Dot14()),e.components.push(x),S=!!(32&a)}if(256&a){e.instructionLength=o.parseUShort(),e.instructions=[];for(var U=0;U<e.instructionLength;U+=1)e.instructions.push(o.parseByte())}}}function me(e,t){for(var r=[],a=0;a<e.length;a+=1){var n=e[a],o={x:t.xScale*n.x+t.scale01*n.y+t.dx,y:t.scale10*n.x+t.yScale*n.y+t.dy,onCurve:n.onCurve,lastPointOfContour:n.lastPointOfContour};r.push(o)}return r}function ye(e){var t=new M;if(!e)return t;for(var r=function(e){for(var t=[],r=[],a=0;a<e.length;a+=1){var n=e[a];r.push(n),n.lastPointOfContour&&(t.push(r),r=[])}return L.argument(0===r.length,"There are still points left in the current contour."),t}(e),a=0;a<r.length;++a){var n=r[a],o=null,s=n[n.length-1],i=n[0];if(s.onCurve)t.moveTo(s.x,s.y);else if(i.onCurve)t.moveTo(i.x,i.y);else{var l={x:.5*(s.x+i.x),y:.5*(s.y+i.y)};t.moveTo(l.x,l.y)}for(var u=0;u<n.length;++u)if(o=s,s=i,i=n[(u+1)%n.length],s.onCurve)t.lineTo(s.x,s.y);else{var p=o,c=i;o.onCurve||(p={x:.5*(s.x+o.x),y:.5*(s.y+o.y)},t.lineTo(p.x,p.y)),i.onCurve||(c={x:.5*(s.x+i.x),y:.5*(s.y+i.y)}),t.lineTo(p.x,p.y),t.quadraticCurveTo(s.x,s.y,c.x,c.y)}t.closePath()}return t}function be(e,t){if(t.isComposite)for(var r=0;r<t.components.length;r+=1){var a=t.components[r],n=e.get(a.glyphIndex);if(n.getPath(),n.points){var o=void 0;if(void 0===a.matchedPoints)o=me(n.points,a);else{if(a.matchedPoints[0]>t.points.length-1||a.matchedPoints[1]>n.points.length-1)throw Error("Matched points out of range in "+t.name);var s=t.points[a.matchedPoints[0]],i=n.points[a.matchedPoints[1]],l={xScale:a.xScale,scale01:a.scale01,scale10:a.scale10,yScale:a.yScale,dx:0,dy:0};i=me([i],l)[0],l.dx=s.x-i.x,l.dy=s.y-i.y,o=me(n.points,l)}t.points=t.points.concat(o)}}return ye(t.points)}var Se={getPath:ye,parse:function(e,t,r,a){for(var n=new Oe.GlyphSet(a),o=0;o<r.length-1;o+=1){var s=r[o];s!==r[o+1]?n.push(o,Oe.ttfGlyphLoader(a,o,ge,e,t+s,be)):n.push(o,Oe.glyphLoader(a,o))}return n}};function xe(e){this.bindConstructorValues(e)}function Ue(t,e,r){Object.defineProperty(t,e,{get:function(){return t.path,t[r]},set:function(e){t[r]=e},enumerable:!0,configurable:!0})}function Te(e,t){if(this.font=e,this.glyphs={},Array.isArray(t))for(var r=0;r<t.length;r++)this.glyphs[r]=t[r];this.length=t&&t.length||0}xe.prototype.bindConstructorValues=function(e){var t,r;this.index=e.index||0,this.name=e.name||null,this.unicode=e.unicode||void 0,this.unicodes=e.unicodes||void 0!==e.unicode?[e.unicode]:[],e.xMin&&(this.xMin=e.xMin),e.yMin&&(this.yMin=e.yMin),e.xMax&&(this.xMax=e.xMax),e.yMax&&(this.yMax=e.yMax),e.advanceWidth&&(this.advanceWidth=e.advanceWidth),Object.defineProperty(this,"path",(t=e.path,r=t||new M,{configurable:!0,get:function(){return"function"==typeof r&&(r=r()),r},set:function(e){r=e}}))},xe.prototype.addUnicode=function(e){0===this.unicodes.length&&(this.unicode=e),this.unicodes.push(e)},xe.prototype.getBoundingBox=function(){return this.path.getBoundingBox()},xe.prototype.getPath=function(e,t,r,a,n){var o,s;e=void 0!==e?e:0,t=void 0!==t?t:0,r=void 0!==r?r:72,a||(a={});var i=a.xScale,l=a.yScale;if(a.hinting&&n&&n.hinting&&(s=this.path&&n.hinting.exec(this,r)),s)o=Se.getPath(s).commands,e=Math.round(e),t=Math.round(t),i=l=1;else{o=this.path.commands;var u=1/this.path.unitsPerEm*r;void 0===i&&(i=u),void 0===l&&(l=u)}for(var p=new M,c=0;c<o.length;c+=1){var h=o[c];"M"===h.type?p.moveTo(e+h.x*i,t+-h.y*l):"L"===h.type?p.lineTo(e+h.x*i,t+-h.y*l):"Q"===h.type?p.quadraticCurveTo(e+h.x1*i,t+-h.y1*l,e+h.x*i,t+-h.y*l):"C"===h.type?p.curveTo(e+h.x1*i,t+-h.y1*l,e+h.x2*i,t+-h.y2*l,e+h.x*i,t+-h.y*l):"Z"===h.type&&p.closePath()}return p},xe.prototype.getContours=function(){if(void 0===this.points)return[];for(var e=[],t=[],r=0;r<this.points.length;r+=1){var a=this.points[r];t.push(a),a.lastPointOfContour&&(e.push(t),t=[])}return L.argument(0===t.length,"There are still points left in the current contour."),e},xe.prototype.getMetrics=function(){for(var e=this.path.commands,t=[],r=[],a=0;a<e.length;a+=1){var n=e[a];"Z"!==n.type&&(t.push(n.x),r.push(n.y)),"Q"!==n.type&&"C"!==n.type||(t.push(n.x1),r.push(n.y1)),"C"===n.type&&(t.push(n.x2),r.push(n.y2))}var o={xMin:Math.min.apply(null,t),yMin:Math.min.apply(null,r),xMax:Math.max.apply(null,t),yMax:Math.max.apply(null,r),leftSideBearing:this.leftSideBearing};return isFinite(o.xMin)||(o.xMin=0),isFinite(o.xMax)||(o.xMax=this.advanceWidth),isFinite(o.yMin)||(o.yMin=0),isFinite(o.yMax)||(o.yMax=0),o.rightSideBearing=this.advanceWidth-o.leftSideBearing-(o.xMax-o.xMin),o},xe.prototype.draw=function(e,t,r,a,n){this.getPath(t,r,a,n).draw(e)},xe.prototype.drawPoints=function(s,e,t,r){function a(e,t,r,a){var n=2*Math.PI;s.beginPath();for(var o=0;o<e.length;o+=1)s.moveTo(t+e[o].x*a,r+e[o].y*a),s.arc(t+e[o].x*a,r+e[o].y*a,2,0,n,!1);s.closePath(),s.fill()}e=void 0!==e?e:0,t=void 0!==t?t:0,r=void 0!==r?r:24;for(var n=1/this.path.unitsPerEm*r,o=[],i=[],l=this.path,u=0;u<l.commands.length;u+=1){var p=l.commands[u];void 0!==p.x&&o.push({x:p.x,y:-p.y}),void 0!==p.x1&&i.push({x:p.x1,y:-p.y1}),void 0!==p.x2&&i.push({x:p.x2,y:-p.y2})}s.fillStyle="blue",a(o,e,t,n),s.fillStyle="red",a(i,e,t,n)},xe.prototype.drawMetrics=function(e,t,r,a){var n;t=void 0!==t?t:0,r=void 0!==r?r:0,a=void 0!==a?a:24,n=1/this.path.unitsPerEm*a,e.lineWidth=1,e.strokeStyle="black",de.line(e,t,-1e4,t,1e4),de.line(e,-1e4,r,1e4,r);var o=this.xMin||0,s=this.yMin||0,i=this.xMax||0,l=this.yMax||0,u=this.advanceWidth||0;e.strokeStyle="blue",de.line(e,t+o*n,-1e4,t+o*n,1e4),de.line(e,t+i*n,-1e4,t+i*n,1e4),de.line(e,-1e4,r+-s*n,1e4,r+-s*n),de.line(e,-1e4,r+-l*n,1e4,r+-l*n),e.strokeStyle="green",de.line(e,t+u*n,-1e4,t+u*n,1e4)},Te.prototype.get=function(e){return"function"==typeof this.glyphs[e]&&(this.glyphs[e]=this.glyphs[e]()),this.glyphs[e]},Te.prototype.push=function(e,t){this.glyphs[e]=t,this.length++};var Oe={GlyphSet:Te,glyphLoader:function(e,t){return new xe({index:t,font:e})},ttfGlyphLoader:function(r,e,a,n,o,s){return function(){var t=new xe({index:e,font:r});return t.path=function(){a(t,n,o);var e=s(r.glyphs,t);return e.unitsPerEm=r.unitsPerEm,e},Ue(t,"xMin","_xMin"),Ue(t,"xMax","_xMax"),Ue(t,"yMin","_yMin"),Ue(t,"yMax","_yMax"),t}},cffGlyphLoader:function(r,e,a,n){return function(){var t=new xe({index:e,font:r});return t.path=function(){var e=a(r,t,n);return e.unitsPerEm=r.unitsPerEm,e},t}}};function Ee(e,t){if(e===t)return!0;if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;for(var r=0;r<e.length;r+=1)if(!Ee(e[r],t[r]))return!1;return!0}return!1}function ke(e){return e.length<1240?107:e.length<33900?1131:32768}function Re(e,t,r){var a,n,o=[],s=[],i=ne.getCard16(e,t);if(0!==i){var l=ne.getByte(e,t+2);a=t+(i+1)*l+2;for(var u=t+3,p=0;p<i+1;p+=1)o.push(ne.getOffset(e,u,l)),u+=l;n=a+o[i]}else n=t+2;for(var c=0;c<o.length-1;c+=1){var h=ne.getBytes(e,a+o[c],a+o[c+1]);r&&(h=r(h)),s.push(h)}return{objects:s,startOffset:t,endOffset:n}}function Le(e,t){if(28===t)return e.parseByte()<<8|e.parseByte();if(29===t)return e.parseByte()<<24|e.parseByte()<<16|e.parseByte()<<8|e.parseByte();if(30===t)return function(e){for(var t="",r=["0","1","2","3","4","5","6","7","8","9",".","E","E-",null,"-"];;){var a=e.parseByte(),n=a>>4,o=15&a;if(15===n)break;if(t+=r[n],15===o)break;t+=r[o]}return parseFloat(t)}(e);if(32<=t&&t<=246)return t-139;if(247<=t&&t<=250)return 256*(t-247)+e.parseByte()+108;if(251<=t&&t<=254)return 256*-(t-251)-e.parseByte()-108;throw new Error("Invalid b0 "+t)}function De(e,t,r){t=void 0!==t?t:0;var a=new ne.Parser(e,t),n=[],o=[];for(r=void 0!==r?r:e.length;a.relativeOffset<r;){var s=a.parseByte();s<=21?(12===s&&(s=1200+a.parseByte()),n.push([s,o]),o=[]):o.push(Le(a,s))}return function(e){for(var t={},r=0;r<e.length;r+=1){var a=e[r][0],n=e[r][1],o=void 0;if(o=1===n.length?n[0]:n,t.hasOwnProperty(a)&&!isNaN(t[a]))throw new Error("Object "+t+" already has key "+a);t[a]=o}return t}(n)}function we(e,t){return t=t<=390?se[t]:e[t-391]}function Ce(e,t,r){for(var a,n={},o=0;o<t.length;o+=1){var s=t[o];if(Array.isArray(s.type)){var i=[];i.length=s.type.length;for(var l=0;l<s.type.length;l++)void 0===(a=void 0!==e[s.op]?e[s.op][l]:void 0)&&(a=void 0!==s.value&&void 0!==s.value[l]?s.value[l]:null),"SID"===s.type[l]&&(a=we(r,a)),i[l]=a;n[s.name]=i}else void 0===(a=e[s.op])&&(a=void 0!==s.value?s.value:null),"SID"===s.type&&(a=we(r,a)),n[s.name]=a}return n}var Ge=[{name:"version",op:0,type:"SID"},{name:"notice",op:1,type:"SID"},{name:"copyright",op:1200,type:"SID"},{name:"fullName",op:2,type:"SID"},{name:"familyName",op:3,type:"SID"},{name:"weight",op:4,type:"SID"},{name:"isFixedPitch",op:1201,type:"number",value:0},{name:"italicAngle",op:1202,type:"number",value:0},{name:"underlinePosition",op:1203,type:"number",value:-100},{name:"underlineThickness",op:1204,type:"number",value:50},{name:"paintType",op:1205,type:"number",value:0},{name:"charstringType",op:1206,type:"number",value:2},{name:"fontMatrix",op:1207,type:["real","real","real","real","real","real"],value:[.001,0,0,.001,0,0]},{name:"uniqueId",op:13,type:"number"},{name:"fontBBox",op:5,type:["number","number","number","number"],value:[0,0,0,0]},{name:"strokeWidth",op:1208,type:"number",value:0},{name:"xuid",op:14,type:[],value:null},{name:"charset",op:15,type:"offset",value:0},{name:"encoding",op:16,type:"offset",value:0},{name:"charStrings",op:17,type:"offset",value:0},{name:"private",op:18,type:["number","offset"],value:[0,0]},{name:"ros",op:1230,type:["SID","SID","number"]},{name:"cidFontVersion",op:1231,type:"number",value:0},{name:"cidFontRevision",op:1232,type:"number",value:0},{name:"cidFontType",op:1233,type:"number",value:0},{name:"cidCount",op:1234,type:"number",value:8720},{name:"uidBase",op:1235,type:"number"},{name:"fdArray",op:1236,type:"offset"},{name:"fdSelect",op:1237,type:"offset"},{name:"fontName",op:1238,type:"SID"}],Ie=[{name:"subrs",op:19,type:"offset",value:0},{name:"defaultWidthX",op:20,type:"number",value:0},{name:"nominalWidthX",op:21,type:"number",value:0}];function Be(e,t,r,a){return Ce(De(e,t,r),Ie,a)}function Me(e,t,r,a){for(var n,o,s=[],i=0;i<r.length;i+=1){var l=new DataView(new Uint8Array(r[i]).buffer),u=(o=a,Ce(De(n=l,0,n.byteLength),Ge,o));u._subrs=[],u._subrsBias=0;var p=u.private[0],c=u.private[1];if(0!==p&&0!==c){var h=Be(e,c+t,p,a);if(u._defaultWidthX=h.defaultWidthX,u._nominalWidthX=h.nominalWidthX,0!==h.subrs){var f=Re(e,c+h.subrs+t);u._subrs=f.objects,u._subrsBias=ke(u._subrs)}u._privateDict=h}s.push(u)}return s}function Ne(g,m,e){var y,b,S,x,U,T,t,O,E=new M,k=[],R=0,L=!1,D=!1,w=0,C=0;if(g.isCIDFont){var r=g.tables.cff.topDict._fdSelect[m.index],a=g.tables.cff.topDict._fdArray[r];U=a._subrs,T=a._subrsBias,t=a._defaultWidthX,O=a._nominalWidthX}else U=g.tables.cff.topDict._subrs,T=g.tables.cff.topDict._subrsBias,t=g.tables.cff.topDict._defaultWidthX,O=g.tables.cff.topDict._nominalWidthX;var G=t;function I(e,t){D&&E.closePath(),E.moveTo(e,t),D=!0}function B(){k.length%2!=0&&!L&&(G=k.shift()+O),R+=k.length>>1,k.length=0,L=!0}return function e(t){for(var r,a,n,o,s,i,l,u,p,c,h,f,d=0;d<t.length;){var v=t[d];switch(d+=1,v){case 1:case 3:B();break;case 4:1<k.length&&!L&&(G=k.shift()+O,L=!0),C+=k.pop(),I(w,C);break;case 5:for(;0<k.length;)w+=k.shift(),C+=k.shift(),E.lineTo(w,C);break;case 6:for(;0<k.length&&(w+=k.shift(),E.lineTo(w,C),0!==k.length);)C+=k.shift(),E.lineTo(w,C);break;case 7:for(;0<k.length&&(C+=k.shift(),E.lineTo(w,C),0!==k.length);)w+=k.shift(),E.lineTo(w,C);break;case 8:for(;0<k.length;)y=w+k.shift(),b=C+k.shift(),S=y+k.shift(),x=b+k.shift(),w=S+k.shift(),C=x+k.shift(),E.curveTo(y,b,S,x,w,C);break;case 10:s=k.pop()+T,(i=U[s])&&e(i);break;case 11:return;case 12:switch(v=t[d],d+=1,v){case 35:y=w+k.shift(),b=C+k.shift(),S=y+k.shift(),x=b+k.shift(),l=S+k.shift(),u=x+k.shift(),p=l+k.shift(),c=u+k.shift(),h=p+k.shift(),f=c+k.shift(),w=h+k.shift(),C=f+k.shift(),k.shift(),E.curveTo(y,b,S,x,l,u),E.curveTo(p,c,h,f,w,C);break;case 34:y=w+k.shift(),b=C,S=y+k.shift(),x=b+k.shift(),l=S+k.shift(),u=x,p=l+k.shift(),c=x,h=p+k.shift(),f=C,w=h+k.shift(),E.curveTo(y,b,S,x,l,u),E.curveTo(p,c,h,f,w,C);break;case 36:y=w+k.shift(),b=C+k.shift(),S=y+k.shift(),x=b+k.shift(),l=S+k.shift(),u=x,p=l+k.shift(),c=x,h=p+k.shift(),f=c+k.shift(),w=h+k.shift(),E.curveTo(y,b,S,x,l,u),E.curveTo(p,c,h,f,w,C);break;case 37:y=w+k.shift(),b=C+k.shift(),S=y+k.shift(),x=b+k.shift(),l=S+k.shift(),u=x+k.shift(),p=l+k.shift(),c=u+k.shift(),h=p+k.shift(),f=c+k.shift(),Math.abs(h-w)>Math.abs(f-C)?w=h+k.shift():C=f+k.shift(),E.curveTo(y,b,S,x,l,u),E.curveTo(p,c,h,f,w,C);break;default:console.log("Glyph "+m.index+": unknown operator 1200"+v),k.length=0}break;case 14:0<k.length&&!L&&(G=k.shift()+O,L=!0),D&&(E.closePath(),D=!1);break;case 18:B();break;case 19:case 20:B(),d+=R+7>>3;break;case 21:2<k.length&&!L&&(G=k.shift()+O,L=!0),C+=k.pop(),I(w+=k.pop(),C);break;case 22:1<k.length&&!L&&(G=k.shift()+O,L=!0),I(w+=k.pop(),C);break;case 23:B();break;case 24:for(;2<k.length;)y=w+k.shift(),b=C+k.shift(),S=y+k.shift(),x=b+k.shift(),w=S+k.shift(),C=x+k.shift(),E.curveTo(y,b,S,x,w,C);w+=k.shift(),C+=k.shift(),E.lineTo(w,C);break;case 25:for(;6<k.length;)w+=k.shift(),C+=k.shift(),E.lineTo(w,C);y=w+k.shift(),b=C+k.shift(),S=y+k.shift(),x=b+k.shift(),w=S+k.shift(),C=x+k.shift(),E.curveTo(y,b,S,x,w,C);break;case 26:for(k.length%2&&(w+=k.shift());0<k.length;)y=w,b=C+k.shift(),S=y+k.shift(),x=b+k.shift(),w=S,C=x+k.shift(),E.curveTo(y,b,S,x,w,C);break;case 27:for(k.length%2&&(C+=k.shift());0<k.length;)y=w+k.shift(),b=C,S=y+k.shift(),x=b+k.shift(),w=S+k.shift(),C=x,E.curveTo(y,b,S,x,w,C);break;case 28:r=t[d],a=t[d+1],k.push((r<<24|a<<16)>>16),d+=2;break;case 29:s=k.pop()+g.gsubrsBias,(i=g.gsubrs[s])&&e(i);break;case 30:for(;0<k.length&&(y=w,b=C+k.shift(),S=y+k.shift(),x=b+k.shift(),w=S+k.shift(),C=x+(1===k.length?k.shift():0),E.curveTo(y,b,S,x,w,C),0!==k.length);)y=w+k.shift(),b=C,S=y+k.shift(),x=b+k.shift(),C=x+k.shift(),w=S+(1===k.length?k.shift():0),E.curveTo(y,b,S,x,w,C);break;case 31:for(;0<k.length&&(y=w+k.shift(),b=C,S=y+k.shift(),x=b+k.shift(),C=x+k.shift(),w=S+(1===k.length?k.shift():0),E.curveTo(y,b,S,x,w,C),0!==k.length);)y=w,b=C+k.shift(),S=y+k.shift(),x=b+k.shift(),w=S+k.shift(),C=x+(1===k.length?k.shift():0),E.curveTo(y,b,S,x,w,C);break;default:v<32?console.log("Glyph "+m.index+": unknown operator "+v):v<247?k.push(v-139):v<251?(r=t[d],d+=1,k.push(256*(v-247)+r+108)):v<255?(r=t[d],d+=1,k.push(256*-(v-251)-r-108)):(r=t[d],a=t[d+1],n=t[d+2],o=t[d+3],d+=4,k.push((r<<24|a<<16|n<<8|o)/65536))}}}(e),m.advanceWidth=G,E}function Pe(e,t){var r,a=se.indexOf(e);return 0<=a&&(r=a),0<=(a=t.indexOf(e))?r=a+se.length:(r=se.length+t.length,t.push(e)),r}function Ae(e,t,r){for(var a={},n=0;n<e.length;n+=1){var o=e[n],s=t[o.name];void 0===s||Ee(s,o.value)||("SID"===o.type&&(s=Pe(s,r)),a[o.op]={name:o.name,type:o.type,value:s})}return a}function Fe(e,t){var r=new Q.Record("Top DICT",[{name:"dict",type:"DICT",value:{}}]);return r.dict=Ae(Ge,e,t),r}function He(e){var t=new Q.Record("Top DICT INDEX",[{name:"topDicts",type:"INDEX",value:[]}]);return t.topDicts=[{name:"topDict_0",type:"TABLE",value:e}],t}function ze(e){var t=[],r=e.path;t.push({name:"width",type:"NUMBER",value:e.advanceWidth});for(var a=0,n=0,o=0;o<r.commands.length;o+=1){var s=void 0,i=void 0,l=r.commands[o];if("Q"===l.type){l={type:"C",x:l.x,y:l.y,x1:1/3*a+2/3*l.x1,y1:1/3*n+2/3*l.y1,x2:1/3*l.x+2/3*l.x1,y2:1/3*l.y+2/3*l.y1}}if("M"===l.type)s=Math.round(l.x-a),i=Math.round(l.y-n),t.push({name:"dx",type:"NUMBER",value:s}),t.push({name:"dy",type:"NUMBER",value:i}),t.push({name:"rmoveto",type:"OP",value:21}),a=Math.round(l.x),n=Math.round(l.y);else if("L"===l.type)s=Math.round(l.x-a),i=Math.round(l.y-n),t.push({name:"dx",type:"NUMBER",value:s}),t.push({name:"dy",type:"NUMBER",value:i}),t.push({name:"rlineto",type:"OP",value:5}),a=Math.round(l.x),n=Math.round(l.y);else if("C"===l.type){var u=Math.round(l.x1-a),p=Math.round(l.y1-n),c=Math.round(l.x2-l.x1),h=Math.round(l.y2-l.y1);s=Math.round(l.x-l.x2),i=Math.round(l.y-l.y2),t.push({name:"dx1",type:"NUMBER",value:u}),t.push({name:"dy1",type:"NUMBER",value:p}),t.push({name:"dx2",type:"NUMBER",value:c}),t.push({name:"dy2",type:"NUMBER",value:h}),t.push({name:"dx",type:"NUMBER",value:s}),t.push({name:"dy",type:"NUMBER",value:i}),t.push({name:"rrcurveto",type:"OP",value:8}),a=Math.round(l.x),n=Math.round(l.y)}}return t.push({name:"endchar",type:"OP",value:14}),t}var We={parse:function(e,t,r){r.tables.cff={};var a,n,o,s=Re(e,Re(e,(a=e,n=t,(o={}).formatMajor=ne.getCard8(a,n),o.formatMinor=ne.getCard8(a,n+1),o.size=ne.getCard8(a,n+2),o.offsetSize=ne.getCard8(a,n+3),o.startOffset=n,o.endOffset=n+4,o).endOffset,ne.bytesToString).endOffset),i=Re(e,s.endOffset,ne.bytesToString),l=Re(e,i.endOffset);r.gsubrs=l.objects,r.gsubrsBias=ke(r.gsubrs);var u=Me(e,t,s.objects,i.objects);if(1!==u.length)throw new Error("CFF table has too many fonts in 'FontSet' - count of fonts NameIndex.length = "+u.length);var p=u[0];if((r.tables.cff.topDict=p)._privateDict&&(r.defaultWidthX=p._privateDict.defaultWidthX,r.nominalWidthX=p._privateDict.nominalWidthX),void 0!==p.ros[0]&&void 0!==p.ros[1]&&(r.isCIDFont=!0),r.isCIDFont){var c=p.fdArray,h=p.fdSelect;if(0===c||0===h)throw new Error("Font is marked as a CID font, but FDArray and/or FDSelect information is missing");var f=Me(e,t,Re(e,c+=t).objects,i.objects);p._fdArray=f,h+=t,p._fdSelect=function(e,t,r,a){var n,o=[],s=new ne.Parser(e,t),i=s.parseCard8();if(0===i)for(var l=0;l<r;l++){if(a<=(n=s.parseCard8()))throw new Error("CFF table CID Font FDSelect has bad FD index value "+n+" (FD count "+a+")");o.push(n)}else{if(3!==i)throw new Error("CFF Table CID Font FDSelect table has unsupported format "+i);var u,p=s.parseCard16(),c=s.parseCard16();if(0!==c)throw new Error("CFF Table CID Font FDSelect format 3 range has bad initial GID "+c);for(var h=0;h<p;h++){if(n=s.parseCard8(),u=s.parseCard16(),a<=n)throw new Error("CFF table CID Font FDSelect has bad FD index value "+n+" (FD count "+a+")");if(r<u)throw new Error("CFF Table CID Font FDSelect format 3 range has bad GID "+u);for(;c<u;c++)o.push(n);c=u}if(u!==r)throw new Error("CFF Table CID Font FDSelect format 3 range has bad final GID "+u)}return o}(e,h,r.numGlyphs,f.length)}var d=t+p.private[1],v=Be(e,d,p.private[0],i.objects);if(r.defaultWidthX=v.defaultWidthX,r.nominalWidthX=v.nominalWidthX,0!==v.subrs){var g=Re(e,d+v.subrs);r.subrs=g.objects,r.subrsBias=ke(r.subrs)}else r.subrs=[],r.subrsBias=0;var m=Re(e,t+p.charStrings);r.nGlyphs=m.objects.length;var y=function(e,t,r,a){var n,o,s=new ne.Parser(e,t);r-=1;var i=[".notdef"],l=s.parseCard8();if(0===l)for(var u=0;u<r;u+=1)n=s.parseSID(),i.push(we(a,n));else if(1===l)for(;i.length<=r;){n=s.parseSID(),o=s.parseCard8();for(var p=0;p<=o;p+=1)i.push(we(a,n)),n+=1}else{if(2!==l)throw new Error("Unknown charset format "+l);for(;i.length<=r;){n=s.parseSID(),o=s.parseCard16();for(var c=0;c<=o;c+=1)i.push(we(a,n)),n+=1}}return i}(e,t+p.charset,r.nGlyphs,i.objects);0===p.encoding?r.cffEncoding=new he(ie,y):1===p.encoding?r.cffEncoding=new he(le,y):r.cffEncoding=function(e,t,r){var a,n={},o=new ne.Parser(e,t),s=o.parseCard8();if(0===s)for(var i=o.parseCard8(),l=0;l<i;l+=1)n[a=o.parseCard8()]=l;else{if(1!==s)throw new Error("Unknown encoding format "+s);var u=o.parseCard8();a=1;for(var p=0;p<u;p+=1)for(var c=o.parseCard8(),h=o.parseCard8(),f=c;f<=c+h;f+=1)n[f]=a,a+=1}return new he(n,r)}(e,t+p.encoding,y),r.encoding=r.encoding||r.cffEncoding,r.glyphs=new Oe.GlyphSet(r);for(var b=0;b<r.nGlyphs;b+=1){var S=m.objects[b];r.glyphs.push(b,Oe.cffGlyphLoader(r,b,Ne,S))}},make:function(e,t){for(var r,a=new Q.Table("CFF ",[{name:"header",type:"RECORD"},{name:"nameIndex",type:"RECORD"},{name:"topDictIndex",type:"RECORD"},{name:"stringIndex",type:"RECORD"},{name:"globalSubrIndex",type:"RECORD"},{name:"charsets",type:"RECORD"},{name:"charStringsIndex",type:"RECORD"},{name:"privateDict",type:"RECORD"}]),n=1/t.unitsPerEm,o={version:t.version,fullName:t.fullName,familyName:t.familyName,weight:t.weightName,fontBBox:t.fontBBox||[0,0,0,0],fontMatrix:[n,0,0,n,0,0],charset:999,encoding:0,charStrings:999,private:[0,999]},s=[],i=1;i<e.length;i+=1)r=e.get(i),s.push(r.name);var l=[];a.header=new Q.Record("Header",[{name:"major",type:"Card8",value:1},{name:"minor",type:"Card8",value:0},{name:"hdrSize",type:"Card8",value:4},{name:"major",type:"Card8",value:1}]),a.nameIndex=function(e){var t=new Q.Record("Name INDEX",[{name:"names",type:"INDEX",value:[]}]);t.names=[];for(var r=0;r<e.length;r+=1)t.names.push({name:"name_"+r,type:"NAME",value:e[r]});return t}([t.postScriptName]);var u,p,c,h=Fe(o,l);a.topDictIndex=He(h),a.globalSubrIndex=new Q.Record("Global Subr INDEX",[{name:"subrs",type:"INDEX",value:[]}]),a.charsets=function(e,t){for(var r=new Q.Record("Charsets",[{name:"format",type:"Card8",value:0}]),a=0;a<e.length;a+=1){var n=Pe(e[a],t);r.fields.push({name:"glyph_"+a,type:"SID",value:n})}return r}(s,l),a.charStringsIndex=function(e){for(var t=new Q.Record("CharStrings INDEX",[{name:"charStrings",type:"INDEX",value:[]}]),r=0;r<e.length;r+=1){var a=e.get(r),n=ze(a);t.charStrings.push({name:a.name,type:"CHARSTRING",value:n})}return t}(e),a.privateDict=(u={},p=l,(c=new Q.Record("Private DICT",[{name:"dict",type:"DICT",value:{}}])).dict=Ae(Ie,u,p),c),a.stringIndex=function(e){var t=new Q.Record("String INDEX",[{name:"strings",type:"INDEX",value:[]}]);t.strings=[];for(var r=0;r<e.length;r+=1)t.strings.push({name:"string_"+r,type:"STRING",value:e[r]});return t}(l);var f=a.header.sizeOf()+a.nameIndex.sizeOf()+a.topDictIndex.sizeOf()+a.stringIndex.sizeOf()+a.globalSubrIndex.sizeOf();return o.charset=f,o.encoding=0,o.charStrings=o.charset+a.charsets.sizeOf(),o.private[1]=o.charStrings+a.charStringsIndex.sizeOf(),h=Fe(o,l),a.topDictIndex=He(h),a}};var qe={parse:function(e,t){var r={},a=new ne.Parser(e,t);return r.version=a.parseVersion(),r.fontRevision=Math.round(1e3*a.parseFixed())/1e3,r.checkSumAdjustment=a.parseULong(),r.magicNumber=a.parseULong(),L.argument(1594834165===r.magicNumber,"Font header has wrong magic number."),r.flags=a.parseUShort(),r.unitsPerEm=a.parseUShort(),r.created=a.parseLongDateTime(),r.modified=a.parseLongDateTime(),r.xMin=a.parseShort(),r.yMin=a.parseShort(),r.xMax=a.parseShort(),r.yMax=a.parseShort(),r.macStyle=a.parseUShort(),r.lowestRecPPEM=a.parseUShort(),r.fontDirectionHint=a.parseShort(),r.indexToLocFormat=a.parseShort(),r.glyphDataFormat=a.parseShort(),r},make:function(e){var t=Math.round((new Date).getTime()/1e3)+2082844800,r=t;return e.createdTimestamp&&(r=e.createdTimestamp+2082844800),new Q.Table("head",[{name:"version",type:"FIXED",value:65536},{name:"fontRevision",type:"FIXED",value:65536},{name:"checkSumAdjustment",type:"ULONG",value:0},{name:"magicNumber",type:"ULONG",value:1594834165},{name:"flags",type:"USHORT",value:0},{name:"unitsPerEm",type:"USHORT",value:1e3},{name:"created",type:"LONGDATETIME",value:r},{name:"modified",type:"LONGDATETIME",value:t},{name:"xMin",type:"SHORT",value:0},{name:"yMin",type:"SHORT",value:0},{name:"xMax",type:"SHORT",value:0},{name:"yMax",type:"SHORT",value:0},{name:"macStyle",type:"USHORT",value:0},{name:"lowestRecPPEM",type:"USHORT",value:0},{name:"fontDirectionHint",type:"SHORT",value:2},{name:"indexToLocFormat",type:"SHORT",value:0},{name:"glyphDataFormat",type:"SHORT",value:0}],e)}};var Xe={parse:function(e,t){var r={},a=new ne.Parser(e,t);return r.version=a.parseVersion(),r.ascender=a.parseShort(),r.descender=a.parseShort(),r.lineGap=a.parseShort(),r.advanceWidthMax=a.parseUShort(),r.minLeftSideBearing=a.parseShort(),r.minRightSideBearing=a.parseShort(),r.xMaxExtent=a.parseShort(),r.caretSlopeRise=a.parseShort(),r.caretSlopeRun=a.parseShort(),r.caretOffset=a.parseShort(),a.relativeOffset+=8,r.metricDataFormat=a.parseShort(),r.numberOfHMetrics=a.parseUShort(),r},make:function(e){return new Q.Table("hhea",[{name:"version",type:"FIXED",value:65536},{name:"ascender",type:"FWORD",value:0},{name:"descender",type:"FWORD",value:0},{name:"lineGap",type:"FWORD",value:0},{name:"advanceWidthMax",type:"UFWORD",value:0},{name:"minLeftSideBearing",type:"FWORD",value:0},{name:"minRightSideBearing",type:"FWORD",value:0},{name:"xMaxExtent",type:"FWORD",value:0},{name:"caretSlopeRise",type:"SHORT",value:1},{name:"caretSlopeRun",type:"SHORT",value:0},{name:"caretOffset",type:"SHORT",value:0},{name:"reserved1",type:"SHORT",value:0},{name:"reserved2",type:"SHORT",value:0},{name:"reserved3",type:"SHORT",value:0},{name:"reserved4",type:"SHORT",value:0},{name:"metricDataFormat",type:"SHORT",value:0},{name:"numberOfHMetrics",type:"USHORT",value:0}],e)}};var _e={parse:function(e,t,r,a,n){for(var o,s,i=new ne.Parser(e,t),l=0;l<a;l+=1){l<r&&(o=i.parseUShort(),s=i.parseShort());var u=n.get(l);u.advanceWidth=o,u.leftSideBearing=s}},make:function(e){for(var t=new Q.Table("hmtx",[]),r=0;r<e.length;r+=1){var a=e.get(r),n=a.advanceWidth||0,o=a.leftSideBearing||0;t.fields.push({name:"advanceWidth_"+r,type:"USHORT",value:n}),t.fields.push({name:"leftSideBearing_"+r,type:"SHORT",value:o})}return t}};var Ve={make:function(e){for(var t=new Q.Table("ltag",[{name:"version",type:"ULONG",value:1},{name:"flags",type:"ULONG",value:0},{name:"numTags",type:"ULONG",value:e.length}]),r="",a=12+4*e.length,n=0;n<e.length;++n){var o=r.indexOf(e[n]);o<0&&(o=r.length,r+=e[n]),t.fields.push({name:"offset "+n,type:"USHORT",value:a+o}),t.fields.push({name:"length "+n,type:"USHORT",value:e[n].length})}return t.fields.push({name:"stringPool",type:"CHARARRAY",value:r}),t},parse:function(e,t){var r=new ne.Parser(e,t),a=r.parseULong();L.argument(1===a,"Unsupported ltag table version."),r.skip("uLong",1);for(var n=r.parseULong(),o=[],s=0;s<n;s++){for(var i="",l=t+r.parseUShort(),u=r.parseUShort(),p=l;p<l+u;++p)i+=String.fromCharCode(e.getInt8(p));o.push(i)}return o}};var Ye={parse:function(e,t){var r={},a=new ne.Parser(e,t);return r.version=a.parseVersion(),r.numGlyphs=a.parseUShort(),1===r.version&&(r.maxPoints=a.parseUShort(),r.maxContours=a.parseUShort(),r.maxCompositePoints=a.parseUShort(),r.maxCompositeContours=a.parseUShort(),r.maxZones=a.parseUShort(),r.maxTwilightPoints=a.parseUShort(),r.maxStorage=a.parseUShort(),r.maxFunctionDefs=a.parseUShort(),r.maxInstructionDefs=a.parseUShort(),r.maxStackElements=a.parseUShort(),r.maxSizeOfInstructions=a.parseUShort(),r.maxComponentElements=a.parseUShort(),r.maxComponentDepth=a.parseUShort()),r},make:function(e){return new Q.Table("maxp",[{name:"version",type:"FIXED",value:20480},{name:"numGlyphs",type:"USHORT",value:e}])}},je=["copyright","fontFamily","fontSubfamily","uniqueID","fullName","version","postScriptName","trademark","manufacturer","designer","description","manufacturerURL","designerURL","license","licenseURL","reserved","preferredFamily","preferredSubfamily","compatibleFullName","sampleText","postScriptFindFontName","wwsFamily","wwsSubfamily"],Ze={0:"en",1:"fr",2:"de",3:"it",4:"nl",5:"sv",6:"es",7:"da",8:"pt",9:"no",10:"he",11:"ja",12:"ar",13:"fi",14:"el",15:"is",16:"mt",17:"tr",18:"hr",19:"zh-Hant",20:"ur",21:"hi",22:"th",23:"ko",24:"lt",25:"pl",26:"hu",27:"es",28:"lv",29:"se",30:"fo",31:"fa",32:"ru",33:"zh",34:"nl-BE",35:"ga",36:"sq",37:"ro",38:"cz",39:"sk",40:"si",41:"yi",42:"sr",43:"mk",44:"bg",45:"uk",46:"be",47:"uz",48:"kk",49:"az-Cyrl",50:"az-Arab",51:"hy",52:"ka",53:"mo",54:"ky",55:"tg",56:"tk",57:"mn-CN",58:"mn",59:"ps",60:"ks",61:"ku",62:"sd",63:"bo",64:"ne",65:"sa",66:"mr",67:"bn",68:"as",69:"gu",70:"pa",71:"or",72:"ml",73:"kn",74:"ta",75:"te",76:"si",77:"my",78:"km",79:"lo",80:"vi",81:"id",82:"tl",83:"ms",84:"ms-Arab",85:"am",86:"ti",87:"om",88:"so",89:"sw",90:"rw",91:"rn",92:"ny",93:"mg",94:"eo",128:"cy",129:"eu",130:"ca",131:"la",132:"qu",133:"gn",134:"ay",135:"tt",136:"ug",137:"dz",138:"jv",139:"su",140:"gl",141:"af",142:"br",143:"iu",144:"gd",145:"gv",146:"ga",147:"to",148:"el-polyton",149:"kl",150:"az",151:"nn"},Qe={0:0,1:0,2:0,3:0,4:0,5:0,6:0,7:0,8:0,9:0,10:5,11:1,12:4,13:0,14:6,15:0,16:0,17:0,18:0,19:2,20:4,21:9,22:21,23:3,24:29,25:29,26:29,27:29,28:29,29:0,30:0,31:4,32:7,33:25,34:0,35:0,36:0,37:0,38:29,39:29,40:0,41:5,42:7,43:7,44:7,45:7,46:7,47:7,48:7,49:7,50:4,51:24,52:23,53:7,54:7,55:7,56:7,57:27,58:7,59:4,60:4,61:4,62:4,63:26,64:9,65:9,66:9,67:13,68:13,69:11,70:10,71:12,72:17,73:16,74:14,75:15,76:18,77:19,78:20,79:22,80:30,81:0,82:0,83:0,84:4,85:28,86:28,87:28,88:0,89:0,90:0,91:0,92:0,93:0,94:0,128:0,129:0,130:0,131:0,132:0,133:0,134:0,135:7,136:4,137:26,138:0,139:0,140:0,141:0,142:0,143:28,144:0,145:0,146:0,147:0,148:6,149:0,150:0,151:0},Ke={1078:"af",1052:"sq",1156:"gsw",1118:"am",5121:"ar-DZ",15361:"ar-BH",3073:"ar",2049:"ar-IQ",11265:"ar-JO",13313:"ar-KW",12289:"ar-LB",4097:"ar-LY",6145:"ary",8193:"ar-OM",16385:"ar-QA",1025:"ar-SA",10241:"ar-SY",7169:"aeb",14337:"ar-AE",9217:"ar-YE",1067:"hy",1101:"as",2092:"az-Cyrl",1068:"az",1133:"ba",1069:"eu",1059:"be",2117:"bn",1093:"bn-IN",8218:"bs-Cyrl",5146:"bs",1150:"br",1026:"bg",1027:"ca",3076:"zh-HK",5124:"zh-MO",2052:"zh",4100:"zh-SG",1028:"zh-TW",1155:"co",1050:"hr",4122:"hr-BA",1029:"cs",1030:"da",1164:"prs",1125:"dv",2067:"nl-BE",1043:"nl",3081:"en-AU",10249:"en-BZ",4105:"en-CA",9225:"en-029",16393:"en-IN",6153:"en-IE",8201:"en-JM",17417:"en-MY",5129:"en-NZ",13321:"en-PH",18441:"en-SG",7177:"en-ZA",11273:"en-TT",2057:"en-GB",1033:"en",12297:"en-ZW",1061:"et",1080:"fo",1124:"fil",1035:"fi",2060:"fr-BE",3084:"fr-CA",1036:"fr",5132:"fr-LU",6156:"fr-MC",4108:"fr-CH",1122:"fy",1110:"gl",1079:"ka",3079:"de-AT",1031:"de",5127:"de-LI",4103:"de-LU",2055:"de-CH",1032:"el",1135:"kl",1095:"gu",1128:"ha",1037:"he",1081:"hi",1038:"hu",1039:"is",1136:"ig",1057:"id",1117:"iu",2141:"iu-Latn",2108:"ga",1076:"xh",1077:"zu",1040:"it",2064:"it-CH",1041:"ja",1099:"kn",1087:"kk",1107:"km",1158:"quc",1159:"rw",1089:"sw",1111:"kok",1042:"ko",1088:"ky",1108:"lo",1062:"lv",1063:"lt",2094:"dsb",1134:"lb",1071:"mk",2110:"ms-BN",1086:"ms",1100:"ml",1082:"mt",1153:"mi",1146:"arn",1102:"mr",1148:"moh",1104:"mn",2128:"mn-CN",1121:"ne",1044:"nb",2068:"nn",1154:"oc",1096:"or",1123:"ps",1045:"pl",1046:"pt",2070:"pt-PT",1094:"pa",1131:"qu-BO",2155:"qu-EC",3179:"qu",1048:"ro",1047:"rm",1049:"ru",9275:"smn",4155:"smj-NO",5179:"smj",3131:"se-FI",1083:"se",2107:"se-SE",8251:"sms",6203:"sma-NO",7227:"sms",1103:"sa",7194:"sr-Cyrl-BA",3098:"sr",6170:"sr-Latn-BA",2074:"sr-Latn",1132:"nso",1074:"tn",1115:"si",1051:"sk",1060:"sl",11274:"es-AR",16394:"es-BO",13322:"es-CL",9226:"es-CO",5130:"es-CR",7178:"es-DO",12298:"es-EC",17418:"es-SV",4106:"es-GT",18442:"es-HN",2058:"es-MX",19466:"es-NI",6154:"es-PA",15370:"es-PY",10250:"es-PE",20490:"es-PR",3082:"es",1034:"es",21514:"es-US",14346:"es-UY",8202:"es-VE",2077:"sv-FI",1053:"sv",1114:"syr",1064:"tg",2143:"tzm",1097:"ta",1092:"tt",1098:"te",1054:"th",1105:"bo",1055:"tr",1090:"tk",1152:"ug",1058:"uk",1070:"hsb",1056:"ur",2115:"uz-Cyrl",1091:"uz",1066:"vi",1106:"cy",1160:"wo",1157:"sah",1144:"ii",1130:"yo"};function Je(e,t,r){switch(e){case 0:if(65535===t)return"und";if(r)return r[t];break;case 1:return Ze[t];case 3:return Ke[t]}}var $e="utf-16",et={0:"macintosh",1:"x-mac-japanese",2:"x-mac-chinesetrad",3:"x-mac-korean",6:"x-mac-greek",7:"x-mac-cyrillic",9:"x-mac-devanagai",10:"x-mac-gurmukhi",11:"x-mac-gujarati",12:"x-mac-oriya",13:"x-mac-bengali",14:"x-mac-tamil",15:"x-mac-telugu",16:"x-mac-kannada",17:"x-mac-malayalam",18:"x-mac-sinhalese",19:"x-mac-burmese",20:"x-mac-khmer",21:"x-mac-thai",22:"x-mac-lao",23:"x-mac-georgian",24:"x-mac-armenian",25:"x-mac-chinesesimp",26:"x-mac-tibetan",27:"x-mac-mongolian",28:"x-mac-ethiopic",29:"x-mac-ce",30:"x-mac-vietnamese",31:"x-mac-extarabic"},tt={15:"x-mac-icelandic",17:"x-mac-turkish",18:"x-mac-croatian",24:"x-mac-ce",25:"x-mac-ce",26:"x-mac-ce",27:"x-mac-ce",28:"x-mac-ce",30:"x-mac-icelandic",37:"x-mac-romanian",38:"x-mac-ce",39:"x-mac-ce",40:"x-mac-ce",143:"x-mac-inuit",146:"x-mac-gaelic"};function rt(e,t,r){switch(e){case 0:return $e;case 1:return tt[r]||et[t];case 3:if(1===t||10===t)return $e}}function at(e){var t={};for(var r in e)t[e[r]]=parseInt(r);return t}function nt(e,t,r,a,n,o){return new Q.Record("NameRecord",[{name:"platformID",type:"USHORT",value:e},{name:"encodingID",type:"USHORT",value:t},{name:"languageID",type:"USHORT",value:r},{name:"nameID",type:"USHORT",value:a},{name:"length",type:"USHORT",value:n},{name:"offset",type:"USHORT",value:o}])}function ot(e,t){var r=function(e,t){var r=e.length,a=t.length-r+1;e:for(var n=0;n<a;n++)for(;n<a;n++){for(var o=0;o<r;o++)if(t[n+o]!==e[o])continue e;return n}return-1}(e,t);if(r<0){r=t.length;for(var a=0,n=e.length;a<n;++a)t.push(e[a])}return r}var st={parse:function(e,t,r){for(var a={},n=new ne.Parser(e,t),o=n.parseUShort(),s=n.parseUShort(),i=n.offset+n.parseUShort(),l=0;l<s;l++){var u=n.parseUShort(),p=n.parseUShort(),c=n.parseUShort(),h=n.parseUShort(),f=je[h]||h,d=n.parseUShort(),v=n.parseUShort(),g=Je(u,c,r),m=rt(u,p,c);if(void 0!==m&&void 0!==g){var y=void 0;if(y=m===$e?D.UTF16(e,i+v,d):D.MACSTRING(e,i+v,d,m)){var b=a[f];void 0===b&&(b=a[f]={}),b[g]=y}}}return 1===o&&n.parseUShort(),a},make:function(e,t){var r,a=[],n={},o=at(je);for(var s in e){var i=o[s];if(void 0===i&&(i=s),r=parseInt(i),isNaN(r))throw new Error('Name table entry "'+s+'" does not exist, see nameTableNames for complete list.');n[r]=e[s],a.push(r)}for(var l=at(Ze),u=at(Ke),p=[],c=[],h=0;h<a.length;h++){var f=n[r=a[h]];for(var d in f){var v=f[d],g=1,m=l[d],y=Qe[m],b=rt(g,y,m),S=w.MACSTRING(v,b);void 0===S&&(g=0,(m=t.indexOf(d))<0&&(m=t.length,t.push(d)),y=4,S=w.UTF16(v));var x=ot(S,c);p.push(nt(g,y,m,r,S.length,x));var U=u[d];if(void 0!==U){var T=w.UTF16(v),O=ot(T,c);p.push(nt(3,1,U,r,T.length,O))}}}p.sort(function(e,t){return e.platformID-t.platformID||e.encodingID-t.encodingID||e.languageID-t.languageID||e.nameID-t.nameID});for(var E=new Q.Table("name",[{name:"format",type:"USHORT",value:0},{name:"count",type:"USHORT",value:p.length},{name:"stringOffset",type:"USHORT",value:6+12*p.length}]),k=0;k<p.length;k++)E.fields.push({name:"record_"+k,type:"RECORD",value:p[k]});return E.fields.push({name:"strings",type:"LITERAL",value:c}),E}},it=[{begin:0,end:127},{begin:128,end:255},{begin:256,end:383},{begin:384,end:591},{begin:592,end:687},{begin:688,end:767},{begin:768,end:879},{begin:880,end:1023},{begin:11392,end:11519},{begin:1024,end:1279},{begin:1328,end:1423},{begin:1424,end:1535},{begin:42240,end:42559},{begin:1536,end:1791},{begin:1984,end:2047},{begin:2304,end:2431},{begin:2432,end:2559},{begin:2560,end:2687},{begin:2688,end:2815},{begin:2816,end:2943},{begin:2944,end:3071},{begin:3072,end:3199},{begin:3200,end:3327},{begin:3328,end:3455},{begin:3584,end:3711},{begin:3712,end:3839},{begin:4256,end:4351},{begin:6912,end:7039},{begin:4352,end:4607},{begin:7680,end:7935},{begin:7936,end:8191},{begin:8192,end:8303},{begin:8304,end:8351},{begin:8352,end:8399},{begin:8400,end:8447},{begin:8448,end:8527},{begin:8528,end:8591},{begin:8592,end:8703},{begin:8704,end:8959},{begin:8960,end:9215},{begin:9216,end:9279},{begin:9280,end:9311},{begin:9312,end:9471},{begin:9472,end:9599},{begin:9600,end:9631},{begin:9632,end:9727},{begin:9728,end:9983},{begin:9984,end:10175},{begin:12288,end:12351},{begin:12352,end:12447},{begin:12448,end:12543},{begin:12544,end:12591},{begin:12592,end:12687},{begin:43072,end:43135},{begin:12800,end:13055},{begin:13056,end:13311},{begin:44032,end:55215},{begin:55296,end:57343},{begin:67840,end:67871},{begin:19968,end:40959},{begin:57344,end:63743},{begin:12736,end:12783},{begin:64256,end:64335},{begin:64336,end:65023},{begin:65056,end:65071},{begin:65040,end:65055},{begin:65104,end:65135},{begin:65136,end:65279},{begin:65280,end:65519},{begin:65520,end:65535},{begin:3840,end:4095},{begin:1792,end:1871},{begin:1920,end:1983},{begin:3456,end:3583},{begin:4096,end:4255},{begin:4608,end:4991},{begin:5024,end:5119},{begin:5120,end:5759},{begin:5760,end:5791},{begin:5792,end:5887},{begin:6016,end:6143},{begin:6144,end:6319},{begin:10240,end:10495},{begin:40960,end:42127},{begin:5888,end:5919},{begin:66304,end:66351},{begin:66352,end:66383},{begin:66560,end:66639},{begin:118784,end:119039},{begin:119808,end:120831},{begin:1044480,end:1048573},{begin:65024,end:65039},{begin:917504,end:917631},{begin:6400,end:6479},{begin:6480,end:6527},{begin:6528,end:6623},{begin:6656,end:6687},{begin:11264,end:11359},{begin:11568,end:11647},{begin:19904,end:19967},{begin:43008,end:43055},{begin:65536,end:65663},{begin:65856,end:65935},{begin:66432,end:66463},{begin:66464,end:66527},{begin:66640,end:66687},{begin:66688,end:66735},{begin:67584,end:67647},{begin:68096,end:68191},{begin:119552,end:119647},{begin:73728,end:74751},{begin:119648,end:119679},{begin:7040,end:7103},{begin:7168,end:7247},{begin:7248,end:7295},{begin:43136,end:43231},{begin:43264,end:43311},{begin:43312,end:43359},{begin:43520,end:43615},{begin:65936,end:65999},{begin:66e3,end:66047},{begin:66208,end:66271},{begin:127024,end:127135}];var lt={parse:function(e,t){var r={},a=new ne.Parser(e,t);r.version=a.parseUShort(),r.xAvgCharWidth=a.parseShort(),r.usWeightClass=a.parseUShort(),r.usWidthClass=a.parseUShort(),r.fsType=a.parseUShort(),r.ySubscriptXSize=a.parseShort(),r.ySubscriptYSize=a.parseShort(),r.ySubscriptXOffset=a.parseShort(),r.ySubscriptYOffset=a.parseShort(),r.ySuperscriptXSize=a.parseShort(),r.ySuperscriptYSize=a.parseShort(),r.ySuperscriptXOffset=a.parseShort(),r.ySuperscriptYOffset=a.parseShort(),r.yStrikeoutSize=a.parseShort(),r.yStrikeoutPosition=a.parseShort(),r.sFamilyClass=a.parseShort(),r.panose=[];for(var n=0;n<10;n++)r.panose[n]=a.parseByte();return r.ulUnicodeRange1=a.parseULong(),r.ulUnicodeRange2=a.parseULong(),r.ulUnicodeRange3=a.parseULong(),r.ulUnicodeRange4=a.parseULong(),r.achVendID=String.fromCharCode(a.parseByte(),a.parseByte(),a.parseByte(),a.parseByte()),r.fsSelection=a.parseUShort(),r.usFirstCharIndex=a.parseUShort(),r.usLastCharIndex=a.parseUShort(),r.sTypoAscender=a.parseShort(),r.sTypoDescender=a.parseShort(),r.sTypoLineGap=a.parseShort(),r.usWinAscent=a.parseUShort(),r.usWinDescent=a.parseUShort(),1<=r.version&&(r.ulCodePageRange1=a.parseULong(),r.ulCodePageRange2=a.parseULong()),2<=r.version&&(r.sxHeight=a.parseShort(),r.sCapHeight=a.parseShort(),r.usDefaultChar=a.parseUShort(),r.usBreakChar=a.parseUShort(),r.usMaxContent=a.parseUShort()),r},make:function(e){return new Q.Table("OS/2",[{name:"version",type:"USHORT",value:3},{name:"xAvgCharWidth",type:"SHORT",value:0},{name:"usWeightClass",type:"USHORT",value:0},{name:"usWidthClass",type:"USHORT",value:0},{name:"fsType",type:"USHORT",value:0},{name:"ySubscriptXSize",type:"SHORT",value:650},{name:"ySubscriptYSize",type:"SHORT",value:699},{name:"ySubscriptXOffset",type:"SHORT",value:0},{name:"ySubscriptYOffset",type:"SHORT",value:140},{name:"ySuperscriptXSize",type:"SHORT",value:650},{name:"ySuperscriptYSize",type:"SHORT",value:699},{name:"ySuperscriptXOffset",type:"SHORT",value:0},{name:"ySuperscriptYOffset",type:"SHORT",value:479},{name:"yStrikeoutSize",type:"SHORT",value:49},{name:"yStrikeoutPosition",type:"SHORT",value:258},{name:"sFamilyClass",type:"SHORT",value:0},{name:"bFamilyType",type:"BYTE",value:0},{name:"bSerifStyle",type:"BYTE",value:0},{name:"bWeight",type:"BYTE",value:0},{name:"bProportion",type:"BYTE",value:0},{name:"bContrast",type:"BYTE",value:0},{name:"bStrokeVariation",type:"BYTE",value:0},{name:"bArmStyle",type:"BYTE",value:0},{name:"bLetterform",type:"BYTE",value:0},{name:"bMidline",type:"BYTE",value:0},{name:"bXHeight",type:"BYTE",value:0},{name:"ulUnicodeRange1",type:"ULONG",value:0},{name:"ulUnicodeRange2",type:"ULONG",value:0},{name:"ulUnicodeRange3",type:"ULONG",value:0},{name:"ulUnicodeRange4",type:"ULONG",value:0},{name:"achVendID",type:"CHARARRAY",value:"XXXX"},{name:"fsSelection",type:"USHORT",value:0},{name:"usFirstCharIndex",type:"USHORT",value:0},{name:"usLastCharIndex",type:"USHORT",value:0},{name:"sTypoAscender",type:"SHORT",value:0},{name:"sTypoDescender",type:"SHORT",value:0},{name:"sTypoLineGap",type:"SHORT",value:0},{name:"usWinAscent",type:"USHORT",value:0},{name:"usWinDescent",type:"USHORT",value:0},{name:"ulCodePageRange1",type:"ULONG",value:0},{name:"ulCodePageRange2",type:"ULONG",value:0},{name:"sxHeight",type:"SHORT",value:0},{name:"sCapHeight",type:"SHORT",value:0},{name:"usDefaultChar",type:"USHORT",value:0},{name:"usBreakChar",type:"USHORT",value:0},{name:"usMaxContext",type:"USHORT",value:0}],e)},unicodeRanges:it,getUnicodeRange:function(e){for(var t=0;t<it.length;t+=1){var r=it[t];if(e>=r.begin&&e<r.end)return t}return-1}};var ut={parse:function(e,t){var r={},a=new ne.Parser(e,t);switch(r.version=a.parseVersion(),r.italicAngle=a.parseFixed(),r.underlinePosition=a.parseShort(),r.underlineThickness=a.parseShort(),r.isFixedPitch=a.parseULong(),r.minMemType42=a.parseULong(),r.maxMemType42=a.parseULong(),r.minMemType1=a.parseULong(),r.maxMemType1=a.parseULong(),r.version){case 1:r.names=ue.slice();break;case 2:r.numberOfGlyphs=a.parseUShort(),r.glyphNameIndex=new Array(r.numberOfGlyphs);for(var n=0;n<r.numberOfGlyphs;n++)r.glyphNameIndex[n]=a.parseUShort();r.names=[];for(var o=0;o<r.numberOfGlyphs;o++)if(r.glyphNameIndex[o]>=ue.length){var s=a.parseChar();r.names.push(a.parseString(s))}break;case 2.5:r.numberOfGlyphs=a.parseUShort(),r.offset=new Array(r.numberOfGlyphs);for(var i=0;i<r.numberOfGlyphs;i++)r.offset[i]=a.parseChar()}return r},make:function(){return new Q.Table("post",[{name:"version",type:"FIXED",value:196608},{name:"italicAngle",type:"FIXED",value:0},{name:"underlinePosition",type:"FWORD",value:0},{name:"underlineThickness",type:"FWORD",value:0},{name:"isFixedPitch",type:"ULONG",value:0},{name:"minMemType42",type:"ULONG",value:0},{name:"maxMemType42",type:"ULONG",value:0},{name:"minMemType1",type:"ULONG",value:0},{name:"maxMemType1",type:"ULONG",value:0}])}},pt=new Array(9);pt[1]=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort();return 1===t?{substFormat:1,coverage:this.parsePointer(re.coverage),deltaGlyphId:this.parseUShort()}:2===t?{substFormat:2,coverage:this.parsePointer(re.coverage),substitute:this.parseOffset16List()}:void L.assert(!1,"0x"+e.toString(16)+": lookup type 1 format must be 1 or 2.")},pt[2]=function(){var e=this.parseUShort();return L.argument(1===e,"GSUB Multiple Substitution Subtable identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(re.coverage),sequences:this.parseListOfLists()}},pt[3]=function(){var e=this.parseUShort();return L.argument(1===e,"GSUB Alternate Substitution Subtable identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(re.coverage),alternateSets:this.parseListOfLists()}},pt[4]=function(){var e=this.parseUShort();return L.argument(1===e,"GSUB ligature table identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(re.coverage),ligatureSets:this.parseListOfLists(function(){return{ligGlyph:this.parseUShort(),components:this.parseUShortList(this.parseUShort()-1)}})}};var ct={sequenceIndex:re.uShort,lookupListIndex:re.uShort};pt[5]=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort();if(1===t)return{substFormat:t,coverage:this.parsePointer(re.coverage),ruleSets:this.parseListOfLists(function(){var e=this.parseUShort(),t=this.parseUShort();return{input:this.parseUShortList(e-1),lookupRecords:this.parseRecordList(t,ct)}})};if(2===t)return{substFormat:t,coverage:this.parsePointer(re.coverage),classDef:this.parsePointer(re.classDef),classSets:this.parseListOfLists(function(){var e=this.parseUShort(),t=this.parseUShort();return{classes:this.parseUShortList(e-1),lookupRecords:this.parseRecordList(t,ct)}})};if(3===t){var r=this.parseUShort(),a=this.parseUShort();return{substFormat:t,coverages:this.parseList(r,re.pointer(re.coverage)),lookupRecords:this.parseRecordList(a,ct)}}L.assert(!1,"0x"+e.toString(16)+": lookup type 5 format must be 1, 2 or 3.")},pt[6]=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort();return 1===t?{substFormat:1,coverage:this.parsePointer(re.coverage),chainRuleSets:this.parseListOfLists(function(){return{backtrack:this.parseUShortList(),input:this.parseUShortList(this.parseShort()-1),lookahead:this.parseUShortList(),lookupRecords:this.parseRecordList(ct)}})}:2===t?{substFormat:2,coverage:this.parsePointer(re.coverage),backtrackClassDef:this.parsePointer(re.classDef),inputClassDef:this.parsePointer(re.classDef),lookaheadClassDef:this.parsePointer(re.classDef),chainClassSet:this.parseListOfLists(function(){return{backtrack:this.parseUShortList(),input:this.parseUShortList(this.parseShort()-1),lookahead:this.parseUShortList(),lookupRecords:this.parseRecordList(ct)}})}:3===t?{substFormat:3,backtrackCoverage:this.parseList(re.pointer(re.coverage)),inputCoverage:this.parseList(re.pointer(re.coverage)),lookaheadCoverage:this.parseList(re.pointer(re.coverage)),lookupRecords:this.parseRecordList(ct)}:void L.assert(!1,"0x"+e.toString(16)+": lookup type 6 format must be 1, 2 or 3.")},pt[7]=function(){var e=this.parseUShort();L.argument(1===e,"GSUB Extension Substitution subtable identifier-format must be 1");var t=this.parseUShort(),r=new re(this.data,this.offset+this.parseULong());return{substFormat:1,lookupType:t,extension:pt[t].call(r)}},pt[8]=function(){var e=this.parseUShort();return L.argument(1===e,"GSUB Reverse Chaining Contextual Single Substitution Subtable identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(re.coverage),backtrackCoverage:this.parseList(re.pointer(re.coverage)),lookaheadCoverage:this.parseList(re.pointer(re.coverage)),substitutes:this.parseUShortList()}};var ht=new Array(9);ht[1]=function(e){return 1===e.substFormat?new Q.Table("substitutionTable",[{name:"substFormat",type:"USHORT",value:1},{name:"coverage",type:"TABLE",value:new Q.Coverage(e.coverage)},{name:"deltaGlyphID",type:"USHORT",value:e.deltaGlyphId}]):new Q.Table("substitutionTable",[{name:"substFormat",type:"USHORT",value:2},{name:"coverage",type:"TABLE",value:new Q.Coverage(e.coverage)}].concat(Q.ushortList("substitute",e.substitute)))},ht[3]=function(e){return L.assert(1===e.substFormat,"Lookup type 3 substFormat must be 1."),new Q.Table("substitutionTable",[{name:"substFormat",type:"USHORT",value:1},{name:"coverage",type:"TABLE",value:new Q.Coverage(e.coverage)}].concat(Q.tableList("altSet",e.alternateSets,function(e){return new Q.Table("alternateSetTable",Q.ushortList("alternate",e))})))},ht[4]=function(e){return L.assert(1===e.substFormat,"Lookup type 4 substFormat must be 1."),new Q.Table("substitutionTable",[{name:"substFormat",type:"USHORT",value:1},{name:"coverage",type:"TABLE",value:new Q.Coverage(e.coverage)}].concat(Q.tableList("ligSet",e.ligatureSets,function(e){return new Q.Table("ligatureSetTable",Q.tableList("ligature",e,function(e){return new Q.Table("ligatureTable",[{name:"ligGlyph",type:"USHORT",value:e.ligGlyph}].concat(Q.ushortList("component",e.components,e.components.length+1)))}))})))};var ft={parse:function(e,t){var r=new re(e,t=t||0),a=r.parseVersion(1);return L.argument(1===a||1.1===a,"Unsupported GSUB table version."),1===a?{version:a,scripts:r.parseScriptList(),features:r.parseFeatureList(),lookups:r.parseLookupList(pt)}:{version:a,scripts:r.parseScriptList(),features:r.parseFeatureList(),lookups:r.parseLookupList(pt),variations:r.parseFeatureVariationsList()}},make:function(e){return new Q.Table("GSUB",[{name:"version",type:"ULONG",value:65536},{name:"scripts",type:"TABLE",value:new Q.ScriptList(e.scripts)},{name:"features",type:"TABLE",value:new Q.FeatureList(e.features)},{name:"lookups",type:"TABLE",value:new Q.LookupList(e.lookups,ht)}])}};var dt={parse:function(e,t){var r=new ne.Parser(e,t),a=r.parseULong();L.argument(1===a,"Unsupported META table version."),r.parseULong(),r.parseULong();for(var n=r.parseULong(),o={},s=0;s<n;s++){var i=r.parseTag(),l=r.parseULong(),u=r.parseULong(),p=D.UTF8(e,t+l,u);o[i]=p}return o},make:function(e){var t=Object.keys(e).length,r="",a=16+12*t,n=new Q.Table("meta",[{name:"version",type:"ULONG",value:1},{name:"flags",type:"ULONG",value:0},{name:"offset",type:"ULONG",value:a},{name:"numTags",type:"ULONG",value:t}]);for(var o in e){var s=r.length;r+=e[o],n.fields.push({name:"tag "+o,type:"TAG",value:o}),n.fields.push({name:"offset "+o,type:"ULONG",value:a+s}),n.fields.push({name:"length "+o,type:"ULONG",value:e[o].length})}return n.fields.push({name:"stringPool",type:"CHARARRAY",value:r}),n}};function vt(e){return Math.log(e)/Math.log(2)|0}function gt(e){for(;e.length%4!=0;)e.push(0);for(var t=0,r=0;r<e.length;r+=4)t+=(e[r]<<24)+(e[r+1]<<16)+(e[r+2]<<8)+e[r+3];return t%=Math.pow(2,32)}function mt(e,t,r,a){return new Q.Record("Table Record",[{name:"tag",type:"TAG",value:void 0!==e?e:""},{name:"checkSum",type:"ULONG",value:void 0!==t?t:0},{name:"offset",type:"ULONG",value:void 0!==r?r:0},{name:"length",type:"ULONG",value:void 0!==a?a:0}])}function yt(e){var t=new Q.Table("sfnt",[{name:"version",type:"TAG",value:"OTTO"},{name:"numTables",type:"USHORT",value:0},{name:"searchRange",type:"USHORT",value:0},{name:"entrySelector",type:"USHORT",value:0},{name:"rangeShift",type:"USHORT",value:0}]);t.tables=e,t.numTables=e.length;var r=Math.pow(2,vt(t.numTables));t.searchRange=16*r,t.entrySelector=vt(r),t.rangeShift=16*t.numTables-t.searchRange;for(var a=[],n=[],o=t.sizeOf()+mt().sizeOf()*t.numTables;o%4!=0;)o+=1,n.push({name:"padding",type:"BYTE",value:0});for(var s=0;s<e.length;s+=1){var i=e[s];L.argument(4===i.tableName.length,"Table name"+i.tableName+" is invalid.");var l=i.sizeOf(),u=mt(i.tableName,gt(i.encode()),o,l);for(a.push({name:u.tag+" Table Record",type:"RECORD",value:u}),n.push({name:i.tableName+" table",type:"RECORD",value:i}),o+=l,L.argument(!isNaN(o),"Something went wrong calculating the offset.");o%4!=0;)o+=1,n.push({name:"padding",type:"BYTE",value:0})}return a.sort(function(e,t){return e.value.tag>t.value.tag?1:-1}),t.fields=t.fields.concat(a),t.fields=t.fields.concat(n),t}function bt(e,t,r){for(var a=0;a<t.length;a+=1){var n=e.charToGlyphIndex(t[a]);if(0<n)return e.glyphs.get(n).getMetrics()}return r}var St,xt,Ut,Tt,Ot={make:yt,fontToTable:function(e){for(var t,r=[],a=[],n=[],o=[],s=[],i=[],l=[],u=0,p=0,c=0,h=0,f=0,d=0;d<e.glyphs.length;d+=1){var v=e.glyphs.get(d),g=0|v.unicode;if(isNaN(v.advanceWidth))throw new Error("Glyph "+v.name+" ("+d+"): advanceWidth is not a number.");(g<t||void 0===t)&&0<g&&(t=g),u<g&&(u=g);var m=lt.getUnicodeRange(g);if(m<32)p|=1<<m;else if(m<64)c|=1<<m-32;else if(m<96)h|=1<<m-64;else{if(!(m<123))throw new Error("Unicode ranges bits > 123 are reserved for internal usage");f|=1<<m-96}if(".notdef"!==v.name){var y=v.getMetrics();r.push(y.xMin),a.push(y.yMin),n.push(y.xMax),o.push(y.yMax),i.push(y.leftSideBearing),l.push(y.rightSideBearing),s.push(v.advanceWidth)}}var b={xMin:Math.min.apply(null,r),yMin:Math.min.apply(null,a),xMax:Math.max.apply(null,n),yMax:Math.max.apply(null,o),advanceWidthMax:Math.max.apply(null,s),advanceWidthAvg:function(e){for(var t=0,r=0;r<e.length;r+=1)t+=e[r];return t/e.length}(s),minLeftSideBearing:Math.min.apply(null,i),maxLeftSideBearing:Math.max.apply(null,i),minRightSideBearing:Math.min.apply(null,l)};b.ascender=e.ascender,b.descender=e.descender;var S=qe.make({flags:3,unitsPerEm:e.unitsPerEm,xMin:b.xMin,yMin:b.yMin,xMax:b.xMax,yMax:b.yMax,lowestRecPPEM:3,createdTimestamp:e.createdTimestamp}),x=Xe.make({ascender:b.ascender,descender:b.descender,advanceWidthMax:b.advanceWidthMax,minLeftSideBearing:b.minLeftSideBearing,minRightSideBearing:b.minRightSideBearing,xMaxExtent:b.maxLeftSideBearing+(b.xMax-b.xMin),numberOfHMetrics:e.glyphs.length}),U=Ye.make(e.glyphs.length),T=lt.make({xAvgCharWidth:Math.round(b.advanceWidthAvg),usWeightClass:e.tables.os2.usWeightClass,usWidthClass:e.tables.os2.usWidthClass,usFirstCharIndex:t,usLastCharIndex:u,ulUnicodeRange1:p,ulUnicodeRange2:c,ulUnicodeRange3:h,ulUnicodeRange4:f,fsSelection:e.tables.os2.fsSelection,sTypoAscender:b.ascender,sTypoDescender:b.descender,sTypoLineGap:0,usWinAscent:b.yMax,usWinDescent:Math.abs(b.yMin),ulCodePageRange1:1,sxHeight:bt(e,"xyvw",{yMax:Math.round(b.ascender/2)}).yMax,sCapHeight:bt(e,"HIKLEFJMNTZBDPRAGOQSUVWXY",b).yMax,usDefaultChar:e.hasChar(" ")?32:0,usBreakChar:e.hasChar(" ")?32:0}),O=_e.make(e.glyphs),E=oe.make(e.glyphs),k=e.getEnglishName("fontFamily"),R=e.getEnglishName("fontSubfamily"),L=k+" "+R,D=e.getEnglishName("postScriptName");D||(D=k.replace(/\s/g,"")+"-"+R);var w={};for(var C in e.names)w[C]=e.names[C];w.uniqueID||(w.uniqueID={en:e.getEnglishName("manufacturer")+":"+L}),w.postScriptName||(w.postScriptName={en:D}),w.preferredFamily||(w.preferredFamily=e.names.fontFamily),w.preferredSubfamily||(w.preferredSubfamily=e.names.fontSubfamily);var G=[],I=st.make(w,G),B=0<G.length?Ve.make(G):void 0,M=ut.make(),N=We.make(e.glyphs,{version:e.getEnglishName("version"),fullName:L,familyName:k,weightName:R,postScriptName:D,unitsPerEm:e.unitsPerEm,fontBBox:[0,b.yMin,b.ascender,b.advanceWidthMax]}),P=e.metas&&0<Object.keys(e.metas).length?dt.make(e.metas):void 0,A=[S,x,U,T,I,E,M,N,O];B&&A.push(B),e.tables.gsub&&A.push(ft.make(e.tables.gsub)),P&&A.push(P);for(var F=yt(A),H=gt(F.encode()),z=F.fields,W=!1,q=0;q<z.length;q+=1)if("head table"===z[q].name){z[q].value.checkSumAdjustment=2981146554-H,W=!0;break}if(!W)throw new Error("Could not find head table with checkSum to adjust.");return F},computeCheckSum:gt};function Et(e,t){for(var r=0,a=e.length-1;r<=a;){var n=r+a>>>1,o=e[n].tag;if(o===t)return n;o<t?r=n+1:a=n-1}return-r-1}function kt(e,t){for(var r=0,a=e.length-1;r<=a;){var n=r+a>>>1,o=e[n];if(o===t)return n;o<t?r=n+1:a=n-1}return-r-1}function Rt(e,t){for(var r,a=0,n=e.length-1;a<=n;){var o=a+n>>>1,s=(r=e[o]).start;if(s===t)return r;s<t?a=o+1:n=o-1}if(0<a)return t>(r=e[a-1]).end?0:r}function Lt(e,t){this.font=e,this.tableName=t}function Dt(e){Lt.call(this,e,"gpos")}function wt(e){Lt.call(this,e,"gsub")}function Ct(e,t){var r=e.length;if(r!==t.length)return!1;for(var a=0;a<r;a++)if(e[a]!==t[a])return!1;return!0}function Gt(e,t,r){for(var a=e.subtables,n=0;n<a.length;n++){var o=a[n];if(o.substFormat===t)return o}if(r)return a.push(r),r}function It(e){for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),a=0;a<e.length;++a)r[a]=e[a];return t}function Bt(e,t){if(!e)throw t}function Mt(e){this.font=e,this._fpgmState=this._prepState=void 0,this._errorState=0}function Nt(e){return e}function Pt(e){return Math.sign(e)*Math.round(Math.abs(e))}function At(e){return Math.sign(e)*Math.round(Math.abs(2*e))/2}function Ft(e){return Math.sign(e)*(Math.round(Math.abs(e)+.5)-.5)}function Ht(e){return Math.sign(e)*Math.ceil(Math.abs(e))}function zt(e){return Math.sign(e)*Math.floor(Math.abs(e))}(Dt.prototype=Lt.prototype={searchTag:Et,binSearch:kt,getTable:function(e){var t=this.font.tables[this.tableName];return!t&&e&&(t=this.font.tables[this.tableName]=this.createDefaultTable()),t},getScriptNames:function(){var e=this.getTable();return e?e.scripts.map(function(e){return e.tag}):[]},getDefaultScriptName:function(){var e=this.getTable();if(e){for(var t=!1,r=0;r<e.scripts.length;r++){var a=e.scripts[r].tag;if("DFLT"===a)return a;"latn"===a&&(t=!0)}return t?"latn":void 0}},getScriptTable:function(e,t){var r=this.getTable(t);if(r){e=e||"DFLT";var a=r.scripts,n=Et(r.scripts,e);if(0<=n)return a[n].script;if(t){var o={tag:e,script:{defaultLangSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]},langSysRecords:[]}};return a.splice(-1-n,0,o),o.script}}},getLangSysTable:function(e,t,r){var a=this.getScriptTable(e,r);if(a){if(!t||"dflt"===t||"DFLT"===t)return a.defaultLangSys;var n=Et(a.langSysRecords,t);if(0<=n)return a.langSysRecords[n].langSys;if(r){var o={tag:t,langSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]}};return a.langSysRecords.splice(-1-n,0,o),o.langSys}}},getFeatureTable:function(e,t,r,a){var n=this.getLangSysTable(e,t,a);if(n){for(var o,s=n.featureIndexes,i=this.font.tables[this.tableName].features,l=0;l<s.length;l++)if((o=i[s[l]]).tag===r)return o.feature;if(a){var u=i.length;return L.assert(0===u||r>=i[u-1].tag,"Features must be added in alphabetical order."),o={tag:r,feature:{params:0,lookupListIndexes:[]}},i.push(o),s.push(u),o.feature}}},getLookupTables:function(e,t,r,a,n){var o=this.getFeatureTable(e,t,r,n),s=[];if(o){for(var i,l=o.lookupListIndexes,u=this.font.tables[this.tableName].lookups,p=0;p<l.length;p++)(i=u[l[p]]).lookupType===a&&s.push(i);if(0===s.length&&n){i={lookupType:a,lookupFlag:0,subtables:[],markFilteringSet:void 0};var c=u.length;return u.push(i),l.push(c),[i]}}return s},getGlyphClass:function(e,t){switch(e.format){case 1:return e.startGlyph<=t&&t<e.startGlyph+e.classes.length?e.classes[t-e.startGlyph]:0;case 2:var r=Rt(e.ranges,t);return r?r.classId:0}},getCoverageIndex:function(e,t){switch(e.format){case 1:var r=kt(e.glyphs,t);return 0<=r?r:-1;case 2:var a=Rt(e.ranges,t);return a?a.index+t-a.start:-1}},expandCoverage:function(e){if(1===e.format)return e.glyphs;for(var t=[],r=e.ranges,a=0;a<r.length;a++)for(var n=r[a],o=n.start,s=n.end,i=o;i<=s;i++)t.push(i);return t}}).getKerningValue=function(e,t,r){for(var a=0;a<e.length;a++)for(var n=e[a].subtables,o=0;o<n.length;o++){var s=n[o],i=this.getCoverageIndex(s.coverage,t);if(!(i<0))switch(s.posFormat){case 1:for(var l=s.pairSets[i],u=0;u<l.length;u++){var p=l[u];if(p.secondGlyph===r)return p.value1&&p.value1.xAdvance||0}break;case 2:var c=this.getGlyphClass(s.classDef1,t),h=this.getGlyphClass(s.classDef2,r),f=s.classRecords[c][h];return f.value1&&f.value1.xAdvance||0}}return 0},Dt.prototype.getKerningTables=function(e,t){if(this.font.tables.gpos)return this.getLookupTables(e,t,"kern",2)},(wt.prototype=Lt.prototype).createDefaultTable=function(){return{version:1,scripts:[{tag:"DFLT",script:{defaultLangSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]},langSysRecords:[]}}],features:[],lookups:[]}},wt.prototype.getSingle=function(e,t,r){for(var a=[],n=this.getLookupTables(t,r,e,1),o=0;o<n.length;o++)for(var s=n[o].subtables,i=0;i<s.length;i++){var l=s[i],u=this.expandCoverage(l.coverage),p=void 0;if(1===l.substFormat){var c=l.deltaGlyphId;for(p=0;p<u.length;p++){var h=u[p];a.push({sub:h,by:h+c})}}else{var f=l.substitute;for(p=0;p<u.length;p++)a.push({sub:u[p],by:f[p]})}}return a},wt.prototype.getAlternates=function(e,t,r){for(var a=[],n=this.getLookupTables(t,r,e,3),o=0;o<n.length;o++)for(var s=n[o].subtables,i=0;i<s.length;i++)for(var l=s[i],u=this.expandCoverage(l.coverage),p=l.alternateSets,c=0;c<u.length;c++)a.push({sub:u[c],by:p[c]});return a},wt.prototype.getLigatures=function(e,t,r){for(var a=[],n=this.getLookupTables(t,r,e,4),o=0;o<n.length;o++)for(var s=n[o].subtables,i=0;i<s.length;i++)for(var l=s[i],u=this.expandCoverage(l.coverage),p=l.ligatureSets,c=0;c<u.length;c++)for(var h=u[c],f=p[c],d=0;d<f.length;d++){var v=f[d];a.push({sub:[h].concat(v.components),by:v.ligGlyph})}return a},wt.prototype.addSingle=function(e,t,r,a){var n=Gt(this.getLookupTables(r,a,e,1,!0)[0],2,{substFormat:2,coverage:{format:1,glyphs:[]},substitute:[]});L.assert(1===n.coverage.format,"Ligature: unable to modify coverage table format "+n.coverage.format);var o=t.sub,s=this.binSearch(n.coverage.glyphs,o);s<0&&(s=-1-s,n.coverage.glyphs.splice(s,0,o),n.substitute.splice(s,0,0)),n.substitute[s]=t.by},wt.prototype.addAlternate=function(e,t,r,a){var n=Gt(this.getLookupTables(r,a,e,3,!0)[0],1,{substFormat:1,coverage:{format:1,glyphs:[]},alternateSets:[]});L.assert(1===n.coverage.format,"Ligature: unable to modify coverage table format "+n.coverage.format);var o=t.sub,s=this.binSearch(n.coverage.glyphs,o);s<0&&(s=-1-s,n.coverage.glyphs.splice(s,0,o),n.alternateSets.splice(s,0,0)),n.alternateSets[s]=t.by},wt.prototype.addLigature=function(e,t,r,a){var n=this.getLookupTables(r,a,e,4,!0)[0],o=n.subtables[0];o||(o={substFormat:1,coverage:{format:1,glyphs:[]},ligatureSets:[]},n.subtables[0]=o),L.assert(1===o.coverage.format,"Ligature: unable to modify coverage table format "+o.coverage.format);var s=t.sub[0],i=t.sub.slice(1),l={ligGlyph:t.by,components:i},u=this.binSearch(o.coverage.glyphs,s);if(0<=u){for(var p=o.ligatureSets[u],c=0;c<p.length;c++)if(Ct(p[c].components,i))return;p.push(l)}else u=-1-u,o.coverage.glyphs.splice(u,0,s),o.ligatureSets.splice(u,0,[l])},wt.prototype.getFeature=function(e,t,r){if(/ss\d\d/.test(e))return this.getSingle(e,t,r);switch(e){case"aalt":case"salt":return this.getSingle(e,t,r).concat(this.getAlternates(e,t,r));case"dlig":case"liga":case"rlig":return this.getLigatures(e,t,r)}},wt.prototype.add=function(e,t,r,a){if(/ss\d\d/.test(e))return this.addSingle(e,t,r,a);switch(e){case"aalt":case"salt":return"number"==typeof t.by?this.addSingle(e,t,r,a):this.addAlternate(e,t,r,a);case"dlig":case"liga":case"rlig":return this.addLigature(e,t,r,a)}};var Wt=function(e){var t=this.srPeriod,r=this.srPhase,a=1;return e<0&&(e=-e,a=-1),e+=this.srThreshold-r,e=Math.trunc(e/t)*t,(e+=r)<0?r*a:e*a},qt={x:1,y:0,axis:"x",distance:function(e,t,r,a){return(r?e.xo:e.x)-(a?t.xo:t.x)},interpolate:function(e,t,r,a){var n,o,s,i,l,u,p;if(!a||a===this)return n=e.xo-t.xo,o=e.xo-r.xo,l=t.x-t.xo,u=r.x-r.xo,0===(p=(s=Math.abs(n))+(i=Math.abs(o)))?void(e.x=e.xo+(l+u)/2):void(e.x=e.xo+(l*i+u*s)/p);n=a.distance(e,t,!0,!0),o=a.distance(e,r,!0,!0),l=a.distance(t,t,!1,!0),u=a.distance(r,r,!1,!0),0!==(p=(s=Math.abs(n))+(i=Math.abs(o)))?qt.setRelative(e,e,(l*i+u*s)/p,a,!0):qt.setRelative(e,e,(l+u)/2,a,!0)},normalSlope:Number.NEGATIVE_INFINITY,setRelative:function(e,t,r,a,n){if(a&&a!==this){var o=n?t.xo:t.x,s=n?t.yo:t.y,i=o+r*a.x,l=s+r*a.y;e.x=i+(e.y-l)/a.normalSlope}else e.x=(n?t.xo:t.x)+r},slope:0,touch:function(e){e.xTouched=!0},touched:function(e){return e.xTouched},untouch:function(e){e.xTouched=!1}},Xt={x:0,y:1,axis:"y",distance:function(e,t,r,a){return(r?e.yo:e.y)-(a?t.yo:t.y)},interpolate:function(e,t,r,a){var n,o,s,i,l,u,p;if(!a||a===this)return n=e.yo-t.yo,o=e.yo-r.yo,l=t.y-t.yo,u=r.y-r.yo,0===(p=(s=Math.abs(n))+(i=Math.abs(o)))?void(e.y=e.yo+(l+u)/2):void(e.y=e.yo+(l*i+u*s)/p);n=a.distance(e,t,!0,!0),o=a.distance(e,r,!0,!0),l=a.distance(t,t,!1,!0),u=a.distance(r,r,!1,!0),0!==(p=(s=Math.abs(n))+(i=Math.abs(o)))?Xt.setRelative(e,e,(l*i+u*s)/p,a,!0):Xt.setRelative(e,e,(l+u)/2,a,!0)},normalSlope:0,setRelative:function(e,t,r,a,n){if(a&&a!==this){var o=n?t.xo:t.x,s=n?t.yo:t.y,i=o+r*a.x,l=s+r*a.y;e.y=l+a.normalSlope*(e.x-i)}else e.y=(n?t.yo:t.y)+r},slope:Number.POSITIVE_INFINITY,touch:function(e){e.yTouched=!0},touched:function(e){return e.yTouched},untouch:function(e){e.yTouched=!1}};function _t(e,t){this.x=e,this.y=t,this.axis=void 0,this.slope=t/e,this.normalSlope=-e/t,Object.freeze(this)}function Vt(e,t){var r=Math.sqrt(e*e+t*t);return t/=r,1===(e/=r)&&0===t?qt:0===e&&1===t?Xt:new _t(e,t)}function Yt(e,t,r,a){this.x=this.xo=Math.round(64*e)/64,this.y=this.yo=Math.round(64*t)/64,this.lastPointOfContour=r,this.onCurve=a,this.prevPointOnContour=void 0,this.nextPointOnContour=void 0,this.xTouched=!1,this.yTouched=!1,Object.preventExtensions(this)}Object.freeze(qt),Object.freeze(Xt),_t.prototype.distance=function(e,t,r,a){return this.x*qt.distance(e,t,r,a)+this.y*Xt.distance(e,t,r,a)},_t.prototype.interpolate=function(e,t,r,a){var n,o,s,i,l,u,p;s=a.distance(e,t,!0,!0),i=a.distance(e,r,!0,!0),n=a.distance(t,t,!1,!0),o=a.distance(r,r,!1,!0),0!==(p=(l=Math.abs(s))+(u=Math.abs(i)))?this.setRelative(e,e,(n*u+o*l)/p,a,!0):this.setRelative(e,e,(n+o)/2,a,!0)},_t.prototype.setRelative=function(e,t,r,a,n){a=a||this;var o=n?t.xo:t.x,s=n?t.yo:t.y,i=o+r*a.x,l=s+r*a.y,u=a.normalSlope,p=this.slope,c=e.x,h=e.y;e.x=(p*c-u*i+l-h)/(p-u),e.y=p*(e.x-c)+h},_t.prototype.touch=function(e){e.xTouched=!0,e.yTouched=!0},Yt.prototype.nextTouched=function(e){for(var t=this.nextPointOnContour;!e.touched(t)&&t!==this;)t=t.nextPointOnContour;return t},Yt.prototype.prevTouched=function(e){for(var t=this.prevPointOnContour;!e.touched(t)&&t!==this;)t=t.prevPointOnContour;return t};var jt=Object.freeze(new Yt(0,0)),Zt={cvCutIn:17/16,deltaBase:9,deltaShift:.125,loop:1,minDis:1,autoFlip:!0};function Qt(e,t){switch(this.env=e,this.stack=[],this.prog=t,e){case"glyf":this.zp0=this.zp1=this.zp2=1,this.rp0=this.rp1=this.rp2=0;case"prep":this.fv=this.pv=this.dpv=qt,this.round=Pt}}function Kt(e){for(var t=e.tZone=new Array(e.gZone.length),r=0;r<t.length;r++)t[r]=new Yt(0,0)}function Jt(e,t){var r,a=e.prog,n=e.ip,o=1;do{if(88===(r=a[++n]))o++;else if(89===r)o--;else if(64===r)n+=a[n+1]+1;else if(65===r)n+=2*a[n+1]+1;else if(176<=r&&r<=183)n+=r-176+1;else if(184<=r&&r<=191)n+=2*(r-184+1);else if(t&&1===o&&27===r)break}while(0<o);e.ip=n}function $t(e,t){E.DEBUG&&console.log(t.step,"SVTCA["+e.axis+"]"),t.fv=t.pv=t.dpv=e}function er(e,t){E.DEBUG&&console.log(t.step,"SPVTCA["+e.axis+"]"),t.pv=t.dpv=e}function tr(e,t){E.DEBUG&&console.log(t.step,"SFVTCA["+e.axis+"]"),t.fv=e}function rr(e,t){var r,a,n=t.stack,o=n.pop(),s=n.pop(),i=t.z2[o],l=t.z1[s];E.DEBUG&&console.log("SPVTL["+e+"]",o,s),e?(r=i.y-l.y,a=l.x-i.x):(r=l.x-i.x,a=l.y-i.y),t.pv=t.dpv=Vt(r,a)}function ar(e,t){var r,a,n=t.stack,o=n.pop(),s=n.pop(),i=t.z2[o],l=t.z1[s];E.DEBUG&&console.log("SFVTL["+e+"]",o,s),e?(r=i.y-l.y,a=l.x-i.x):(r=l.x-i.x,a=l.y-i.y),t.fv=Vt(r,a)}function nr(e){E.DEBUG&&console.log(e.step,"POP[]"),e.stack.pop()}function or(e,t){var r=t.stack.pop(),a=t.z0[r],n=t.fv,o=t.pv;E.DEBUG&&console.log(t.step,"MDAP["+e+"]",r);var s=o.distance(a,jt);e&&(s=t.round(s)),n.setRelative(a,jt,s,o),n.touch(a),t.rp0=t.rp1=r}function sr(e,t){var r,a,n,o=t.z2,s=o.length-2;E.DEBUG&&console.log(t.step,"IUP["+e.axis+"]");for(var i=0;i<s;i++)r=o[i],e.touched(r)||(a=r.prevTouched(e))!==r&&(a===(n=r.nextTouched(e))&&e.setRelative(r,r,e.distance(a,a,!1,!0),e,!0),e.interpolate(r,a,n,e))}function ir(e,t){for(var r=t.stack,a=e?t.rp1:t.rp2,n=(e?t.z0:t.z1)[a],o=t.fv,s=t.pv,i=t.loop,l=t.z2;i--;){var u=r.pop(),p=l[u],c=s.distance(n,n,!1,!0);o.setRelative(p,p,c,s),o.touch(p),E.DEBUG&&console.log(t.step,(1<t.loop?"loop "+(t.loop-i)+": ":"")+"SHP["+(e?"rp1":"rp2")+"]",u)}t.loop=1}function lr(e,t){var r=t.stack,a=e?t.rp1:t.rp2,n=(e?t.z0:t.z1)[a],o=t.fv,s=t.pv,i=r.pop(),l=t.z2[t.contours[i]],u=l;E.DEBUG&&console.log(t.step,"SHC["+e+"]",i);for(var p=s.distance(n,n,!1,!0);u!==n&&o.setRelative(u,u,p,s),(u=u.nextPointOnContour)!==l;);}function ur(e,t){var r,a,n=t.stack,o=e?t.rp1:t.rp2,s=(e?t.z0:t.z1)[o],i=t.fv,l=t.pv,u=n.pop();switch(E.DEBUG&&console.log(t.step,"SHZ["+e+"]",u),u){case 0:r=t.tZone;break;case 1:r=t.gZone;break;default:throw new Error("Invalid zone")}for(var p=l.distance(s,s,!1,!0),c=r.length-2,h=0;h<c;h++)a=r[h],i.setRelative(a,a,p,l)}function pr(e,t){var r=t.stack,a=r.pop()/64,n=r.pop(),o=t.z1[n],s=t.z0[t.rp0],i=t.fv,l=t.pv;i.setRelative(o,s,a,l),i.touch(o),E.DEBUG&&console.log(t.step,"MSIRP["+e+"]",a,n),t.rp1=t.rp0,t.rp2=n,e&&(t.rp0=n)}function cr(e,t){var r=t.stack,a=r.pop(),n=r.pop(),o=t.z0[n],s=t.fv,i=t.pv,l=t.cvt[a];E.DEBUG&&console.log(t.step,"MIAP["+e+"]",a,"(",l,")",n);var u=i.distance(o,jt);e&&(Math.abs(u-l)<t.cvCutIn&&(u=l),u=t.round(u)),s.setRelative(o,jt,u,i),0===t.zp0&&(o.xo=o.x,o.yo=o.y),s.touch(o),t.rp0=t.rp1=n}function hr(e,t){var r=t.stack,a=r.pop(),n=t.z2[a];E.DEBUG&&console.log(t.step,"GC["+e+"]",a),r.push(64*t.dpv.distance(n,jt,e,!1))}function fr(e,t){var r=t.stack,a=r.pop(),n=r.pop(),o=t.z1[a],s=t.z0[n],i=t.dpv.distance(s,o,e,e);E.DEBUG&&console.log(t.step,"MD["+e+"]",a,n,"->",i),t.stack.push(Math.round(64*i))}function dr(e,t){var r=t.stack,a=r.pop(),n=t.fv,o=t.pv,s=t.ppem,i=t.deltaBase+16*(e-1),l=t.deltaShift,u=t.z0;E.DEBUG&&console.log(t.step,"DELTAP["+e+"]",a,r);for(var p=0;p<a;p++){var c=r.pop(),h=r.pop();if(i+((240&h)>>4)===s){var f=(15&h)-8;0<=f&&f++,E.DEBUG&&console.log(t.step,"DELTAPFIX",c,"by",f*l);var d=u[c];n.setRelative(d,d,f*l,o)}}}function vr(e,t){var r=t.stack,a=r.pop();E.DEBUG&&console.log(t.step,"ROUND[]"),r.push(64*t.round(a/64))}function gr(e,t){var r=t.stack,a=r.pop(),n=t.ppem,o=t.deltaBase+16*(e-1),s=t.deltaShift;E.DEBUG&&console.log(t.step,"DELTAC["+e+"]",a,r);for(var i=0;i<a;i++){var l=r.pop(),u=r.pop();if(o+((240&u)>>4)===n){var p=(15&u)-8;0<=p&&p++;var c=p*s;E.DEBUG&&console.log(t.step,"DELTACFIX",l,"by",c),t.cvt[l]+=c}}}function mr(e,t){var r,a,n=t.stack,o=n.pop(),s=n.pop(),i=t.z2[o],l=t.z1[s];E.DEBUG&&console.log(t.step,"SDPVTL["+e+"]",o,s),e?(r=i.y-l.y,a=l.x-i.x):(r=l.x-i.x,a=l.y-i.y),t.dpv=Vt(r,a)}function yr(e,t){var r=t.stack,a=t.prog,n=t.ip;E.DEBUG&&console.log(t.step,"PUSHB["+e+"]");for(var o=0;o<e;o++)r.push(a[++n]);t.ip=n}function br(e,t){var r=t.ip,a=t.prog,n=t.stack;E.DEBUG&&console.log(t.ip,"PUSHW["+e+"]");for(var o=0;o<e;o++){var s=a[++r]<<8|a[++r];32768&s&&(s=-(1+(65535^s))),n.push(s)}t.ip=r}function Sr(e,t,r,a,n,o){var s,i,l,u,p=o.stack,c=e&&p.pop(),h=p.pop(),f=o.rp0,d=o.z0[f],v=o.z1[h],g=o.minDis,m=o.fv,y=o.dpv;l=0<=(i=s=y.distance(v,d,!0,!0))?1:-1,i=Math.abs(i),e&&(u=o.cvt[c],a&&Math.abs(i-u)<o.cvCutIn&&(i=u)),r&&i<g&&(i=g),a&&(i=o.round(i)),m.setRelative(v,d,l*i,y),m.touch(v),E.DEBUG&&console.log(o.step,(e?"MIRP[":"MDRP[")+(t?"M":"m")+(r?">":"_")+(a?"R":"_")+(0===n?"Gr":1===n?"Bl":2===n?"Wh":"")+"]",e?c+"("+o.cvt[c]+","+u+")":"",h,"(d =",s,"->",l*i,")"),o.rp1=o.rp0,o.rp2=h,t&&(o.rp0=h)}function xr(e){(e=e||{}).empty||(Bt(e.familyName,"When creating a new Font object, familyName is required."),Bt(e.styleName,"When creating a new Font object, styleName is required."),Bt(e.unitsPerEm,"When creating a new Font object, unitsPerEm is required."),Bt(e.ascender,"When creating a new Font object, ascender is required."),Bt(e.descender,"When creating a new Font object, descender is required."),Bt(e.descender<0,"Descender should be negative (e.g. -512)."),this.names={fontFamily:{en:e.familyName||" "},fontSubfamily:{en:e.styleName||" "},fullName:{en:e.fullName||e.familyName+" "+e.styleName},postScriptName:{en:e.postScriptName||e.familyName+e.styleName},designer:{en:e.designer||" "},designerURL:{en:e.designerURL||" "},manufacturer:{en:e.manufacturer||" "},manufacturerURL:{en:e.manufacturerURL||" "},license:{en:e.license||" "},licenseURL:{en:e.licenseURL||" "},version:{en:e.version||"Version 0.1"},description:{en:e.description||" "},copyright:{en:e.copyright||" "},trademark:{en:e.trademark||" "}},this.unitsPerEm=e.unitsPerEm||1e3,this.ascender=e.ascender,this.descender=e.descender,this.createdTimestamp=e.createdTimestamp,this.tables={os2:{usWeightClass:e.weightClass||this.usWeightClasses.MEDIUM,usWidthClass:e.widthClass||this.usWidthClasses.MEDIUM,fsSelection:e.fsSelection||this.fsSelectionValues.REGULAR}}),this.supported=!0,this.glyphs=new Oe.GlyphSet(this,e.glyphs||[]),this.encoding=new pe(this),this.position=new Dt(this),this.substitution=new wt(this),this.tables=this.tables||{},Object.defineProperty(this,"hinting",{get:function(){return this._hinting?this._hinting:"truetype"===this.outlinesFormat?this._hinting=new Mt(this):void 0}})}function Ur(e,t){var r=JSON.stringify(e),a=256;for(var n in t){var o=parseInt(n);if(o&&!(o<256)){if(JSON.stringify(t[n])===r)return o;a<=o&&(a=o+1)}}return t[a]=e,a}function Tr(e,t,r,a){for(var n=[{name:"nameID_"+e,type:"USHORT",value:Ur(t.name,a)},{name:"flags_"+e,type:"USHORT",value:0}],o=0;o<r.length;++o){var s=r[o].tag;n.push({name:"axis_"+e+" "+s,type:"FIXED",value:t.coordinates[s]<<16})}return n}function Or(e,t,r,a){var n={},o=new ne.Parser(e,t);n.name=a[o.parseUShort()]||{},o.skip("uShort",1),n.coordinates={};for(var s=0;s<r.length;++s)n.coordinates[r[s].tag]=o.parseFixed();return n}Mt.prototype.exec=function(e,t){if("number"!=typeof t)throw new Error("Point size is not a number!");if(!(2<this._errorState)){var r=this.font,a=this._prepState;if(!a||a.ppem!==t){var n=this._fpgmState;if(!n){Qt.prototype=Zt,(n=this._fpgmState=new Qt("fpgm",r.tables.fpgm)).funcs=[],n.font=r,E.DEBUG&&(console.log("---EXEC FPGM---"),n.step=-1);try{xt(n)}catch(e){return console.log("Hinting error in FPGM:"+e),void(this._errorState=3)}}Qt.prototype=n,(a=this._prepState=new Qt("prep",r.tables.prep)).ppem=t;var o=r.tables.cvt;if(o)for(var s=a.cvt=new Array(o.length),i=t/r.unitsPerEm,l=0;l<o.length;l++)s[l]=o[l]*i;else a.cvt=[];E.DEBUG&&(console.log("---EXEC PREP---"),a.step=-1);try{xt(a)}catch(e){this._errorState<2&&console.log("Hinting error in PREP:"+e),this._errorState=2}}if(!(1<this._errorState))try{return Ut(e,a)}catch(e){return this._errorState<1&&(console.log("Hinting error:"+e),console.log("Note: further hinting errors are silenced")),void(this._errorState=1)}}},Ut=function(e,t){var r,a,n,o=t.ppem/t.font.unitsPerEm,s=o,i=e.components;if(Qt.prototype=t,i){var l=t.font;a=[],r=[];for(var u=0;u<i.length;u++){var p=i[u],c=l.glyphs.get(p.glyphIndex);n=new Qt("glyf",c.instructions),E.DEBUG&&(console.log("---EXEC COMP "+u+"---"),n.step=-1),Tt(c,n,o,s);for(var h=Math.round(p.dx*o),f=Math.round(p.dy*s),d=n.gZone,v=n.contours,g=0;g<d.length;g++){var m=d[g];m.xTouched=m.yTouched=!1,m.xo=m.x=m.x+h,m.yo=m.y=m.y+f}var y=a.length;a.push.apply(a,d);for(var b=0;b<v.length;b++)r.push(v[b]+y)}e.instructions&&!n.inhibitGridFit&&((n=new Qt("glyf",e.instructions)).gZone=n.z0=n.z1=n.z2=a,n.contours=r,a.push(new Yt(0,0),new Yt(Math.round(e.advanceWidth*o),0)),E.DEBUG&&(console.log("---EXEC COMPOSITE---"),n.step=-1),xt(n),a.length-=2)}else n=new Qt("glyf",e.instructions),E.DEBUG&&(console.log("---EXEC GLYPH---"),n.step=-1),Tt(e,n,o,s),a=n.gZone;return a},Tt=function(e,t,r,a){for(var n,o,s,i=e.points||[],l=i.length,u=t.gZone=t.z0=t.z1=t.z2=[],p=t.contours=[],c=0;c<l;c++)n=i[c],u[c]=new Yt(n.x*r,n.y*a,n.lastPointOfContour,n.onCurve);for(var h=0;h<l;h++)n=u[h],o||(o=n,p.push(h)),n.lastPointOfContour?((n.nextPointOnContour=o).prevPointOnContour=n,o=void 0):(s=u[h+1],(n.nextPointOnContour=s).prevPointOnContour=n);if(!t.inhibitGridFit){if(E.DEBUG){console.log("PROCESSING GLYPH",t.stack);for(var f=0;f<l;f++)console.log(f,u[f].x,u[f].y)}if(u.push(new Yt(0,0),new Yt(Math.round(e.advanceWidth*r),0)),xt(t),u.length-=2,E.DEBUG){console.log("FINISHED GLYPH",t.stack);for(var d=0;d<l;d++)console.log(d,u[d].x,u[d].y)}}},xt=function(e){var t=e.prog;if(t){var r,a=t.length;for(e.ip=0;e.ip<a;e.ip++){if(E.DEBUG&&e.step++,!(r=St[t[e.ip]]))throw new Error("unknown instruction: 0x"+Number(t[e.ip]).toString(16));r(e)}}},St=[$t.bind(void 0,Xt),$t.bind(void 0,qt),er.bind(void 0,Xt),er.bind(void 0,qt),tr.bind(void 0,Xt),tr.bind(void 0,qt),rr.bind(void 0,0),rr.bind(void 0,1),ar.bind(void 0,0),ar.bind(void 0,1),function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"SPVFS[]",r,a),e.pv=e.dpv=Vt(a,r)},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"SPVFS[]",r,a),e.fv=Vt(a,r)},function(e){var t=e.stack,r=e.pv;E.DEBUG&&console.log(e.step,"GPV[]"),t.push(16384*r.x),t.push(16384*r.y)},function(e){var t=e.stack,r=e.fv;E.DEBUG&&console.log(e.step,"GFV[]"),t.push(16384*r.x),t.push(16384*r.y)},function(e){e.fv=e.pv,E.DEBUG&&console.log(e.step,"SFVTPV[]")},function(e){var t=e.stack,r=t.pop(),a=t.pop(),n=t.pop(),o=t.pop(),s=t.pop(),i=e.z0,l=e.z1,u=i[r],p=i[a],c=l[n],h=l[o],f=e.z2[s];E.DEBUG&&console.log("ISECT[], ",r,a,n,o,s);var d=u.x,v=u.y,g=p.x,m=p.y,y=c.x,b=c.y,S=h.x,x=h.y,U=(d-g)*(b-x)-(v-m)*(y-S),T=d*m-v*g,O=y*x-b*S;f.x=(T*(y-S)-O*(d-g))/U,f.y=(T*(b-x)-O*(v-m))/U},function(e){e.rp0=e.stack.pop(),E.DEBUG&&console.log(e.step,"SRP0[]",e.rp0)},function(e){e.rp1=e.stack.pop(),E.DEBUG&&console.log(e.step,"SRP1[]",e.rp1)},function(e){e.rp2=e.stack.pop(),E.DEBUG&&console.log(e.step,"SRP2[]",e.rp2)},function(e){var t=e.stack.pop();switch(E.DEBUG&&console.log(e.step,"SZP0[]",t),e.zp0=t){case 0:e.tZone||Kt(e),e.z0=e.tZone;break;case 1:e.z0=e.gZone;break;default:throw new Error("Invalid zone pointer")}},function(e){var t=e.stack.pop();switch(E.DEBUG&&console.log(e.step,"SZP1[]",t),e.zp1=t){case 0:e.tZone||Kt(e),e.z1=e.tZone;break;case 1:e.z1=e.gZone;break;default:throw new Error("Invalid zone pointer")}},function(e){var t=e.stack.pop();switch(E.DEBUG&&console.log(e.step,"SZP2[]",t),e.zp2=t){case 0:e.tZone||Kt(e),e.z2=e.tZone;break;case 1:e.z2=e.gZone;break;default:throw new Error("Invalid zone pointer")}},function(e){var t=e.stack.pop();switch(E.DEBUG&&console.log(e.step,"SZPS[]",t),e.zp0=e.zp1=e.zp2=t,t){case 0:e.tZone||Kt(e),e.z0=e.z1=e.z2=e.tZone;break;case 1:e.z0=e.z1=e.z2=e.gZone;break;default:throw new Error("Invalid zone pointer")}},function(e){e.loop=e.stack.pop(),E.DEBUG&&console.log(e.step,"SLOOP[]",e.loop)},function(e){E.DEBUG&&console.log(e.step,"RTG[]"),e.round=Pt},function(e){E.DEBUG&&console.log(e.step,"RTHG[]"),e.round=Ft},function(e){var t=e.stack.pop();E.DEBUG&&console.log(e.step,"SMD[]",t),e.minDis=t/64},function(e){E.DEBUG&&console.log(e.step,"ELSE[]"),Jt(e,!1)},function(e){var t=e.stack.pop();E.DEBUG&&console.log(e.step,"JMPR[]",t),e.ip+=t-1},function(e){var t=e.stack.pop();E.DEBUG&&console.log(e.step,"SCVTCI[]",t),e.cvCutIn=t/64},void 0,void 0,function(e){var t=e.stack;E.DEBUG&&console.log(e.step,"DUP[]"),t.push(t[t.length-1])},nr,function(e){E.DEBUG&&console.log(e.step,"CLEAR[]"),e.stack.length=0},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"SWAP[]"),t.push(r),t.push(a)},function(e){var t=e.stack;E.DEBUG&&console.log(e.step,"DEPTH[]"),t.push(t.length)},function(e){var t=e.stack,r=t.pop();E.DEBUG&&console.log(e.step,"CINDEX[]",r),t.push(t[t.length-r])},function(e){var t=e.stack,r=t.pop();E.DEBUG&&console.log(e.step,"MINDEX[]",r),t.push(t.splice(t.length-r,1)[0])},void 0,void 0,void 0,function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"LOOPCALL[]",r,a);var n=e.ip,o=e.prog;e.prog=e.funcs[r];for(var s=0;s<a;s++)xt(e),E.DEBUG&&console.log(++e.step,s+1<a?"next loopcall":"done loopcall",s);e.ip=n,e.prog=o},function(e){var t=e.stack.pop();E.DEBUG&&console.log(e.step,"CALL[]",t);var r=e.ip,a=e.prog;e.prog=e.funcs[t],xt(e),e.ip=r,e.prog=a,E.DEBUG&&console.log(++e.step,"returning from",t)},function(e){if("fpgm"!==e.env)throw new Error("FDEF not allowed here");var t=e.stack,r=e.prog,a=e.ip,n=t.pop(),o=a;for(E.DEBUG&&console.log(e.step,"FDEF[]",n);45!==r[++a];);e.ip=a,e.funcs[n]=r.slice(o+1,a)},void 0,or.bind(void 0,0),or.bind(void 0,1),sr.bind(void 0,Xt),sr.bind(void 0,qt),ir.bind(void 0,0),ir.bind(void 0,1),lr.bind(void 0,0),lr.bind(void 0,1),ur.bind(void 0,0),ur.bind(void 0,1),function(e){for(var t=e.stack,r=e.loop,a=e.fv,n=t.pop()/64,o=e.z2;r--;){var s=t.pop(),i=o[s];E.DEBUG&&console.log(e.step,(1<e.loop?"loop "+(e.loop-r)+": ":"")+"SHPIX[]",s,n),a.setRelative(i,i,n),a.touch(i)}e.loop=1},function(e){for(var t=e.stack,r=e.rp1,a=e.rp2,n=e.loop,o=e.z0[r],s=e.z1[a],i=e.fv,l=e.dpv,u=e.z2;n--;){var p=t.pop(),c=u[p];E.DEBUG&&console.log(e.step,(1<e.loop?"loop "+(e.loop-n)+": ":"")+"IP[]",p,r,"<->",a),i.interpolate(c,o,s,l),i.touch(c)}e.loop=1},pr.bind(void 0,0),pr.bind(void 0,1),function(e){for(var t=e.stack,r=e.rp0,a=e.z0[r],n=e.loop,o=e.fv,s=e.pv,i=e.z1;n--;){var l=t.pop(),u=i[l];E.DEBUG&&console.log(e.step,(1<e.loop?"loop "+(e.loop-n)+": ":"")+"ALIGNRP[]",l),o.setRelative(u,a,0,s),o.touch(u)}e.loop=1},function(e){E.DEBUG&&console.log(e.step,"RTDG[]"),e.round=At},cr.bind(void 0,0),cr.bind(void 0,1),function(e){var t=e.prog,r=e.ip,a=e.stack,n=t[++r];E.DEBUG&&console.log(e.step,"NPUSHB[]",n);for(var o=0;o<n;o++)a.push(t[++r]);e.ip=r},function(e){var t=e.ip,r=e.prog,a=e.stack,n=r[++t];E.DEBUG&&console.log(e.step,"NPUSHW[]",n);for(var o=0;o<n;o++){var s=r[++t]<<8|r[++t];32768&s&&(s=-(1+(65535^s))),a.push(s)}e.ip=t},function(e){var t=e.stack,r=e.store;r||(r=e.store=[]);var a=t.pop(),n=t.pop();E.DEBUG&&console.log(e.step,"WS",a,n),r[n]=a},function(e){var t=e.stack,r=e.store,a=t.pop();E.DEBUG&&console.log(e.step,"RS",a);var n=r&&r[a]||0;t.push(n)},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"WCVTP",r,a),e.cvt[a]=r/64},function(e){var t=e.stack,r=t.pop();E.DEBUG&&console.log(e.step,"RCVT",r),t.push(64*e.cvt[r])},hr.bind(void 0,0),hr.bind(void 0,1),void 0,fr.bind(void 0,0),fr.bind(void 0,1),function(e){E.DEBUG&&console.log(e.step,"MPPEM[]"),e.stack.push(e.ppem)},void 0,function(e){E.DEBUG&&console.log(e.step,"FLIPON[]"),e.autoFlip=!0},void 0,void 0,function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"LT[]",r,a),t.push(a<r?1:0)},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"LTEQ[]",r,a),t.push(a<=r?1:0)},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"GT[]",r,a),t.push(r<a?1:0)},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"GTEQ[]",r,a),t.push(r<=a?1:0)},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"EQ[]",r,a),t.push(r===a?1:0)},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"NEQ[]",r,a),t.push(r!==a?1:0)},function(e){var t=e.stack,r=t.pop();E.DEBUG&&console.log(e.step,"ODD[]",r),t.push(Math.trunc(r)%2?1:0)},function(e){var t=e.stack,r=t.pop();E.DEBUG&&console.log(e.step,"EVEN[]",r),t.push(Math.trunc(r)%2?0:1)},function(e){var t=e.stack.pop();E.DEBUG&&console.log(e.step,"IF[]",t),t||(Jt(e,!0),E.DEBUG&&console.log(e.step,"EIF[]"))},function(e){E.DEBUG&&console.log(e.step,"EIF[]")},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"AND[]",r,a),t.push(r&&a?1:0)},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"OR[]",r,a),t.push(r||a?1:0)},function(e){var t=e.stack,r=t.pop();E.DEBUG&&console.log(e.step,"NOT[]",r),t.push(r?0:1)},dr.bind(void 0,1),function(e){var t=e.stack.pop();E.DEBUG&&console.log(e.step,"SDB[]",t),e.deltaBase=t},function(e){var t=e.stack.pop();E.DEBUG&&console.log(e.step,"SDS[]",t),e.deltaShift=Math.pow(.5,t)},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"ADD[]",r,a),t.push(a+r)},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"SUB[]",r,a),t.push(a-r)},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"DIV[]",r,a),t.push(64*a/r)},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"MUL[]",r,a),t.push(a*r/64)},function(e){var t=e.stack,r=t.pop();E.DEBUG&&console.log(e.step,"ABS[]",r),t.push(Math.abs(r))},function(e){var t=e.stack,r=t.pop();E.DEBUG&&console.log(e.step,"NEG[]",r),t.push(-r)},function(e){var t=e.stack,r=t.pop();E.DEBUG&&console.log(e.step,"FLOOR[]",r),t.push(64*Math.floor(r/64))},function(e){var t=e.stack,r=t.pop();E.DEBUG&&console.log(e.step,"CEILING[]",r),t.push(64*Math.ceil(r/64))},vr.bind(void 0,0),vr.bind(void 0,1),vr.bind(void 0,2),vr.bind(void 0,3),void 0,void 0,void 0,void 0,function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"WCVTF[]",r,a),e.cvt[a]=r*e.ppem/e.font.unitsPerEm},dr.bind(void 0,2),dr.bind(void 0,3),gr.bind(void 0,1),gr.bind(void 0,2),gr.bind(void 0,3),function(e){var t,r=e.stack.pop();switch(E.DEBUG&&console.log(e.step,"SROUND[]",r),e.round=Wt,192&r){case 0:t=.5;break;case 64:t=1;break;case 128:t=2;break;default:throw new Error("invalid SROUND value")}switch(e.srPeriod=t,48&r){case 0:e.srPhase=0;break;case 16:e.srPhase=.25*t;break;case 32:e.srPhase=.5*t;break;case 48:e.srPhase=.75*t;break;default:throw new Error("invalid SROUND value")}r&=15,e.srThreshold=0===r?0:(r/8-.5)*t},function(e){var t,r=e.stack.pop();switch(E.DEBUG&&console.log(e.step,"S45ROUND[]",r),e.round=Wt,192&r){case 0:t=Math.sqrt(2)/2;break;case 64:t=Math.sqrt(2);break;case 128:t=2*Math.sqrt(2);break;default:throw new Error("invalid S45ROUND value")}switch(e.srPeriod=t,48&r){case 0:e.srPhase=0;break;case 16:e.srPhase=.25*t;break;case 32:e.srPhase=.5*t;break;case 48:e.srPhase=.75*t;break;default:throw new Error("invalid S45ROUND value")}r&=15,e.srThreshold=0===r?0:(r/8-.5)*t},void 0,void 0,function(e){E.DEBUG&&console.log(e.step,"ROFF[]"),e.round=Nt},void 0,function(e){E.DEBUG&&console.log(e.step,"RUTG[]"),e.round=Ht},function(e){E.DEBUG&&console.log(e.step,"RDTG[]"),e.round=zt},nr,nr,void 0,void 0,void 0,void 0,void 0,function(e){var t=e.stack.pop();E.DEBUG&&console.log(e.step,"SCANCTRL[]",t)},mr.bind(void 0,0),mr.bind(void 0,1),function(e){var t=e.stack,r=t.pop(),a=0;E.DEBUG&&console.log(e.step,"GETINFO[]",r),1&r&&(a=35),32&r&&(a|=4096),t.push(a)},void 0,function(e){var t=e.stack,r=t.pop(),a=t.pop(),n=t.pop();E.DEBUG&&console.log(e.step,"ROLL[]"),t.push(a),t.push(r),t.push(n)},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"MAX[]",r,a),t.push(Math.max(a,r))},function(e){var t=e.stack,r=t.pop(),a=t.pop();E.DEBUG&&console.log(e.step,"MIN[]",r,a),t.push(Math.min(a,r))},function(e){var t=e.stack.pop();E.DEBUG&&console.log(e.step,"SCANTYPE[]",t)},function(e){var t=e.stack.pop(),r=e.stack.pop();switch(E.DEBUG&&console.log(e.step,"INSTCTRL[]",t,r),t){case 1:return void(e.inhibitGridFit=!!r);case 2:return void(e.ignoreCvt=!!r);default:throw new Error("invalid INSTCTRL[] selector")}},void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,yr.bind(void 0,1),yr.bind(void 0,2),yr.bind(void 0,3),yr.bind(void 0,4),yr.bind(void 0,5),yr.bind(void 0,6),yr.bind(void 0,7),yr.bind(void 0,8),br.bind(void 0,1),br.bind(void 0,2),br.bind(void 0,3),br.bind(void 0,4),br.bind(void 0,5),br.bind(void 0,6),br.bind(void 0,7),br.bind(void 0,8),Sr.bind(void 0,0,0,0,0,0),Sr.bind(void 0,0,0,0,0,1),Sr.bind(void 0,0,0,0,0,2),Sr.bind(void 0,0,0,0,0,3),Sr.bind(void 0,0,0,0,1,0),Sr.bind(void 0,0,0,0,1,1),Sr.bind(void 0,0,0,0,1,2),Sr.bind(void 0,0,0,0,1,3),Sr.bind(void 0,0,0,1,0,0),Sr.bind(void 0,0,0,1,0,1),Sr.bind(void 0,0,0,1,0,2),Sr.bind(void 0,0,0,1,0,3),Sr.bind(void 0,0,0,1,1,0),Sr.bind(void 0,0,0,1,1,1),Sr.bind(void 0,0,0,1,1,2),Sr.bind(void 0,0,0,1,1,3),Sr.bind(void 0,0,1,0,0,0),Sr.bind(void 0,0,1,0,0,1),Sr.bind(void 0,0,1,0,0,2),Sr.bind(void 0,0,1,0,0,3),Sr.bind(void 0,0,1,0,1,0),Sr.bind(void 0,0,1,0,1,1),Sr.bind(void 0,0,1,0,1,2),Sr.bind(void 0,0,1,0,1,3),Sr.bind(void 0,0,1,1,0,0),Sr.bind(void 0,0,1,1,0,1),Sr.bind(void 0,0,1,1,0,2),Sr.bind(void 0,0,1,1,0,3),Sr.bind(void 0,0,1,1,1,0),Sr.bind(void 0,0,1,1,1,1),Sr.bind(void 0,0,1,1,1,2),Sr.bind(void 0,0,1,1,1,3),Sr.bind(void 0,1,0,0,0,0),Sr.bind(void 0,1,0,0,0,1),Sr.bind(void 0,1,0,0,0,2),Sr.bind(void 0,1,0,0,0,3),Sr.bind(void 0,1,0,0,1,0),Sr.bind(void 0,1,0,0,1,1),Sr.bind(void 0,1,0,0,1,2),Sr.bind(void 0,1,0,0,1,3),Sr.bind(void 0,1,0,1,0,0),Sr.bind(void 0,1,0,1,0,1),Sr.bind(void 0,1,0,1,0,2),Sr.bind(void 0,1,0,1,0,3),Sr.bind(void 0,1,0,1,1,0),Sr.bind(void 0,1,0,1,1,1),Sr.bind(void 0,1,0,1,1,2),Sr.bind(void 0,1,0,1,1,3),Sr.bind(void 0,1,1,0,0,0),Sr.bind(void 0,1,1,0,0,1),Sr.bind(void 0,1,1,0,0,2),Sr.bind(void 0,1,1,0,0,3),Sr.bind(void 0,1,1,0,1,0),Sr.bind(void 0,1,1,0,1,1),Sr.bind(void 0,1,1,0,1,2),Sr.bind(void 0,1,1,0,1,3),Sr.bind(void 0,1,1,1,0,0),Sr.bind(void 0,1,1,1,0,1),Sr.bind(void 0,1,1,1,0,2),Sr.bind(void 0,1,1,1,0,3),Sr.bind(void 0,1,1,1,1,0),Sr.bind(void 0,1,1,1,1,1),Sr.bind(void 0,1,1,1,1,2),Sr.bind(void 0,1,1,1,1,3)],xr.prototype.hasChar=function(e){return null!==this.encoding.charToGlyphIndex(e)},xr.prototype.charToGlyphIndex=function(e){return this.encoding.charToGlyphIndex(e)},xr.prototype.charToGlyph=function(e){var t=this.charToGlyphIndex(e),r=this.glyphs.get(t);return r||(r=this.glyphs.get(0)),r},xr.prototype.stringToGlyphs=function(e,t){t=t||this.defaultRenderOptions;for(var r=[],a=0;a<e.length;a+=1){var n=e[a];r.push(this.charToGlyphIndex(n))}var o=r.length;if(t.features){var s=t.script||this.substitution.getDefaultScriptName(),i=[];t.features.liga&&(i=i.concat(this.substitution.getFeature("liga",s,t.language))),t.features.rlig&&(i=i.concat(this.substitution.getFeature("rlig",s,t.language)));for(var l=0;l<o;l+=1)for(var u=0;u<i.length;u++){for(var p=i[u],c=p.sub,h=c.length,f=0;f<h&&c[f]===r[l+f];)f++;f===h&&(r.splice(l,h,p.by),o=o-h+1)}}for(var d=new Array(o),v=this.glyphs.get(0),g=0;g<o;g+=1)d[g]=this.glyphs.get(r[g])||v;return d},xr.prototype.nameToGlyphIndex=function(e){return this.glyphNames.nameToGlyphIndex(e)},xr.prototype.nameToGlyph=function(e){var t=this.nameToGlyphIndex(e),r=this.glyphs.get(t);return r||(r=this.glyphs.get(0)),r},xr.prototype.glyphIndexToName=function(e){return this.glyphNames.glyphIndexToName?this.glyphNames.glyphIndexToName(e):""},xr.prototype.getKerningValue=function(e,t){return e=e.index||e,t=t.index||t,this.kerningPairs[e+","+t]||0},xr.prototype.defaultRenderOptions={kerning:!0,features:{liga:!0,rlig:!0}},xr.prototype.forEachGlyph=function(e,t,r,a,n,o){t=void 0!==t?t:0,r=void 0!==r?r:0,a=void 0!==a?a:72,n=n||this.defaultRenderOptions;var s,i=1/this.unitsPerEm*a,l=this.stringToGlyphs(e,n);if(n.kerning){var u=n.script||this.position.getDefaultScriptName();s=this.position.getKerningTables(u,n.language)}for(var p=0;p<l.length;p+=1){var c=l[p];if(o.call(this,c,t,r,a,n),c.advanceWidth&&(t+=c.advanceWidth*i),n.kerning&&p<l.length-1)t+=(s?this.position.getKerningValue(s,c.index,l[p+1].index):this.getKerningValue(c,l[p+1]))*i;n.letterSpacing?t+=n.letterSpacing*a:n.tracking&&(t+=n.tracking/1e3*a)}return t},xr.prototype.getPath=function(e,t,r,a,o){var s=new M;return this.forEachGlyph(e,t,r,a,o,function(e,t,r,a){var n=e.getPath(t,r,a,o,this);s.extend(n)}),s},xr.prototype.getPaths=function(e,t,r,a,o){var s=[];return this.forEachGlyph(e,t,r,a,o,function(e,t,r,a){var n=e.getPath(t,r,a,o,this);s.push(n)}),s},xr.prototype.getAdvanceWidth=function(e,t,r){return this.forEachGlyph(e,0,0,t,r,function(){})},xr.prototype.draw=function(e,t,r,a,n,o){this.getPath(t,r,a,n,o).draw(e)},xr.prototype.drawPoints=function(n,e,t,r,a,o){this.forEachGlyph(e,t,r,a,o,function(e,t,r,a){e.drawPoints(n,t,r,a)})},xr.prototype.drawMetrics=function(n,e,t,r,a,o){this.forEachGlyph(e,t,r,a,o,function(e,t,r,a){e.drawMetrics(n,t,r,a)})},xr.prototype.getEnglishName=function(e){var t=this.names[e];if(t)return t.en},xr.prototype.validate=function(){var r=[],a=this;function n(e,t){e||r.push(t)}function e(e){var t=a.getEnglishName(e);n(t&&0<t.trim().length,"No English "+e+" specified.")}e("fontFamily"),e("weightName"),e("manufacturer"),e("copyright"),e("version"),n(0<this.unitsPerEm,"No unitsPerEm specified.")},xr.prototype.toTables=function(){return Ot.fontToTable(this)},xr.prototype.toBuffer=function(){return console.warn("Font.toBuffer is deprecated. Use Font.toArrayBuffer instead."),this.toArrayBuffer()},xr.prototype.toArrayBuffer=function(){for(var e=this.toTables().encode(),t=new ArrayBuffer(e.length),r=new Uint8Array(t),a=0;a<e.length;a++)r[a]=e[a];return t},xr.prototype.download=function(t){var e=this.getEnglishName("fontFamily"),r=this.getEnglishName("fontSubfamily");t=t||e.replace(/\s/g,"")+"-"+r+".otf";var n=this.toArrayBuffer();if("undefined"!=typeof window)window.requestFileSystem=window.requestFileSystem||window.webkitRequestFileSystem,window.requestFileSystem(window.TEMPORARY,n.byteLength,function(e){e.root.getFile(t,{create:!0},function(a){a.createWriter(function(e){var t=new DataView(n),r=new Blob([t],{type:"font/opentype"});e.write(r),e.addEventListener("writeend",function(){location.href=a.toURL()},!1)})})},function(e){throw new Error(e.name+": "+e.message)});else{var a=require("fs"),o=function(e){for(var t=new Buffer(e.byteLength),r=new Uint8Array(e),a=0;a<t.length;++a)t[a]=r[a];return t}(n);a.writeFileSync(t,o)}},xr.prototype.fsSelectionValues={ITALIC:1,UNDERSCORE:2,NEGATIVE:4,OUTLINED:8,STRIKEOUT:16,BOLD:32,REGULAR:64,USER_TYPO_METRICS:128,WWS:256,OBLIQUE:512},xr.prototype.usWidthClasses={ULTRA_CONDENSED:1,EXTRA_CONDENSED:2,CONDENSED:3,SEMI_CONDENSED:4,MEDIUM:5,SEMI_EXPANDED:6,EXPANDED:7,EXTRA_EXPANDED:8,ULTRA_EXPANDED:9},xr.prototype.usWeightClasses={THIN:100,EXTRA_LIGHT:200,LIGHT:300,NORMAL:400,MEDIUM:500,SEMI_BOLD:600,BOLD:700,EXTRA_BOLD:800,BLACK:900};var Er={make:function(e,t){var r,a,n,o,s=new Q.Table("fvar",[{name:"version",type:"ULONG",value:65536},{name:"offsetToData",type:"USHORT",value:0},{name:"countSizePairs",type:"USHORT",value:2},{name:"axisCount",type:"USHORT",value:e.axes.length},{name:"axisSize",type:"USHORT",value:20},{name:"instanceCount",type:"USHORT",value:e.instances.length},{name:"instanceSize",type:"USHORT",value:4+4*e.axes.length}]);s.offsetToData=s.sizeOf();for(var i=0;i<e.axes.length;i++)s.fields=s.fields.concat((r=i,a=e.axes[i],n=t,o=Ur(a.name,n),[{name:"tag_"+r,type:"TAG",value:a.tag},{name:"minValue_"+r,type:"FIXED",value:a.minValue<<16},{name:"defaultValue_"+r,type:"FIXED",value:a.defaultValue<<16},{name:"maxValue_"+r,type:"FIXED",value:a.maxValue<<16},{name:"flags_"+r,type:"USHORT",value:0},{name:"nameID_"+r,type:"USHORT",value:o}]));for(var l=0;l<e.instances.length;l++)s.fields=s.fields.concat(Tr(l,e.instances[l],e.axes,t));return s},parse:function(e,t,r){var a=new ne.Parser(e,t),n=a.parseULong();L.argument(65536===n,"Unsupported fvar table version.");var o=a.parseOffset16();a.skip("uShort",1);for(var s,i,l,u,p,c=a.parseUShort(),h=a.parseUShort(),f=a.parseUShort(),d=a.parseUShort(),v=[],g=0;g<c;g++)v.push((s=e,i=t+o+g*h,l=r,p=u=void 0,u={},p=new ne.Parser(s,i),u.tag=p.parseTag(),u.minValue=p.parseFixed(),u.defaultValue=p.parseFixed(),u.maxValue=p.parseFixed(),p.skip("uShort",1),u.name=l[p.parseUShort()]||{},u));for(var m=[],y=t+o+c*h,b=0;b<f;b++)m.push(Or(e,y+b*d,v,r));return{axes:v,instances:m}}},kr=new Array(10);kr[1]=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort();return 1===t?{posFormat:1,coverage:this.parsePointer(re.coverage),value:this.parseValueRecord()}:2===t?{posFormat:2,coverage:this.parsePointer(re.coverage),values:this.parseValueRecordList()}:void L.assert(!1,"0x"+e.toString(16)+": GPOS lookup type 1 format must be 1 or 2.")},kr[2]=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort(),r=this.parsePointer(re.coverage),a=this.parseUShort(),n=this.parseUShort();if(1===t)return{posFormat:t,coverage:r,valueFormat1:a,valueFormat2:n,pairSets:this.parseList(re.pointer(re.list(function(){return{secondGlyph:this.parseUShort(),value1:this.parseValueRecord(a),value2:this.parseValueRecord(n)}})))};if(2===t){var o=this.parsePointer(re.classDef),s=this.parsePointer(re.classDef),i=this.parseUShort(),l=this.parseUShort();return{posFormat:t,coverage:r,valueFormat1:a,valueFormat2:n,classDef1:o,classDef2:s,class1Count:i,class2Count:l,classRecords:this.parseList(i,re.list(l,function(){return{value1:this.parseValueRecord(a),value2:this.parseValueRecord(n)}}))}}L.assert(!1,"0x"+e.toString(16)+": GPOS lookup type 2 format must be 1 or 2.")},kr[3]=function(){return{error:"GPOS Lookup 3 not supported"}},kr[4]=function(){return{error:"GPOS Lookup 4 not supported"}},kr[5]=function(){return{error:"GPOS Lookup 5 not supported"}},kr[6]=function(){return{error:"GPOS Lookup 6 not supported"}},kr[7]=function(){return{error:"GPOS Lookup 7 not supported"}},kr[8]=function(){return{error:"GPOS Lookup 8 not supported"}},kr[9]=function(){return{error:"GPOS Lookup 9 not supported"}};var Rr=new Array(10);var Lr={parse:function(e,t){var r=new re(e,t=t||0),a=r.parseVersion(1);return L.argument(1===a||1.1===a,"Unsupported GPOS table version "+a),1===a?{version:a,scripts:r.parseScriptList(),features:r.parseFeatureList(),lookups:r.parseLookupList(kr)}:{version:a,scripts:r.parseScriptList(),features:r.parseFeatureList(),lookups:r.parseLookupList(kr),variations:r.parseFeatureVariationsList()}},make:function(e){return new Q.Table("GPOS",[{name:"version",type:"ULONG",value:65536},{name:"scripts",type:"TABLE",value:new Q.ScriptList(e.scripts)},{name:"features",type:"TABLE",value:new Q.FeatureList(e.features)},{name:"lookups",type:"TABLE",value:new Q.LookupList(e.lookups,Rr)}])}};var Dr={parse:function(e,t){var r=new ne.Parser(e,t),a=r.parseUShort();if(0===a)return function(e){var t={};e.skip("uShort");var r=e.parseUShort();L.argument(0===r,"Unsupported kern sub-table version."),e.skip("uShort",2);var a=e.parseUShort();e.skip("uShort",3);for(var n=0;n<a;n+=1){var o=e.parseUShort(),s=e.parseUShort(),i=e.parseShort();t[o+","+s]=i}return t}(r);if(1===a)return function(e){var t={};e.skip("uShort"),1<e.parseULong()&&console.warn("Only the first kern subtable is supported."),e.skip("uLong");var r=255&e.parseUShort();if(e.skip("uShort"),0===r){var a=e.parseUShort();e.skip("uShort",3);for(var n=0;n<a;n+=1){var o=e.parseUShort(),s=e.parseUShort(),i=e.parseShort();t[o+","+s]=i}}return t}(r);throw new Error("Unsupported kern table version ("+a+").")}};var wr={parse:function(e,t,r,a){for(var n=new ne.Parser(e,t),o=a?n.parseUShort:n.parseULong,s=[],i=0;i<r+1;i+=1){var l=o.call(n);a&&(l*=2),s.push(l)}return s}};function Cr(e,r){require("fs").readFile(e,function(e,t){if(e)return r(e.message);r(null,It(t))})}function Gr(e,t){var r=new XMLHttpRequest;r.open("get",e,!0),r.responseType="arraybuffer",r.onload=function(){return r.response?t(null,r.response):t("Font could not be loaded: "+r.statusText)},r.onerror=function(){t("Font could not be loaded")},r.send()}function Ir(e,t){for(var r=[],a=12,n=0;n<t;n+=1){var o=ne.getTag(e,a),s=ne.getULong(e,a+4),i=ne.getULong(e,a+8),l=ne.getULong(e,a+12);r.push({tag:o,checksum:s,offset:i,length:l,compression:!1}),a+=16}return r}function Br(e,t){if("WOFF"===t.compression){var r=new Uint8Array(e.buffer,t.offset+2,t.compressedLength-2),a=new Uint8Array(t.length);if(n(r,a),a.byteLength!==t.length)throw new Error("Decompression error: "+t.tag+" decompressed length doesn't match recorded length");return{data:new DataView(a.buffer,0),offset:0}}return{data:e,offset:t.offset}}function Mr(e){var t,r,a,n,o,s,i,l,u,p,c,h,f,d,v=new xr({empty:!0}),g=new DataView(e,0),m=[],y=ne.getTag(g,0);if(y===String.fromCharCode(0,1,0,0)||"true"===y||"typ1"===y)v.outlinesFormat="truetype",m=Ir(g,a=ne.getUShort(g,4));else if("OTTO"===y)v.outlinesFormat="cff",m=Ir(g,a=ne.getUShort(g,4));else{if("wOFF"!==y)throw new Error("Unsupported OpenType signature "+y);var b=ne.getTag(g,4);if(b===String.fromCharCode(0,1,0,0))v.outlinesFormat="truetype";else{if("OTTO"!==b)throw new Error("Unsupported OpenType flavor "+y);v.outlinesFormat="cff"}m=function(e,t){for(var r=[],a=44,n=0;n<t;n+=1){var o=ne.getTag(e,a),s=ne.getULong(e,a+4),i=ne.getULong(e,a+8),l=ne.getULong(e,a+12),u=void 0;u=i<l&&"WOFF",r.push({tag:o,offset:s,compression:u,compressedLength:i,length:l}),a+=20}return r}(g,a=ne.getUShort(g,12))}for(var S=0;S<a;S+=1){var x=m[S],U=void 0;switch(x.tag){case"cmap":U=Br(g,x),v.tables.cmap=oe.parse(U.data,U.offset),v.encoding=new ce(v.tables.cmap);break;case"cvt ":U=Br(g,x),d=new ne.Parser(U.data,U.offset),v.tables.cvt=d.parseShortList(x.length/2);break;case"fvar":o=x;break;case"fpgm":U=Br(g,x),d=new ne.Parser(U.data,U.offset),v.tables.fpgm=d.parseByteList(x.length);break;case"head":U=Br(g,x),v.tables.head=qe.parse(U.data,U.offset),v.unitsPerEm=v.tables.head.unitsPerEm,t=v.tables.head.indexToLocFormat;break;case"hhea":U=Br(g,x),v.tables.hhea=Xe.parse(U.data,U.offset),v.ascender=v.tables.hhea.ascender,v.descender=v.tables.hhea.descender,v.numberOfHMetrics=v.tables.hhea.numberOfHMetrics;break;case"hmtx":u=x;break;case"ltag":U=Br(g,x),r=Ve.parse(U.data,U.offset);break;case"maxp":U=Br(g,x),v.tables.maxp=Ye.parse(U.data,U.offset),v.numGlyphs=v.tables.maxp.numGlyphs;break;case"name":h=x;break;case"OS/2":U=Br(g,x),v.tables.os2=lt.parse(U.data,U.offset);break;case"post":U=Br(g,x),v.tables.post=ut.parse(U.data,U.offset),v.glyphNames=new fe(v.tables.post);break;case"prep":U=Br(g,x),d=new ne.Parser(U.data,U.offset),v.tables.prep=d.parseByteList(x.length);break;case"glyf":s=x;break;case"loca":c=x;break;case"CFF ":n=x;break;case"kern":p=x;break;case"GPOS":i=x;break;case"GSUB":l=x;break;case"meta":f=x}}var T=Br(g,h);if(v.tables.name=st.parse(T.data,T.offset,r),v.names=v.tables.name,s&&c){var O=0===t,E=Br(g,c),k=wr.parse(E.data,E.offset,v.numGlyphs,O),R=Br(g,s);v.glyphs=Se.parse(R.data,R.offset,k,v)}else{if(!n)throw new Error("Font doesn't contain TrueType or CFF outlines.");var L=Br(g,n);We.parse(L.data,L.offset,v)}var D=Br(g,u);if(_e.parse(D.data,D.offset,v.numberOfHMetrics,v.numGlyphs,v.glyphs),function(e){for(var t,r=e.tables.cmap.glyphIndexMap,a=Object.keys(r),n=0;n<a.length;n+=1){var o=a[n],s=r[o];(t=e.glyphs.get(s)).addUnicode(parseInt(o))}for(var i=0;i<e.glyphs.length;i+=1)t=e.glyphs.get(i),e.cffEncoding?e.isCIDFont?t.name="gid"+i:t.name=e.cffEncoding.charset[i]:e.glyphNames.names&&(t.name=e.glyphNames.glyphIndexToName(i))}(v),p){var w=Br(g,p);v.kerningPairs=Dr.parse(w.data,w.offset)}else v.kerningPairs={};if(i){var C=Br(g,i);v.tables.gpos=Lr.parse(C.data,C.offset)}if(l){var G=Br(g,l);v.tables.gsub=ft.parse(G.data,G.offset)}if(o){var I=Br(g,o);v.tables.fvar=Er.parse(I.data,I.offset,v.names)}if(f){var B=Br(g,f);v.tables.meta=dt.parse(B.data,B.offset),v.metas=v.tables.meta}return v}E.Font=xr,E.Glyph=xe,E.Path=M,E.BoundingBox=R,E._parse=ne,E.parse=Mr,E.load=function(e,a){("undefined"==typeof window?Cr:Gr)(e,function(e,t){if(e)return a(e);var r;try{r=Mr(t)}catch(e){return a(e,null)}return a(null,r)})},E.loadSync=function(e){return Mr(It(require("fs").readFileSync(e)))},Object.defineProperty(E,"__esModule",{value:!0})});//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/9df2e0f1ef56cd7bcef40a92d8f4f46ca576ee3f/node_modules/opentype.js/dist/opentype.min.js.map