{"name": "opentype.js", "description": "OpenType font parser", "version": "0.8.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["graphics", "fonts", "opentype", "otf", "ttf", "woff", "type"], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/nodebox/opentype.js.git"}, "main": "dist/opentype.js", "module": "src/opentype.js", "bin": {"ot": "./bin/ot"}, "scripts": {"start": "node ./bin/server.js", "watch": "rollup -c -w", "test": "mocha --require reify --compilers js:buble/register --recursive && jshint . && jscs .", "build": "rollup -c", "minify": "uglifyjs --source-map \"url='opentype.min.js.map'\" --compress --mangle --output ./dist/opentype.min.js -- ./dist/opentype.js", "dist": "npm run test && npm run build && npm run minify"}, "devDependencies": {"buble": "^0.15.2", "cross-env": "^5.0.0", "jscs": "^3.0.3", "jshint": "^2.9.2", "mocha": "^2.5.3", "reify": "^0.11.0", "rollup": "^0.41.6", "rollup-plugin-buble": "^0.15.0", "rollup-plugin-commonjs": "^8.0.2", "rollup-plugin-license": "^0.4.0", "rollup-plugin-node-resolve": "^3.0.0", "rollup-watch": "^3.2.2", "uglify-js": "^3.3.10"}, "browser": {"fs": false}, "dependencies": {"tiny-inflate": "^1.0.2"}}