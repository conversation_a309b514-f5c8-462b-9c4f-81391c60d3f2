/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import assert from 'assert';
import { EnvironmentMainService } from '../../electron-main/environmentMainService.js';
import product from '../../../product/common/product.js';
import { isLinux } from '../../../../base/common/platform.js';
import { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../base/test/common/utils.js';
suite('EnvironmentMainService', () => {
    test('can unset and restore snap env variables', () => {
        const service = new EnvironmentMainService({ '_': [] }, { '_serviceBrand': undefined, ...product });
        process.env['TEST_ARG1_VSCODE_SNAP_ORIG'] = 'original';
        process.env['TEST_ARG1'] = 'modified';
        process.env['TEST_ARG2_SNAP'] = 'test_arg2';
        process.env['TEST_ARG3_VSCODE_SNAP_ORIG'] = '';
        process.env['TEST_ARG3'] = 'test_arg3_non_empty';
        // Unset snap env variables
        service.unsetSnapExportedVariables();
        if (isLinux) {
            assert.strictEqual(process.env['TEST_ARG1'], 'original');
            assert.strictEqual(process.env['TEST_ARG2'], undefined);
            assert.strictEqual(process.env['TEST_ARG1_VSCODE_SNAP_ORIG'], 'original');
            assert.strictEqual(process.env['TEST_ARG2_SNAP'], 'test_arg2');
            assert.strictEqual(process.env['TEST_ARG3_VSCODE_SNAP_ORIG'], '');
            assert.strictEqual(process.env['TEST_ARG3'], undefined);
        }
        else {
            assert.strictEqual(process.env['TEST_ARG1'], 'modified');
            assert.strictEqual(process.env['TEST_ARG2'], undefined);
            assert.strictEqual(process.env['TEST_ARG1_VSCODE_SNAP_ORIG'], 'original');
            assert.strictEqual(process.env['TEST_ARG2_SNAP'], 'test_arg2');
            assert.strictEqual(process.env['TEST_ARG3_VSCODE_SNAP_ORIG'], '');
            assert.strictEqual(process.env['TEST_ARG3'], 'test_arg3_non_empty');
        }
        // Restore snap env variables
        service.restoreSnapExportedVariables();
        if (isLinux) {
            assert.strictEqual(process.env['TEST_ARG1'], 'modified');
            assert.strictEqual(process.env['TEST_ARG1_VSCODE_SNAP_ORIG'], 'original');
            assert.strictEqual(process.env['TEST_ARG2_SNAP'], 'test_arg2');
            assert.strictEqual(process.env['TEST_ARG2'], undefined);
            assert.strictEqual(process.env['TEST_ARG3_VSCODE_SNAP_ORIG'], '');
            assert.strictEqual(process.env['TEST_ARG3'], 'test_arg3_non_empty');
        }
        else {
            assert.strictEqual(process.env['TEST_ARG1'], 'modified');
            assert.strictEqual(process.env['TEST_ARG1_VSCODE_SNAP_ORIG'], 'original');
            assert.strictEqual(process.env['TEST_ARG2_SNAP'], 'test_arg2');
            assert.strictEqual(process.env['TEST_ARG2'], undefined);
            assert.strictEqual(process.env['TEST_ARG3_VSCODE_SNAP_ORIG'], '');
            assert.strictEqual(process.env['TEST_ARG3'], 'test_arg3_non_empty');
        }
    });
    test('can invoke unsetSnapExportedVariables and restoreSnapExportedVariables multiple times', () => {
        const service = new EnvironmentMainService({ '_': [] }, { '_serviceBrand': undefined, ...product });
        // Mock snap environment
        process.env['SNAP'] = '1';
        process.env['SNAP_REVISION'] = 'test_revision';
        process.env['TEST_ARG1_VSCODE_SNAP_ORIG'] = 'original';
        process.env['TEST_ARG1'] = 'modified';
        process.env['TEST_ARG2_SNAP'] = 'test_arg2';
        process.env['TEST_ARG3_VSCODE_SNAP_ORIG'] = '';
        process.env['TEST_ARG3'] = 'test_arg3_non_empty';
        // Unset snap env variables
        service.unsetSnapExportedVariables();
        service.unsetSnapExportedVariables();
        service.unsetSnapExportedVariables();
        if (isLinux) {
            assert.strictEqual(process.env['TEST_ARG1'], 'original');
            assert.strictEqual(process.env['TEST_ARG2'], undefined);
            assert.strictEqual(process.env['TEST_ARG1_VSCODE_SNAP_ORIG'], 'original');
            assert.strictEqual(process.env['TEST_ARG2_SNAP'], 'test_arg2');
            assert.strictEqual(process.env['TEST_ARG3_VSCODE_SNAP_ORIG'], '');
            assert.strictEqual(process.env['TEST_ARG3'], undefined);
        }
        else {
            assert.strictEqual(process.env['TEST_ARG1'], 'modified');
            assert.strictEqual(process.env['TEST_ARG2'], undefined);
            assert.strictEqual(process.env['TEST_ARG1_VSCODE_SNAP_ORIG'], 'original');
            assert.strictEqual(process.env['TEST_ARG2_SNAP'], 'test_arg2');
            assert.strictEqual(process.env['TEST_ARG3_VSCODE_SNAP_ORIG'], '');
            assert.strictEqual(process.env['TEST_ARG3'], 'test_arg3_non_empty');
        }
        // Restore snap env variables
        service.restoreSnapExportedVariables();
        service.restoreSnapExportedVariables();
        if (isLinux) {
            assert.strictEqual(process.env['TEST_ARG1'], 'modified');
            assert.strictEqual(process.env['TEST_ARG1_VSCODE_SNAP_ORIG'], 'original');
            assert.strictEqual(process.env['TEST_ARG2_SNAP'], 'test_arg2');
            assert.strictEqual(process.env['TEST_ARG2'], undefined);
            assert.strictEqual(process.env['TEST_ARG3_VSCODE_SNAP_ORIG'], '');
            assert.strictEqual(process.env['TEST_ARG3'], 'test_arg3_non_empty');
        }
        else {
            assert.strictEqual(process.env['TEST_ARG1'], 'modified');
            assert.strictEqual(process.env['TEST_ARG1_VSCODE_SNAP_ORIG'], 'original');
            assert.strictEqual(process.env['TEST_ARG2_SNAP'], 'test_arg2');
            assert.strictEqual(process.env['TEST_ARG2'], undefined);
            assert.strictEqual(process.env['TEST_ARG3_VSCODE_SNAP_ORIG'], '');
            assert.strictEqual(process.env['TEST_ARG3'], 'test_arg3_non_empty');
        }
        // Unset snap env variables
        service.unsetSnapExportedVariables();
        if (isLinux) {
            assert.strictEqual(process.env['TEST_ARG1'], 'original');
            assert.strictEqual(process.env['TEST_ARG2'], undefined);
            assert.strictEqual(process.env['TEST_ARG1_VSCODE_SNAP_ORIG'], 'original');
            assert.strictEqual(process.env['TEST_ARG2_SNAP'], 'test_arg2');
            assert.strictEqual(process.env['TEST_ARG3_VSCODE_SNAP_ORIG'], '');
            assert.strictEqual(process.env['TEST_ARG3'], undefined);
        }
        else {
            assert.strictEqual(process.env['TEST_ARG1'], 'modified');
            assert.strictEqual(process.env['TEST_ARG2'], undefined);
            assert.strictEqual(process.env['TEST_ARG1_VSCODE_SNAP_ORIG'], 'original');
            assert.strictEqual(process.env['TEST_ARG2_SNAP'], 'test_arg2');
            assert.strictEqual(process.env['TEST_ARG3_VSCODE_SNAP_ORIG'], '');
            assert.strictEqual(process.env['TEST_ARG3'], 'test_arg3_non_empty');
        }
    });
    ensureNoDisposablesAreLeakedInTestSuite();
});
//# sourceMappingURL=data:application/json;base64,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