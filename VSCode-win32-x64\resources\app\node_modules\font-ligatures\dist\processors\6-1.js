"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const coverage_1 = require("./coverage");
const helper_1 = require("./helper");
/**
 * Build lookup tree for GSUB lookup table 6, format 1.
 * https://docs.microsoft.com/en-us/typography/opentype/spec/gsub#61-chaining-context-substitution-format-1-simple-glyph-contexts
 *
 * @param table JSON representation of the table
 * @param lookups List of lookup tables
 * @param tableIndex Index of this table in the overall lookup
 */
function buildTree(table, lookups, tableIndex) {
    const result = {
        individual: {},
        range: []
    };
    const firstGlyphs = coverage_1.listGlyphsByIndex(table.coverage);
    for (const { glyphId, index } of firstGlyphs) {
        const chainRuleSet = table.chainRuleSets[index];
        // If the chain rule set is null there's nothing to do with this table.
        if (!chainRuleSet) {
            continue;
        }
        for (const [subIndex, subTable] of chainRuleSet.entries()) {
            let currentEntries = helper_1.getInputTree(result, subTable.lookupRecords, lookups, 0, glyphId).map(({ entry, substitution }) => ({ entry, substitutions: [substitution] }));
            // We walk forward, then backward
            for (const [index, glyph] of subTable.input.entries()) {
                currentEntries = helper_1.processInputPosition([glyph], index + 1, currentEntries, subTable.lookupRecords, lookups);
            }
            for (const glyph of subTable.lookahead) {
                currentEntries = helper_1.processLookaheadPosition([glyph], currentEntries);
            }
            for (const glyph of subTable.backtrack) {
                currentEntries = helper_1.processBacktrackPosition([glyph], currentEntries);
            }
            // When we get to the end, insert the lookup information
            for (const { entry, substitutions } of currentEntries) {
                entry.lookup = {
                    substitutions,
                    length: subTable.input.length + 1,
                    index: tableIndex,
                    subIndex,
                    contextRange: [
                        -1 * subTable.backtrack.length,
                        1 + subTable.input.length + subTable.lookahead.length
                    ]
                };
            }
        }
    }
    return result;
}
exports.default = buildTree;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/9df2e0f1ef56cd7bcef40a92d8f4f46ca576ee3f/node_modules/font-ligatures/dist/processors/6-1.js.map