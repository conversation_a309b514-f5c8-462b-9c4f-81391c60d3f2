{"name": "font-ligatures", "version": "1.4.1", "description": "Find ligature replacements for any system font", "homepage": "https://github.com/princjef/font-ligatures#readme", "repository": {"type": "git", "url": "https://github.com/princjef/font-ligatures.git"}, "bugs": {"url": "https://github.com/princjef/font-ligatures/issues"}, "engines": {"node": ">8.0.0"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"commit": "commit", "commitmsg": "commitlint -e $GIT_PARAMS", "commitlint": "commitlint", "lint": "tslint --project tsconfig.json --fix", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean && npm run lint", "build": "tsc", "watch": "tsc -w", "pretest": "npm run build", "test": "nyc ava dist/**/*.spec.js", "pretest-nocover": "npm run build", "test-nocover": "ava dist/**/*.spec.js", "show-coverage": "opener ./coverage/index.html", "coverage": "nyc report --reporter=text-lcov > coverage.lcov", "prerelease": "npm run build", "release": "semantic-release", "bench": "node --expose-gc bench/all.js"}, "keywords": ["font", "opentype", "truetype", "ligature"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"font-finder": "^1.0.3", "lru-cache": "^6.0.0", "opentype.js": "^0.8.0"}, "devDependencies": {"@commitlint/cli": "^9.1.2", "@commitlint/config-conventional": "^9.1.2", "@commitlint/prompt-cli": "^9.1.2", "@semantic-release/changelog": "^2.1.2", "@semantic-release/git": "^4.0.3", "@semantic-release/github": "^4.4.2", "@semantic-release/npm": "^3.4.1", "@types/lru-cache": "^5.1.0", "@types/node": "^8.10.10", "@types/opentype.js": "^0.7.0", "@types/sinon": "^4.3.1", "ava": "^0.25.0", "chalk": "^2.4.1", "codecov": "^3.0.1", "columnify": "^1.5.4", "husky": "^0.14.3", "nyc": "^15.1.0", "opener": "^1.4.3", "pretty-bytes": "^5.0.0", "rimraf": "^2.6.2", "semantic-release": "^15.9.3", "simple-statistics": "^6.0.0", "sinon": "^4.5.0", "tslint": "^5.9.1", "tslint-config-standard": "^7.0.0", "typescript": "^2.8.3"}, "nyc": {"sourceMap": true, "reporter": ["text", "html", "json"], "include": ["src/**/*.ts"], "exclude": ["src/**/*.spec.ts"], "check-coverage": true, "lines": 85, "statements": 85, "functions": 85, "branches": 75, "watermarks": {"lines": [80, 90], "statements": [80, 90], "functions": [80, 90], "branches": [80, 90]}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release": {"verifyConditions": ["@semantic-release/changelog", "@semantic-release/github", "@semantic-release/npm", "@semantic-release/git"], "prepare": [{"path": "@semantic-release/changelog", "changelogFile": "CHANGELOG.md"}, "@semantic-release/npm", "@semantic-release/git"], "publish": ["@semantic-release/npm", "@semantic-release/github"], "success": ["@semantic-release/github"], "fail": ["@semantic-release/github"]}}