/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import assert from 'assert';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../base/test/common/utils.js';
import { TextAreaState } from '../../../browser/controller/editContext/textArea/textAreaEditContextState.js';
import { Range } from '../../../common/core/range.js';
import { Selection } from '../../../common/core/selection.js';
import { createTextModel } from '../../common/testTextModel.js';
import { SimplePagedScreenReaderStrategy } from '../../../browser/controller/editContext/screenReaderUtils.js';
class MockTextAreaWrapper extends Disposable {
    constructor() {
        super();
        this._value = '';
        this._selectionStart = 0;
        this._selectionEnd = 0;
    }
    getValue() {
        return this._value;
    }
    setValue(reason, value) {
        this._value = value;
        this._selectionStart = this._value.length;
        this._selectionEnd = this._value.length;
    }
    getSelectionStart() {
        return this._selectionStart;
    }
    getSelectionEnd() {
        return this._selectionEnd;
    }
    setSelectionRange(reason, selectionStart, selectionEnd) {
        if (selectionStart < 0) {
            selectionStart = 0;
        }
        if (selectionStart > this._value.length) {
            selectionStart = this._value.length;
        }
        if (selectionEnd < 0) {
            selectionEnd = 0;
        }
        if (selectionEnd > this._value.length) {
            selectionEnd = this._value.length;
        }
        this._selectionStart = selectionStart;
        this._selectionEnd = selectionEnd;
    }
}
function equalsTextAreaState(a, b) {
    return (a.value === b.value
        && a.selectionStart === b.selectionStart
        && a.selectionEnd === b.selectionEnd
        && Range.equalsRange(a.selection, b.selection)
        && a.newlineCountBeforeSelection === b.newlineCountBeforeSelection);
}
suite('TextAreaState', () => {
    ensureNoDisposablesAreLeakedInTestSuite();
    function assertTextAreaState(actual, value, selectionStart, selectionEnd) {
        const desired = new TextAreaState(value, selectionStart, selectionEnd, null, undefined);
        assert.ok(equalsTextAreaState(desired, actual), desired.toString() + ' == ' + actual.toString());
    }
    test('fromTextArea', () => {
        const textArea = new MockTextAreaWrapper();
        textArea._value = 'Hello world!';
        textArea._selectionStart = 1;
        textArea._selectionEnd = 12;
        let actual = TextAreaState.readFromTextArea(textArea, null);
        assertTextAreaState(actual, 'Hello world!', 1, 12);
        assert.strictEqual(actual.value, 'Hello world!');
        assert.strictEqual(actual.selectionStart, 1);
        actual = actual.collapseSelection();
        assertTextAreaState(actual, 'Hello world!', 12, 12);
        textArea.dispose();
    });
    test('applyToTextArea', () => {
        const textArea = new MockTextAreaWrapper();
        textArea._value = 'Hello world!';
        textArea._selectionStart = 1;
        textArea._selectionEnd = 12;
        let state = new TextAreaState('Hi world!', 2, 2, null, undefined);
        state.writeToTextArea('test', textArea, false);
        assert.strictEqual(textArea._value, 'Hi world!');
        assert.strictEqual(textArea._selectionStart, 9);
        assert.strictEqual(textArea._selectionEnd, 9);
        state = new TextAreaState('Hi world!', 3, 3, null, undefined);
        state.writeToTextArea('test', textArea, false);
        assert.strictEqual(textArea._value, 'Hi world!');
        assert.strictEqual(textArea._selectionStart, 9);
        assert.strictEqual(textArea._selectionEnd, 9);
        state = new TextAreaState('Hi world!', 0, 2, null, undefined);
        state.writeToTextArea('test', textArea, true);
        assert.strictEqual(textArea._value, 'Hi world!');
        assert.strictEqual(textArea._selectionStart, 0);
        assert.strictEqual(textArea._selectionEnd, 2);
        textArea.dispose();
    });
    function testDeduceInput(prevState, value, selectionStart, selectionEnd, couldBeEmojiInput, expected, expectedCharReplaceCnt) {
        prevState = prevState || TextAreaState.EMPTY;
        const textArea = new MockTextAreaWrapper();
        textArea._value = value;
        textArea._selectionStart = selectionStart;
        textArea._selectionEnd = selectionEnd;
        const newState = TextAreaState.readFromTextArea(textArea, null);
        const actual = TextAreaState.deduceInput(prevState, newState, couldBeEmojiInput);
        assert.deepStrictEqual(actual, {
            text: expected,
            replacePrevCharCnt: expectedCharReplaceCnt,
            replaceNextCharCnt: 0,
            positionDelta: 0,
        });
        textArea.dispose();
    }
    test('extractNewText - no previous state with selection', () => {
        testDeduceInput(null, 'a', 0, 1, true, 'a', 0);
    });
    test('issue #2586: Replacing selected end-of-line with newline locks up the document', () => {
        testDeduceInput(new TextAreaState(']\n', 1, 2, null, undefined), ']\n', 2, 2, true, '\n', 0);
    });
    test('extractNewText - no previous state without selection', () => {
        testDeduceInput(null, 'a', 1, 1, true, 'a', 0);
    });
    test('extractNewText - typing does not cause a selection', () => {
        testDeduceInput(TextAreaState.EMPTY, 'a', 0, 1, true, 'a', 0);
    });
    test('extractNewText - had the textarea empty', () => {
        testDeduceInput(TextAreaState.EMPTY, 'a', 1, 1, true, 'a', 0);
    });
    test('extractNewText - had the entire line selected', () => {
        testDeduceInput(new TextAreaState('Hello world!', 0, 12, null, undefined), 'H', 1, 1, true, 'H', 0);
    });
    test('extractNewText - had previous text 1', () => {
        testDeduceInput(new TextAreaState('Hello world!', 12, 12, null, undefined), 'Hello world!a', 13, 13, true, 'a', 0);
    });
    test('extractNewText - had previous text 2', () => {
        testDeduceInput(new TextAreaState('Hello world!', 0, 0, null, undefined), 'aHello world!', 1, 1, true, 'a', 0);
    });
    test('extractNewText - had previous text 3', () => {
        testDeduceInput(new TextAreaState('Hello world!', 6, 11, null, undefined), 'Hello other!', 11, 11, true, 'other', 0);
    });
    test('extractNewText - IME', () => {
        testDeduceInput(TextAreaState.EMPTY, 'これは', 3, 3, true, 'これは', 0);
    });
    test('extractNewText - isInOverwriteMode', () => {
        testDeduceInput(new TextAreaState('Hello world!', 0, 0, null, undefined), 'Aello world!', 1, 1, true, 'A', 0);
    });
    test('extractMacReplacedText - does nothing if there is selection', () => {
        testDeduceInput(new TextAreaState('Hello world!', 5, 5, null, undefined), 'Hellö world!', 4, 5, true, 'ö', 0);
    });
    test('extractMacReplacedText - does nothing if there is more than one extra char', () => {
        testDeduceInput(new TextAreaState('Hello world!', 5, 5, null, undefined), 'Hellöö world!', 5, 5, true, 'öö', 1);
    });
    test('extractMacReplacedText - does nothing if there is more than one changed char', () => {
        testDeduceInput(new TextAreaState('Hello world!', 5, 5, null, undefined), 'Helöö world!', 5, 5, true, 'öö', 2);
    });
    test('extractMacReplacedText', () => {
        testDeduceInput(new TextAreaState('Hello world!', 5, 5, null, undefined), 'Hellö world!', 5, 5, true, 'ö', 1);
    });
    test('issue #25101 - First key press ignored', () => {
        testDeduceInput(new TextAreaState('a', 0, 1, null, undefined), 'a', 1, 1, true, 'a', 0);
    });
    test('issue #16520 - Cmd-d of single character followed by typing same character as has no effect', () => {
        testDeduceInput(new TextAreaState('x x', 0, 1, null, undefined), 'x x', 1, 1, true, 'x', 0);
    });
    function testDeduceAndroidCompositionInput(prevState, value, selectionStart, selectionEnd, expected, expectedReplacePrevCharCnt, expectedReplaceNextCharCnt, expectedPositionDelta) {
        prevState = prevState || TextAreaState.EMPTY;
        const textArea = new MockTextAreaWrapper();
        textArea._value = value;
        textArea._selectionStart = selectionStart;
        textArea._selectionEnd = selectionEnd;
        const newState = TextAreaState.readFromTextArea(textArea, null);
        const actual = TextAreaState.deduceAndroidCompositionInput(prevState, newState);
        assert.deepStrictEqual(actual, {
            text: expected,
            replacePrevCharCnt: expectedReplacePrevCharCnt,
            replaceNextCharCnt: expectedReplaceNextCharCnt,
            positionDelta: expectedPositionDelta,
        });
        textArea.dispose();
    }
    test('Android composition input 1', () => {
        testDeduceAndroidCompositionInput(new TextAreaState('Microsoft', 4, 4, null, undefined), 'Microsoft', 4, 4, '', 0, 0, 0);
    });
    test('Android composition input 2', () => {
        testDeduceAndroidCompositionInput(new TextAreaState('Microsoft', 4, 4, null, undefined), 'Microsoft', 0, 9, '', 0, 0, 5);
    });
    test('Android composition input 3', () => {
        testDeduceAndroidCompositionInput(new TextAreaState('Microsoft', 0, 9, null, undefined), 'Microsoft\'s', 11, 11, '\'s', 0, 0, 0);
    });
    test('Android backspace', () => {
        testDeduceAndroidCompositionInput(new TextAreaState('undefinedVariable', 2, 2, null, undefined), 'udefinedVariable', 1, 1, '', 1, 0, 0);
    });
    suite('SimplePagedScreenReaderStrategy', () => {
        function testPagedScreenReaderStrategy(lines, selection, expected) {
            const model = createTextModel(lines.join('\n'));
            const screenReaderStrategy = new SimplePagedScreenReaderStrategy();
            const screenReaderContentState = screenReaderStrategy.fromEditorSelection(model, selection, 10, true);
            const textAreaState = TextAreaState.fromScreenReaderContentState(screenReaderContentState);
            assert.ok(equalsTextAreaState(textAreaState, expected));
            model.dispose();
        }
        test('simple', () => {
            testPagedScreenReaderStrategy([
                'Hello world!'
            ], new Selection(1, 13, 1, 13), new TextAreaState('Hello world!', 12, 12, new Range(1, 13, 1, 13), 0));
            testPagedScreenReaderStrategy([
                'Hello world!'
            ], new Selection(1, 1, 1, 1), new TextAreaState('Hello world!', 0, 0, new Range(1, 1, 1, 1), 0));
            testPagedScreenReaderStrategy([
                'Hello world!'
            ], new Selection(1, 1, 1, 6), new TextAreaState('Hello world!', 0, 5, new Range(1, 1, 1, 6), 0));
        });
        test('multiline', () => {
            testPagedScreenReaderStrategy([
                'Hello world!',
                'How are you?'
            ], new Selection(1, 1, 1, 1), new TextAreaState('Hello world!\nHow are you?', 0, 0, new Range(1, 1, 1, 1), 0));
            testPagedScreenReaderStrategy([
                'Hello world!',
                'How are you?'
            ], new Selection(2, 1, 2, 1), new TextAreaState('Hello world!\nHow are you?', 13, 13, new Range(2, 1, 2, 1), 1));
        });
        test('page', () => {
            testPagedScreenReaderStrategy([
                'L1\nL2\nL3\nL4\nL5\nL6\nL7\nL8\nL9\nL10\nL11\nL12\nL13\nL14\nL15\nL16\nL17\nL18\nL19\nL20\nL21'
            ], new Selection(1, 1, 1, 1), new TextAreaState('L1\nL2\nL3\nL4\nL5\nL6\nL7\nL8\nL9\nL10\n', 0, 0, new Range(1, 1, 1, 1), 0));
            testPagedScreenReaderStrategy([
                'L1\nL2\nL3\nL4\nL5\nL6\nL7\nL8\nL9\nL10\nL11\nL12\nL13\nL14\nL15\nL16\nL17\nL18\nL19\nL20\nL21'
            ], new Selection(11, 1, 11, 1), new TextAreaState('L11\nL12\nL13\nL14\nL15\nL16\nL17\nL18\nL19\nL20\n', 0, 0, new Range(11, 1, 11, 1), 0));
            testPagedScreenReaderStrategy([
                'L1\nL2\nL3\nL4\nL5\nL6\nL7\nL8\nL9\nL10\nL11\nL12\nL13\nL14\nL15\nL16\nL17\nL18\nL19\nL20\nL21'
            ], new Selection(12, 1, 12, 1), new TextAreaState('L11\nL12\nL13\nL14\nL15\nL16\nL17\nL18\nL19\nL20\n', 4, 4, new Range(12, 1, 12, 1), 1));
            testPagedScreenReaderStrategy([
                'L1\nL2\nL3\nL4\nL5\nL6\nL7\nL8\nL9\nL10\nL11\nL12\nL13\nL14\nL15\nL16\nL17\nL18\nL19\nL20\nL21'
            ], new Selection(21, 1, 21, 1), new TextAreaState('L21', 0, 0, new Range(21, 1, 21, 1), 0));
        });
    });
});
//# sourceMappingURL=data:application/json;base64,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