{"version": 3, "file": "static/js/585.6a2854b2.chunk.js", "mappings": "0MAMA,MAmEA,EAnEcA,KACZ,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,KAC5BC,EAAaC,IAAkBF,EAAAA,EAAAA,WAAS,GAwB/C,OAJAG,EAAAA,EAAAA,WAAU,KAlBWC,WACnBL,EAASM,aAAaC,QAAQ,UAAY,IAC1C,MAAMC,QAAYC,EAAAA,GAAIC,IAAI,eACpB,QAAEC,EAAO,QAAEC,EAAO,KAAEC,GAASL,EAAIK,KACvC,GAAIF,EAAS,CACX,IAAIG,EAAeD,EACdA,EAAKE,WAAW,cACnBD,EAAeE,EAAAA,GAAOC,MAAMJ,IAE9Bb,EAASc,GACTR,aAAaY,QAAQ,QAASJ,EAChC,MACEK,EAAAA,EAAAA,IAAUP,GACVZ,EAASH,EAAE,yBAEbM,GAAe,IAIfiB,GAAeC,QACd,KAGDC,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SACGtB,GAAyB,KAAVH,GACduB,EAAAA,EAAAA,KAAA,OAAKG,UAAU,sBAAqBD,UAClCF,EAAAA,EAAAA,KAACI,EAAAA,EAAI,CAACC,OAAK,EAACF,UAAU,aAAYD,UAChCI,EAAAA,EAAAA,MAACF,EAAAA,EAAKG,QAAO,CAAAL,SAAA,EACXF,EAAAA,EAAAA,KAACI,EAAAA,EAAKI,OAAM,CAACL,UAAU,SAAQD,SAAE3B,EAAE,kBACnCyB,EAAAA,EAAAA,KAAA,KAAAE,SAAI3B,EAAE,uBACLA,EAAE,qBACHyB,EAAAA,EAAAA,KAAA,KAAGS,KAAK,0CAAyCP,SAAC,oDAOxDF,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SACGzB,EAAMgB,WAAW,aAChBO,EAAAA,EAAAA,KAAA,UACEU,IAAKjC,EACLkC,MAAO,CAAEC,MAAO,OAAQC,OAAQ,QAASC,OAAQ,WAGnDd,EAAAA,EAAAA,KAAA,OAAKG,UAAU,sBAAqBD,UAClCF,EAAAA,EAAAA,KAACI,EAAAA,EAAI,CAACC,OAAK,EAACF,UAAU,aAAYD,UAChCF,EAAAA,EAAAA,KAACI,EAAAA,EAAKG,QAAO,CAAAL,UACXF,EAAAA,EAAAA,KAAA,OACEW,MAAO,CAAEI,SAAU,UACnBC,wBAAyB,CAAEC,OAAQxC,e", "sources": ["pages/About/index.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { Card } from 'semantic-ui-react';\r\nimport { API, showError } from '../../helpers';\r\nimport { marked } from 'marked';\r\n\r\nconst About = () => {\r\n  const { t } = useTranslation();\r\n  const [about, setAbout] = useState('');\r\n  const [aboutLoaded, setAboutLoaded] = useState(false);\r\n\r\n  const displayAbout = async () => {\r\n    setAbout(localStorage.getItem('about') || '');\r\n    const res = await API.get('/api/about');\r\n    const { success, message, data } = res.data;\r\n    if (success) {\r\n      let aboutContent = data;\r\n      if (!data.startsWith('https://')) {\r\n        aboutContent = marked.parse(data);\r\n      }\r\n      setAbout(aboutContent);\r\n      localStorage.setItem('about', aboutContent);\r\n    } else {\r\n      showError(message);\r\n      setAbout(t('about.loading_failed'));\r\n    }\r\n    setAboutLoaded(true);\r\n  };\r\n\r\n  useEffect(() => {\r\n    displayAbout().then();\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      {aboutLoaded && about === '' ? (\r\n        <div className='dashboard-container'>\r\n          <Card fluid className='chart-card'>\r\n            <Card.Content>\r\n              <Card.Header className='header'>{t('about.title')}</Card.Header>\r\n              <p>{t('about.description')}</p>\r\n              {t('about.repository')}\r\n              <a href='https://github.com/songquanpeng/one-api'>\r\n                https://github.com/songquanpeng/one-api\r\n              </a>\r\n            </Card.Content>\r\n          </Card>\r\n        </div>\r\n      ) : (\r\n        <>\r\n          {about.startsWith('https://') ? (\r\n            <iframe\r\n              src={about}\r\n              style={{ width: '100%', height: '100vh', border: 'none' }}\r\n            />\r\n          ) : (\r\n            <div className='dashboard-container'>\r\n              <Card fluid className='chart-card'>\r\n                <Card.Content>\r\n                  <div\r\n                    style={{ fontSize: 'larger' }}\r\n                    dangerouslySetInnerHTML={{ __html: about }}\r\n                  ></div>\r\n                </Card.Content>\r\n              </Card>\r\n            </div>\r\n          )}\r\n        </>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default About;\r\n"], "names": ["About", "t", "useTranslation", "about", "setAbout", "useState", "aboutLoaded", "setAboutLoaded", "useEffect", "async", "localStorage", "getItem", "res", "API", "get", "success", "message", "data", "about<PERSON>ontent", "startsWith", "marked", "parse", "setItem", "showError", "displayAbout", "then", "_jsx", "_Fragment", "children", "className", "Card", "fluid", "_jsxs", "Content", "Header", "href", "src", "style", "width", "height", "border", "fontSize", "dangerouslySetInnerHTML", "__html"], "sourceRoot": ""}