{"name": "vscode-oniguruma", "version": "1.7.0", "description": "VSCode oniguruma bindings", "author": {"name": "Microsoft Corporation"}, "main": "release/main.js", "typings": "main.d.ts", "repository": {"type": "git", "url": "https://github.com/microsoft/vscode-oniguruma"}, "license": "MIT", "bugs": {"url": "https://github.com/microsoft/vscode-oniguruma/issues"}, "scripts": {"test": "mocha --ui=tdd ./out/test/index.test.js", "build-onig": "cd deps/oniguruma && autoreconf -vfi && emconfigure ./configure && make clean && emmake make", "build-wasm": "./scripts/build.sh", "build-tsc": "tsc -p tsconfig.json", "watch-tsc": "tsc -w -p tsconfig.json", "package": "npm run build-tsc && webpack && node ./scripts/cp ./out/onig.wasm ./release/onig.wasm", "prepublishOnly": "npm run package"}, "devDependencies": {"@types/mocha": "^10.0.0", "@types/node": "^18.11.9", "mocha": "^10.1.0", "typescript": "^4.9.3", "webpack": "^5.75.0", "webpack-cli": "^5.0.0"}}