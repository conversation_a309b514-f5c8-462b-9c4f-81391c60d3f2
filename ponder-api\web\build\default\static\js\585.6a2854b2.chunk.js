"use strict";(self.webpackChunkreact_template=self.webpackChunkreact_template||[]).push([[585],{5585:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var s=a(5043),r=a(4117),n=a(7644),c=a(8169),i=a(96),l=a(579);const d=()=>{const{t:e}=(0,r.Bd)(),[t,a]=(0,s.useState)(""),[d,h]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{(async()=>{a(localStorage.getItem("about")||"");const t=await c.nC.get("/api/about"),{success:s,message:r,data:n}=t.data;if(s){let e=n;n.startsWith("https://")||(e=i.xI.parse(n)),a(e),localStorage.setItem("about",e)}else(0,c.Qg)(r),a(e("about.loading_failed"));h(!0)})().then()},[]),(0,l.jsx)(l.Fragment,{children:d&&""===t?(0,l.jsx)("div",{className:"dashboard-container",children:(0,l.jsx)(n.A,{fluid:!0,className:"chart-card",children:(0,l.jsxs)(n.A.Content,{children:[(0,l.jsx)(n.A.Header,{className:"header",children:e("about.title")}),(0,l.jsx)("p",{children:e("about.description")}),e("about.repository"),(0,l.jsx)("a",{href:"https://github.com/songquanpeng/one-api",children:"https://github.com/songquanpeng/one-api"})]})})}):(0,l.jsx)(l.Fragment,{children:t.startsWith("https://")?(0,l.jsx)("iframe",{src:t,style:{width:"100%",height:"100vh",border:"none"}}):(0,l.jsx)("div",{className:"dashboard-container",children:(0,l.jsx)(n.A,{fluid:!0,className:"chart-card",children:(0,l.jsx)(n.A.Content,{children:(0,l.jsx)("div",{style:{fontSize:"larger"},dangerouslySetInnerHTML:{__html:t}})})})})})})}}}]);
//# sourceMappingURL=585.6a2854b2.chunk.js.map