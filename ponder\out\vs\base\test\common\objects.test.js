/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import assert from 'assert';
import * as objects from '../../common/objects.js';
import { ensureNoDisposablesAreLeakedInTestSuite } from './utils.js';
const check = (one, other, msg) => {
    assert(objects.equals(one, other), msg);
    assert(objects.equals(other, one), '[reverse] ' + msg);
};
const checkNot = (one, other, msg) => {
    assert(!objects.equals(one, other), msg);
    assert(!objects.equals(other, one), '[reverse] ' + msg);
};
suite('Objects', () => {
    ensureNoDisposablesAreLeakedInTestSuite();
    test('equals', () => {
        check(null, null, 'null');
        check(undefined, undefined, 'undefined');
        check(1234, 1234, 'numbers');
        check('', '', 'empty strings');
        check('1234', '1234', 'strings');
        check([], [], 'empty arrays');
        // check(['', 123], ['', 123], 'arrays');
        check([[1, 2, 3], [4, 5, 6]], [[1, 2, 3], [4, 5, 6]], 'nested arrays');
        check({}, {}, 'empty objects');
        check({ a: 1, b: '123' }, { a: 1, b: '123' }, 'objects');
        check({ a: 1, b: '123' }, { b: '123', a: 1 }, 'objects (key order)');
        check({ a: { b: 1, c: 2 }, b: 3 }, { a: { b: 1, c: 2 }, b: 3 }, 'nested objects');
        checkNot(null, undefined, 'null != undefined');
        checkNot(null, '', 'null != empty string');
        checkNot(null, [], 'null != empty array');
        checkNot(null, {}, 'null != empty object');
        checkNot(null, 0, 'null != zero');
        checkNot(undefined, '', 'undefined != empty string');
        checkNot(undefined, [], 'undefined != empty array');
        checkNot(undefined, {}, 'undefined != empty object');
        checkNot(undefined, 0, 'undefined != zero');
        checkNot('', [], 'empty string != empty array');
        checkNot('', {}, 'empty string != empty object');
        checkNot('', 0, 'empty string != zero');
        checkNot([], {}, 'empty array != empty object');
        checkNot([], 0, 'empty array != zero');
        checkNot(0, [], 'zero != empty array');
        checkNot('1234', 1234, 'string !== number');
        checkNot([[1, 2, 3], [4, 5, 6]], [[1, 2, 3], [4, 5, 6000]], 'arrays');
        checkNot({ a: { b: 1, c: 2 }, b: 3 }, { b: 3, a: { b: 9, c: 2 } }, 'objects');
    });
    test('mixin - array', function () {
        const foo = {};
        objects.mixin(foo, { bar: [1, 2, 3] });
        assert(foo.bar);
        assert(Array.isArray(foo.bar));
        assert.strictEqual(foo.bar.length, 3);
        assert.strictEqual(foo.bar[0], 1);
        assert.strictEqual(foo.bar[1], 2);
        assert.strictEqual(foo.bar[2], 3);
    });
    test('mixin - no overwrite', function () {
        const foo = {
            bar: '123'
        };
        const bar = {
            bar: '456'
        };
        objects.mixin(foo, bar, false);
        assert.strictEqual(foo.bar, '123');
    });
    test('cloneAndChange', () => {
        const o1 = { something: 'hello' };
        const o = {
            o1: o1,
            o2: o1
        };
        assert.deepStrictEqual(objects.cloneAndChange(o, () => { }), o);
    });
    test('safeStringify', () => {
        const obj1 = {
            friend: null
        };
        const obj2 = {
            friend: null
        };
        obj1.friend = obj2;
        obj2.friend = obj1;
        const arr = [1];
        arr.push(arr);
        const circular = {
            a: 42,
            b: null,
            c: [
                obj1, obj2
            ],
            d: null,
            e: BigInt(42)
        };
        arr.push(circular);
        circular.b = circular;
        circular.d = arr;
        const result = objects.safeStringify(circular);
        assert.deepStrictEqual(JSON.parse(result), {
            a: 42,
            b: '[Circular]',
            c: [
                {
                    friend: {
                        friend: '[Circular]'
                    }
                },
                '[Circular]'
            ],
            d: [1, '[Circular]', '[Circular]'],
            e: '[BigInt 42]'
        });
    });
    test('distinct', () => {
        const base = {
            one: 'one',
            two: 2,
            three: {
                3: true
            },
            four: false
        };
        let diff = objects.distinct(base, base);
        assert.strictEqual(Object.keys(diff).length, 0);
        let obj = {};
        diff = objects.distinct(base, obj);
        assert.strictEqual(Object.keys(diff).length, 0);
        obj = {
            one: 'one',
            two: 2
        };
        diff = objects.distinct(base, obj);
        assert.strictEqual(Object.keys(diff).length, 0);
        obj = {
            three: {
                3: true
            },
            four: false
        };
        diff = objects.distinct(base, obj);
        assert.strictEqual(Object.keys(diff).length, 0);
        obj = {
            one: 'two',
            two: 2,
            three: {
                3: true
            },
            four: true
        };
        diff = objects.distinct(base, obj);
        assert.strictEqual(Object.keys(diff).length, 2);
        assert.strictEqual(diff.one, 'two');
        assert.strictEqual(diff.four, true);
        obj = {
            one: null,
            two: 2,
            three: {
                3: true
            },
            four: undefined
        };
        diff = objects.distinct(base, obj);
        assert.strictEqual(Object.keys(diff).length, 2);
        assert.strictEqual(diff.one, null);
        assert.strictEqual(diff.four, undefined);
        obj = {
            one: 'two',
            two: 3,
            three: { 3: false },
            four: true
        };
        diff = objects.distinct(base, obj);
        assert.strictEqual(Object.keys(diff).length, 4);
        assert.strictEqual(diff.one, 'two');
        assert.strictEqual(diff.two, 3);
        assert.strictEqual(diff.three?.['3'], false);
        assert.strictEqual(diff.four, true);
    });
    test('getCaseInsensitive', () => {
        const obj1 = {
            lowercase: 123,
            mIxEdCaSe: 456
        };
        assert.strictEqual(obj1.lowercase, objects.getCaseInsensitive(obj1, 'lowercase'));
        assert.strictEqual(obj1.lowercase, objects.getCaseInsensitive(obj1, 'lOwErCaSe'));
        assert.strictEqual(obj1.mIxEdCaSe, objects.getCaseInsensitive(obj1, 'MIXEDCASE'));
        assert.strictEqual(obj1.mIxEdCaSe, objects.getCaseInsensitive(obj1, 'mixedcase'));
    });
});
test('mapValues', () => {
    const obj = {
        a: 1,
        b: 2,
        c: 3
    };
    const result = objects.mapValues(obj, (value, key) => `${key}: ${value * 2}`);
    assert.deepStrictEqual(result, {
        a: 'a: 2',
        b: 'b: 4',
        c: 'c: 6',
    });
});
//# sourceMappingURL=data:application/json;base64,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