{"name": "undici", "version": "7.9.0", "description": "An HTTP/1.1 client, written from scratch for Node.js", "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "repository": {"type": "git", "url": "git+https://github.com/nodejs/undici.git"}, "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/dnlup", "author": true}, {"name": "<PERSON>", "url": "https://github.com/ethan-arrowood", "author": true}, {"name": "<PERSON>", "url": "https://github.com/mcollina", "author": true}, {"name": "<PERSON>", "url": "https://github.com/KhafraDev", "author": true}, {"name": "<PERSON>", "url": "https://github.com/ronag", "author": true}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/szmarczak", "author": true}, {"name": "<PERSON>", "url": "https://github.com/delvedor", "author": true}], "keywords": ["fetch", "http", "https", "promise", "request", "curl", "wget", "xhr", "whatwg"], "main": "index.js", "types": "index.d.ts", "scripts": {"build:node": "esbuild index-fetch.js --bundle --platform=node --outfile=undici-fetch.js --define:esbuildDetection=1 --keep-names && node scripts/strip-comments.js", "build:wasm": "node build/wasm.js --docker", "generate-pem": "node scripts/generate-pem.js", "lint": "eslint --cache", "lint:fix": "eslint --fix --cache", "test": "npm run test:javascript && cross-env NODE_V8_COVERAGE=  npm run test:typescript", "test:javascript": "npm run test:javascript:no-jest && npm run test:jest", "test:javascript:no-jest": "npm run generate-pem && npm run test:unit && npm run test:node-fetch && npm run test:cache && npm run test:cache-interceptor && npm run test:interceptors && npm run test:fetch && npm run test:cookies && npm run test:eventsource && npm run test:wpt && npm run test:websocket && npm run test:node-test && npm run test:cache-tests", "test:javascript:without-intl": "npm run test:javascript:no-jest", "test:busboy": "borp -p \"test/busboy/*.js\"", "test:cache": "borp -p \"test/cache/*.js\"", "test:sqlite": "NODE_OPTIONS=--experimental-sqlite borp -p \"test/cache-interceptor/*.js\"", "test:cache-interceptor": "borp -p \"test/cache-interceptor/*.js\"", "test:cookies": "borp -p \"test/cookie/*.js\"", "test:eventsource": "npm run build:node && borp --expose-gc -p \"test/eventsource/*.js\"", "test:fuzzing": "node test/fuzzing/fuzzing.test.js", "test:fetch": "npm run build:node && borp --timeout 180000 --expose-gc --concurrency 1 -p \"test/fetch/*.js\" && npm run test:webidl && npm run test:busboy", "test:h2": "npm run test:h2:core && npm run test:h2:fetch", "test:h2:core": "borp -p \"test/+(http2|h2)*.js\"", "test:h2:fetch": "npm run build:node && borp -p \"test/fetch/http2*.js\"", "test:interceptors": "borp -p \"test/interceptors/*.js\"", "test:jest": "cross-env NODE_V8_COVERAGE= jest", "test:unit": "borp --expose-gc -p \"test/*.js\"", "test:node-fetch": "borp -p \"test/node-fetch/**/*.js\"", "test:node-test": "borp -p \"test/node-test/**/*.js\"", "test:tdd": "borp --expose-gc -p \"test/*.js\"", "test:tdd:node-test": "borp -p \"test/node-test/**/*.js\" -w", "test:typescript": "tsd && tsc test/imports/undici-import.ts --typeRoots ./types --noEmit && tsc ./types/*.d.ts --noEmit --typeRoots ./types", "test:webidl": "borp -p \"test/webidl/*.js\"", "test:websocket": "borp -p \"test/websocket/*.js\"", "test:websocket:autobahn": "node test/autobahn/client.js", "test:websocket:autobahn:report": "node test/autobahn/report.js", "test:wpt": "node test/wpt/start-fetch.mjs && node test/wpt/start-mimesniff.mjs && node test/wpt/start-xhr.mjs && node test/wpt/start-websockets.mjs && node test/wpt/start-cacheStorage.mjs && node test/wpt/start-eventsource.mjs", "test:wpt:withoutintl": "node test/wpt/start-fetch.mjs && node test/wpt/start-mimesniff.mjs && node test/wpt/start-xhr.mjs && node test/wpt/start-cacheStorage.mjs && node test/wpt/start-eventsource.mjs", "test:cache-tests": "node test/cache-interceptor/cache-tests.mjs --ci", "coverage": "npm run coverage:clean && cross-env NODE_V8_COVERAGE=./coverage/tmp npm run test:javascript && npm run coverage:report", "coverage:ci": "npm run coverage:clean && cross-env NODE_V8_COVERAGE=./coverage/tmp npm run test:javascript && npm run coverage:report:ci", "coverage:clean": "node ./scripts/clean-coverage.js", "coverage:report": "cross-env NODE_V8_COVERAGE= c8 report", "coverage:report:ci": "c8 report", "bench": "echo \"Error: Benchmarks have been moved to '/benchmarks'\" && exit 1", "serve:website": "echo \"Error: Documentation has been moved to '/docs'\" && exit 1", "prepare": "husky && node ./scripts/platform-shell.js"}, "devDependencies": {"@fastify/busboy": "3.1.1", "@matteo.collina/tspl": "^0.2.0", "@sinonjs/fake-timers": "^12.0.0", "@types/node": "^18.19.50", "abort-controller": "^3.0.0", "borp": "^0.19.0", "c8": "^10.0.0", "cross-env": "^7.0.3", "dns-packet": "^5.4.0", "esbuild": "^0.25.2", "eslint": "^9.9.0", "fast-check": "^3.17.1", "https-pem": "^3.0.0", "husky": "^9.0.7", "jest": "^29.0.2", "neostandard": "^0.12.0", "node-forge": "^1.3.1", "proxy": "^2.1.1", "tsd": "^0.31.2", "typescript": "^5.6.2", "ws": "^8.11.0"}, "engines": {"node": ">=20.18.1"}, "tsd": {"directory": "test/types", "compilerOptions": {"esModuleInterop": true, "lib": ["esnext"]}}, "jest": {"testMatch": ["<rootDir>/test/jest/**"]}}