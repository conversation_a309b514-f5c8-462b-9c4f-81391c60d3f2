/*!
 * Microsoft Dynamic Proto Utility, 1.1.9
 * Copyright (c) Microsoft and contributors. All rights reserved.
 */
"use strict";var n,t="undefined",o="constructor",d="prototype",h="function",v="_dynInstFuncs",_="_isDynProxy",g="_dynClass",w="_dynInstChk",b=w,P="_dfOpts",r="_unknown_",e="__proto__",i="_dyn"+e,u="__dynProto$Gbl",f="_dynInstProto",m="useBaseInst",I="setInstFuncs",s=Object,O=s.getPrototypeOf,a=s.getOwnPropertyNames,t=(n=(n=(n=(n=typeof globalThis!=t?globalThis:n)||typeof self==t?n:self)||typeof window==t?n:window)||typeof global==t?n:global)||{},C=t[u]||(t[u]={o:((n={})[I]=!0,n[m]=!0,n),n:1e3});function k(n,t){return n&&s[d].hasOwnProperty.call(n,t)}function F(n){return n&&(n===s[d]||n===Array[d])}function T(n){return F(n)||n===Function[d]}function $(n){if(n){if(O)return O(n);var t=n[e]||n[d]||(n[o]?n[o][d]:null),r=n[i]||t;k(n,i)||(delete n[f],r=n[i]=n[f]||n[i],n[f]=t)}return r}function x(n,t){var r=[];if(a)r=a(n);else for(var o in n)"string"==typeof o&&k(n,o)&&r.push(o);if(r&&0<r.length)for(var e=0;e<r.length;e++)t(r[e])}function D(n,t,r){return t!==o&&typeof n[t]===h&&(r||k(n,t))}function j(n){throw new TypeError("DynamicProto: "+n)}function A(n,t){for(var r=n.length-1;0<=r;r--)if(n[r]===t)return 1}function B(n,t){return k(n,d)?n.name||t||r:((n||{})[o]||{}).name||t||r}function E(n,o,t,r){k(n,d)||j("theClass is an invalid class definition.");var e,i,u,f,s,a,c=n[d],l=(function(n){if(!O)return 1;for(var t=[],r=$(o);r&&!T(r)&&!A(t,r);){if(r===n)return 1;t.push(r),r=$(r)}}(c)||j("["+B(n)+"] not in hierarchy of ["+B(o)+"]"),null),n=(k(c,g)?l=c[g]:(l="_dynCls$"+B(n,"_")+"$"+C.n,C.n++,c[g]=l),E[P]),y=!!n[m],p=(y&&r&&r[m]!==undefined&&(y=!!r[m]),i={},x(e=o,function(n){!i[n]&&D(e,n,!1)&&(i[n]=e[n])}),i),y=(t(o,function(n,t,r,i){function o(n,t,r){var o,e=t[r];return e[_]&&i&&!1!==(o=n[v]||{})[b]&&(e=(o[t[g]]||{})[r]||e),function(){return e.apply(n,arguments)}}for(var e={},u=(x(r,function(n){e[n]=o(t,r,n)}),$(n)),f=[];u&&!T(u)&&!A(f,u);)x(u,function(n){!e[n]&&D(u,n,!O)&&(e[n]=o(t,u,n))}),f.push(u),u=$(u);return e}(c,o,p,y)),!!O&&!!n[I]);u=c,t=l,f=o,s=p,n=0!=(y&&r?!!r[I]:y),F(u)||(c=f[v]=f[v]||{},a=c[t]=c[t]||{},!1!==c[b]&&(c[b]=!!n),x(f,function(n){var r,o,e;D(f,n,!1)&&f[n]!==s[n]&&(a[n]=f[n],delete f[n],k(u,n)&&(!u[n]||u[n][_])||(u[n]=(r=u,o=n,(e=function(){var n,t;return(function(n,t,r,o){var e=null;if(n&&k(r,g)){var i=n[v]||{};if((e=(i[r[g]]||{})[t])||j("Missing ["+t+"] "+h),!e[w]&&!1!==i[b]){for(var u=!k(n,t),f=$(n),s=[];u&&f&&!T(f)&&!A(s,f);){var a=f[t];if(a){u=a===o;break}s.push(f),f=$(f)}try{u&&(n[t]=e),e[w]=1}catch(c){i[b]=!1}}}return e}(this,o,r,e)||(typeof(t=(t=r[n=o])===e?$(r)[n]:t)!==h&&j("["+n+"] is not a "+h),t)).apply(this,arguments)})[_]=1,e)))}))}E[P]=C.o,module.exports=E;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/9df2e0f1ef56cd7bcef40a92d8f4f46ca576ee3f/node_modules/@microsoft/dynamicproto-js/lib/dist/cjs/dynamicproto-js.min.js.map
