{"version": 3, "file": "static/js/151.da0f3c02.chunk.js", "mappings": "iPASA,MAiSA,EAjSaA,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACjB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAaC,IAAkBC,EAAAA,EAAAA,YAAWC,EAAAA,IAC1CC,EAAuBC,IAA4BC,EAAAA,EAAAA,WAAS,IAC5DC,EAAiBC,IAAsBF,EAAAA,EAAAA,UAAS,KAChDG,IAAaP,EAAAA,EAAAA,YAAWQ,EAAAA,GA6C/B,OALAC,EAAAA,EAAAA,WAAU,KAtCYC,WACpB,MAAMC,QAAYC,EAAAA,GAAIC,IAAI,gBACpB,QAAEC,EAAO,QAAEC,EAAO,KAAEC,GAASL,EAAIK,KACvC,GAAIF,GAEF,GAAIE,IADYC,aAAaC,QAAQ,WACF,KAATF,EAAa,CACrC,MAAMG,GAAaC,EAAAA,EAAAA,IAAOJ,IAC1BK,EAAAA,EAAAA,IAAWF,GAAY,GACvBF,aAAaK,QAAQ,SAAUN,EACjC,OAEAO,EAAAA,EAAAA,IAAUR,IA4BZS,GAAgBC,OAxBaf,WAC7BJ,EAAmBW,aAAaC,QAAQ,sBAAwB,IAChE,MAAMP,QAAYC,EAAAA,GAAIC,IAAI,2BACpB,QAAEC,EAAO,QAAEC,EAAO,KAAEC,GAASL,EAAIK,KACvC,GAAIF,EAAS,CACX,IAAIY,EAAUV,EACTA,EAAKW,WAAW,cACnBD,EAAUN,EAAAA,GAAOQ,MAAMZ,IAEzBV,EAAmBoB,GACnBT,aAAaK,QAAQ,oBAAqBI,EAC5C,MACEH,EAAAA,EAAAA,IAAUR,GACVT,EAAmBV,EAAE,wBAEvBO,GAAyB,IAUzB0B,GAAyBJ,QACxB,KAGDK,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SACG9B,GAA6C,KAApBG,GACxB4B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sBAAqBF,SAAA,EAClCF,EAAAA,EAAAA,KAACK,EAAAA,EAAI,CAACC,OAAK,EAACF,UAAU,aAAYF,UAChCC,EAAAA,EAAAA,MAACE,EAAAA,EAAKE,QAAO,CAAAL,SAAA,EACXF,EAAAA,EAAAA,KAACK,EAAAA,EAAKG,OAAM,CAACJ,UAAU,SAAQF,SAC5BpC,EAAE,yBAELqC,EAAAA,EAAAA,MAACE,EAAAA,EAAKI,YAAW,CAACC,MAAO,CAAEC,WAAY,OAAQT,SAAA,EAC7CF,EAAAA,EAAAA,KAAA,KAAAE,SAAIpC,EAAE,+BACJW,EAAUmC,OAAQZ,EAAAA,EAAAA,KAAA,KAAAE,SAAIpC,EAAE,wCAIhCkC,EAAAA,EAAAA,KAACK,EAAAA,EAAI,CAACC,OAAK,EAACF,UAAU,aAAYF,UAChCC,EAAAA,EAAAA,MAACE,EAAAA,EAAKE,QAAO,CAAAL,SAAA,EACXF,EAAAA,EAAAA,KAACK,EAAAA,EAAKG,OAAM,CAAAN,UACVF,EAAAA,EAAAA,KAACQ,EAAAA,EAAM,CAACK,GAAG,KAAIX,SAAEpC,EAAE,iCAErBqC,EAAAA,EAAAA,MAACW,EAAAA,EAAI,CAACC,QAAS,EAAGC,WAAS,EAAAd,SAAA,EACzBF,EAAAA,EAAAA,KAACc,EAAAA,EAAKG,OAAM,CAAAf,UACVF,EAAAA,EAAAA,KAACK,EAAAA,EAAI,CACHC,OAAK,EACLF,UAAU,aACVM,MAAO,CAAEQ,UAAW,8BAA+BhB,UAEnDC,EAAAA,EAAAA,MAACE,EAAAA,EAAKE,QAAO,CAAAL,SAAA,EACXF,EAAAA,EAAAA,KAACK,EAAAA,EAAKG,OAAM,CAAAN,UACVF,EAAAA,EAAAA,KAACQ,EAAAA,EAAM,CAACK,GAAG,KAAKH,MAAO,CAAES,MAAO,QAASjB,SACtCpC,EAAE,sCAGPqC,EAAAA,EAAAA,MAACE,EAAAA,EAAKI,YAAW,CACfC,MAAO,CAAEC,WAAY,IAAKS,UAAW,OAAQlB,SAAA,EAE7CC,EAAAA,EAAAA,MAAA,KACEO,MAAO,CACLW,QAAS,OACTC,WAAY,SACZC,IAAK,SACLrB,SAAA,EAEFF,EAAAA,EAAAA,KAAA,KAAGI,UAAU,sBACbJ,EAAAA,EAAAA,KAAA,QAAMU,MAAO,CAAEc,WAAY,QAAStB,SACjCpC,EAAE,mCAELkC,EAAAA,EAAAA,KAAA,QAAAE,SAAkB,OAAXlC,QAAW,IAAXA,GAAmB,QAARZ,EAAXY,EAAayD,cAAM,IAAArE,OAAR,EAAXA,EAAqBsE,kBAE9BvB,EAAAA,EAAAA,MAAA,KACEO,MAAO,CACLW,QAAS,OACTC,WAAY,SACZC,IAAK,SACLrB,SAAA,EAEFF,EAAAA,EAAAA,KAAA,KAAGI,UAAU,sBACbJ,EAAAA,EAAAA,KAAA,QAAMU,MAAO,CAAEc,WAAY,QAAStB,SACjCpC,EAAE,sCAELkC,EAAAA,EAAAA,KAAA,QAAAE,UACc,OAAXlC,QAAW,IAAXA,GAAmB,QAARX,EAAXW,EAAayD,cAAM,IAAApE,OAAR,EAAXA,EAAqBsE,UAAW,gBAGrCxB,EAAAA,EAAAA,MAAA,KACEO,MAAO,CACLW,QAAS,OACTC,WAAY,SACZC,IAAK,SACLrB,SAAA,EAEFF,EAAAA,EAAAA,KAAA,KAAGI,UAAU,iBACbJ,EAAAA,EAAAA,KAAA,QAAMU,MAAO,CAAEc,WAAY,QAAStB,SACjCpC,EAAE,qCAELkC,EAAAA,EAAAA,KAAA,KACE4B,KAAK,0CACLC,OAAO,SACPnB,MAAO,CAAES,MAAO,WAAYjB,SAE3BpC,EAAE,6CAGPqC,EAAAA,EAAAA,MAAA,KACEO,MAAO,CACLW,QAAS,OACTC,WAAY,SACZC,IAAK,SACLrB,SAAA,EAEFF,EAAAA,EAAAA,KAAA,KAAGI,UAAU,wBACbJ,EAAAA,EAAAA,KAAA,QAAMU,MAAO,CAAEc,WAAY,QAAStB,SACjCpC,EAAE,yCAELkC,EAAAA,EAAAA,KAAA,QAAAE,SAxGG4B,MAAO,IAADC,EAC/B,MAAMC,EAAuB,OAAXhE,QAAW,IAAXA,GAAmB,QAAR+D,EAAX/D,EAAayD,cAAM,IAAAM,OAAR,EAAXA,EAAqBE,WACvC,OAAOC,EAAAA,EAAAA,IAAiBF,IAsGKF,mBAOjB9B,EAAAA,EAAAA,KAACc,EAAAA,EAAKG,OAAM,CAAAf,UACVF,EAAAA,EAAAA,KAACK,EAAAA,EAAI,CACHC,OAAK,EACLF,UAAU,aACVM,MAAO,CAAEQ,UAAW,8BAA+BhB,UAEnDC,EAAAA,EAAAA,MAACE,EAAAA,EAAKE,QAAO,CAAAL,SAAA,EACXF,EAAAA,EAAAA,KAACK,EAAAA,EAAKG,OAAM,CAAAN,UACVF,EAAAA,EAAAA,KAACQ,EAAAA,EAAM,CAACK,GAAG,KAAKH,MAAO,CAAES,MAAO,QAASjB,SACtCpC,EAAE,wCAGPqC,EAAAA,EAAAA,MAACE,EAAAA,EAAKI,YAAW,CACfC,MAAO,CAAEC,WAAY,IAAKS,UAAW,OAAQlB,SAAA,EAE7CC,EAAAA,EAAAA,MAAA,KACEO,MAAO,CACLW,QAAS,OACTC,WAAY,SACZC,IAAK,SACLrB,SAAA,EAEFF,EAAAA,EAAAA,KAAA,KAAGI,UAAU,mBACbJ,EAAAA,EAAAA,KAAA,QAAMU,MAAO,CAAEc,WAAY,QAAStB,SACjCpC,EAAE,6CAELkC,EAAAA,EAAAA,KAAA,QACEU,MAAO,CACLS,MAAkB,OAAXnD,QAAW,IAAXA,GAAmB,QAARV,EAAXU,EAAayD,cAAM,IAAAnE,GAAnBA,EAAqB6E,mBACxB,UACA,UACJX,WAAY,OACZtB,SAEU,OAAXlC,QAAW,IAAXA,GAAmB,QAART,EAAXS,EAAayD,cAAM,IAAAlE,GAAnBA,EAAqB4E,mBAClBrE,EAAE,qCACFA,EAAE,4CAGVqC,EAAAA,EAAAA,MAAA,KACEO,MAAO,CACLW,QAAS,OACTC,WAAY,SACZC,IAAK,SACLrB,SAAA,EAEFF,EAAAA,EAAAA,KAAA,KAAGI,UAAU,iBACbJ,EAAAA,EAAAA,KAAA,QAAMU,MAAO,CAAEc,WAAY,QAAStB,SACjCpC,EAAE,6CAELkC,EAAAA,EAAAA,KAAA,QACEU,MAAO,CACLS,MAAkB,OAAXnD,QAAW,IAAXA,GAAmB,QAARR,EAAXQ,EAAayD,cAAM,IAAAjE,GAAnBA,EAAqB4E,aACxB,UACA,UACJZ,WAAY,OACZtB,SAEU,OAAXlC,QAAW,IAAXA,GAAmB,QAARP,EAAXO,EAAayD,cAAM,IAAAhE,GAAnBA,EAAqB2E,aAClBtE,EAAE,qCACFA,EAAE,4CAGVqC,EAAAA,EAAAA,MAAA,KACEO,MAAO,CACLW,QAAS,OACTC,WAAY,SACZC,IAAK,SACLrB,SAAA,EAEFF,EAAAA,EAAAA,KAAA,KAAGI,UAAU,iBACbJ,EAAAA,EAAAA,KAAA,QAAMU,MAAO,CAAEc,WAAY,QAAStB,SACjCpC,EAAE,6CAELkC,EAAAA,EAAAA,KAAA,QACEU,MAAO,CACLS,MAAkB,OAAXnD,QAAW,IAAXA,GAAmB,QAARN,EAAXM,EAAayD,cAAM,IAAA/D,GAAnBA,EAAqB2E,aACxB,UACA,UACJb,WAAY,OACZtB,SAEU,OAAXlC,QAAW,IAAXA,GAAmB,QAARL,EAAXK,EAAayD,cAAM,IAAA9D,GAAnBA,EAAqB0E,aAClBvE,EAAE,qCACFA,EAAE,4CAGVqC,EAAAA,EAAAA,MAAA,KACEO,MAAO,CACLW,QAAS,OACTC,WAAY,SACZC,IAAK,SACLrB,SAAA,EAEFF,EAAAA,EAAAA,KAAA,KAAGI,UAAU,2BACbJ,EAAAA,EAAAA,KAAA,QAAMU,MAAO,CAAEc,WAAY,QAAStB,SACjCpC,EAAE,0CAELkC,EAAAA,EAAAA,KAAA,QACEU,MAAO,CACLS,MAAkB,OAAXnD,QAAW,IAAXA,GAAmB,QAARJ,EAAXI,EAAayD,cAAM,IAAA7D,GAAnBA,EAAqB0E,gBACxB,UACA,UACJd,WAAY,OACZtB,SAEU,OAAXlC,QAAW,IAAXA,GAAmB,QAARH,EAAXG,EAAayD,cAAM,IAAA5D,GAAnBA,EAAqByE,gBAClBxE,EAAE,qCACFA,EAAE,iEAY1BkC,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SACG3B,EAAgBsB,WAAW,aAC1BG,EAAAA,EAAAA,KAAA,UACEuC,IAAKhE,EACLmC,MAAO,CAAE8B,MAAO,OAAQC,OAAQ,QAASC,OAAQ,WAGnD1C,EAAAA,EAAAA,KAAA,OACEU,MAAO,CAAEiC,SAAU,UACnBC,wBAAyB,CAAEC,OAAQtE,S", "sources": ["pages/Home/index.js"], "sourcesContent": ["import React, { useContext, useEffect, useState } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { <PERSON>, Grid, Header } from 'semantic-ui-react';\r\nimport { API, showError, showNotice, timestamp2string } from '../../helpers';\r\nimport { StatusContext } from '../../context/Status';\r\nimport { marked } from 'marked';\r\nimport { UserContext } from '../../context/User';\r\nimport { Link } from 'react-router-dom';\r\n\r\nconst Home = () => {\r\n  const { t } = useTranslation();\r\n  const [statusState, statusDispatch] = useContext(StatusContext);\r\n  const [homePageContentLoaded, setHomePageContentLoaded] = useState(false);\r\n  const [homePageContent, setHomePageContent] = useState('');\r\n  const [userState] = useContext(UserContext);\r\n\r\n  const displayNotice = async () => {\r\n    const res = await API.get('/api/notice');\r\n    const { success, message, data } = res.data;\r\n    if (success) {\r\n      let oldNotice = localStorage.getItem('notice');\r\n      if (data !== oldNotice && data !== '') {\r\n        const htmlNotice = marked(data);\r\n        showNotice(htmlNotice, true);\r\n        localStorage.setItem('notice', data);\r\n      }\r\n    } else {\r\n      showError(message);\r\n    }\r\n  };\r\n\r\n  const displayHomePageContent = async () => {\r\n    setHomePageContent(localStorage.getItem('home_page_content') || '');\r\n    const res = await API.get('/api/home_page_content');\r\n    const { success, message, data } = res.data;\r\n    if (success) {\r\n      let content = data;\r\n      if (!data.startsWith('https://')) {\r\n        content = marked.parse(data);\r\n      }\r\n      setHomePageContent(content);\r\n      localStorage.setItem('home_page_content', content);\r\n    } else {\r\n      showError(message);\r\n      setHomePageContent(t('home.loading_failed'));\r\n    }\r\n    setHomePageContentLoaded(true);\r\n  };\r\n\r\n  const getStartTimeString = () => {\r\n    const timestamp = statusState?.status?.start_time;\r\n    return timestamp2string(timestamp);\r\n  };\r\n\r\n  useEffect(() => {\r\n    displayNotice().then();\r\n    displayHomePageContent().then();\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      {homePageContentLoaded && homePageContent === '' ? (\r\n        <div className='dashboard-container'>\r\n          <Card fluid className='chart-card'>\r\n            <Card.Content>\r\n              <Card.Header className='header'>\r\n                {t('home.welcome.title')}\r\n              </Card.Header>\r\n              <Card.Description style={{ lineHeight: '1.6' }}>\r\n                <p>{t('home.welcome.description')}</p>\r\n                {!userState.user && <p>{t('home.welcome.login_notice')}</p>}\r\n              </Card.Description>\r\n            </Card.Content>\r\n          </Card>\r\n          <Card fluid className='chart-card'>\r\n            <Card.Content>\r\n              <Card.Header>\r\n                <Header as='h3'>{t('home.system_status.title')}</Header>\r\n              </Card.Header>\r\n              <Grid columns={2} stackable>\r\n                <Grid.Column>\r\n                  <Card\r\n                    fluid\r\n                    className='chart-card'\r\n                    style={{ boxShadow: '0 1px 3px rgba(0,0,0,0.12)' }}\r\n                  >\r\n                    <Card.Content>\r\n                      <Card.Header>\r\n                        <Header as='h3' style={{ color: '#444' }}>\r\n                          {t('home.system_status.info.title')}\r\n                        </Header>\r\n                      </Card.Header>\r\n                      <Card.Description\r\n                        style={{ lineHeight: '2', marginTop: '1em' }}\r\n                      >\r\n                        <p\r\n                          style={{\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            gap: '0.5em',\r\n                          }}\r\n                        >\r\n                          <i className='info circle icon'></i>\r\n                          <span style={{ fontWeight: 'bold' }}>\r\n                            {t('home.system_status.info.name')}\r\n                          </span>\r\n                          <span>{statusState?.status?.system_name}</span>\r\n                        </p>\r\n                        <p\r\n                          style={{\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            gap: '0.5em',\r\n                          }}\r\n                        >\r\n                          <i className='code branch icon'></i>\r\n                          <span style={{ fontWeight: 'bold' }}>\r\n                            {t('home.system_status.info.version')}\r\n                          </span>\r\n                          <span>\r\n                            {statusState?.status?.version || 'unknown'}\r\n                          </span>\r\n                        </p>\r\n                        <p\r\n                          style={{\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            gap: '0.5em',\r\n                          }}\r\n                        >\r\n                          <i className='github icon'></i>\r\n                          <span style={{ fontWeight: 'bold' }}>\r\n                            {t('home.system_status.info.source')}\r\n                          </span>\r\n                          <a\r\n                            href='https://github.com/songquanpeng/one-api'\r\n                            target='_blank'\r\n                            style={{ color: '#2185d0' }}\r\n                          >\r\n                            {t('home.system_status.info.source_link')}\r\n                          </a>\r\n                        </p>\r\n                        <p\r\n                          style={{\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            gap: '0.5em',\r\n                          }}\r\n                        >\r\n                          <i className='clock outline icon'></i>\r\n                          <span style={{ fontWeight: 'bold' }}>\r\n                            {t('home.system_status.info.start_time')}\r\n                          </span>\r\n                          <span>{getStartTimeString()}</span>\r\n                        </p>\r\n                      </Card.Description>\r\n                    </Card.Content>\r\n                  </Card>\r\n                </Grid.Column>\r\n\r\n                <Grid.Column>\r\n                  <Card\r\n                    fluid\r\n                    className='chart-card'\r\n                    style={{ boxShadow: '0 1px 3px rgba(0,0,0,0.12)' }}\r\n                  >\r\n                    <Card.Content>\r\n                      <Card.Header>\r\n                        <Header as='h3' style={{ color: '#444' }}>\r\n                          {t('home.system_status.config.title')}\r\n                        </Header>\r\n                      </Card.Header>\r\n                      <Card.Description\r\n                        style={{ lineHeight: '2', marginTop: '1em' }}\r\n                      >\r\n                        <p\r\n                          style={{\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            gap: '0.5em',\r\n                          }}\r\n                        >\r\n                          <i className='envelope icon'></i>\r\n                          <span style={{ fontWeight: 'bold' }}>\r\n                            {t('home.system_status.config.email_verify')}\r\n                          </span>\r\n                          <span\r\n                            style={{\r\n                              color: statusState?.status?.email_verification\r\n                                ? '#21ba45'\r\n                                : '#db2828',\r\n                              fontWeight: '500',\r\n                            }}\r\n                          >\r\n                            {statusState?.status?.email_verification\r\n                              ? t('home.system_status.config.enabled')\r\n                              : t('home.system_status.config.disabled')}\r\n                          </span>\r\n                        </p>\r\n                        <p\r\n                          style={{\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            gap: '0.5em',\r\n                          }}\r\n                        >\r\n                          <i className='github icon'></i>\r\n                          <span style={{ fontWeight: 'bold' }}>\r\n                            {t('home.system_status.config.github_oauth')}\r\n                          </span>\r\n                          <span\r\n                            style={{\r\n                              color: statusState?.status?.github_oauth\r\n                                ? '#21ba45'\r\n                                : '#db2828',\r\n                              fontWeight: '500',\r\n                            }}\r\n                          >\r\n                            {statusState?.status?.github_oauth\r\n                              ? t('home.system_status.config.enabled')\r\n                              : t('home.system_status.config.disabled')}\r\n                          </span>\r\n                        </p>\r\n                        <p\r\n                          style={{\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            gap: '0.5em',\r\n                          }}\r\n                        >\r\n                          <i className='wechat icon'></i>\r\n                          <span style={{ fontWeight: 'bold' }}>\r\n                            {t('home.system_status.config.wechat_login')}\r\n                          </span>\r\n                          <span\r\n                            style={{\r\n                              color: statusState?.status?.wechat_login\r\n                                ? '#21ba45'\r\n                                : '#db2828',\r\n                              fontWeight: '500',\r\n                            }}\r\n                          >\r\n                            {statusState?.status?.wechat_login\r\n                              ? t('home.system_status.config.enabled')\r\n                              : t('home.system_status.config.disabled')}\r\n                          </span>\r\n                        </p>\r\n                        <p\r\n                          style={{\r\n                            display: 'flex',\r\n                            alignItems: 'center',\r\n                            gap: '0.5em',\r\n                          }}\r\n                        >\r\n                          <i className='shield alternate icon'></i>\r\n                          <span style={{ fontWeight: 'bold' }}>\r\n                            {t('home.system_status.config.turnstile')}\r\n                          </span>\r\n                          <span\r\n                            style={{\r\n                              color: statusState?.status?.turnstile_check\r\n                                ? '#21ba45'\r\n                                : '#db2828',\r\n                              fontWeight: '500',\r\n                            }}\r\n                          >\r\n                            {statusState?.status?.turnstile_check\r\n                              ? t('home.system_status.config.enabled')\r\n                              : t('home.system_status.config.disabled')}\r\n                          </span>\r\n                        </p>\r\n                      </Card.Description>\r\n                    </Card.Content>\r\n                  </Card>\r\n                </Grid.Column>\r\n              </Grid>\r\n            </Card.Content>\r\n          </Card>\r\n        </div>\r\n      ) : (\r\n        <>\r\n          {homePageContent.startsWith('https://') ? (\r\n            <iframe\r\n              src={homePageContent}\r\n              style={{ width: '100%', height: '100vh', border: 'none' }}\r\n            />\r\n          ) : (\r\n            <div\r\n              style={{ fontSize: 'larger' }}\r\n              dangerouslySetInnerHTML={{ __html: homePageContent }}\r\n            ></div>\r\n          )}\r\n        </>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "names": ["Home", "_statusState$status2", "_statusState$status3", "_statusState$status4", "_statusState$status5", "_statusState$status6", "_statusState$status7", "_statusState$status8", "_statusState$status9", "_statusState$status0", "_statusState$status1", "t", "useTranslation", "statusState", "statusDispatch", "useContext", "StatusContext", "homePageContentLoaded", "setHomePageContentLoaded", "useState", "homePageContent", "setHomePageContent", "userState", "UserContext", "useEffect", "async", "res", "API", "get", "success", "message", "data", "localStorage", "getItem", "htmlNotice", "marked", "showNotice", "setItem", "showError", "displayNotice", "then", "content", "startsWith", "parse", "displayHomePageContent", "_jsx", "_Fragment", "children", "_jsxs", "className", "Card", "fluid", "Content", "Header", "Description", "style", "lineHeight", "user", "as", "Grid", "columns", "stackable", "Column", "boxShadow", "color", "marginTop", "display", "alignItems", "gap", "fontWeight", "status", "system_name", "version", "href", "target", "getStartTimeString", "_statusState$status", "timestamp", "start_time", "timestamp2string", "email_verification", "github_oauth", "wechat_login", "turnstile_check", "src", "width", "height", "border", "fontSize", "dangerouslySetInnerHTML", "__html"], "sourceRoot": ""}