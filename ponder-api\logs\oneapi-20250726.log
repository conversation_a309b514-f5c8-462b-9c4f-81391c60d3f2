[INFO] 2025/07/26 - 09:31:17 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 09:31:17 | D:/Projects/ponder-all/ponder-api/model/main.go:103 [openSQLite] SQL_DSN not set, using SQLite as database 
[FATAL] 2025/07/26 - 09:31:17 | D:/Projects/ponder-all/ponder-api/model/main.go:115 [InitDB] failed to initialize database: Binary was compiled with 'CGO_ENABLED=0', go-sqlite3 requires cgo to work. This is a stub 
[INFO] 2025/07/26 - 09:32:07 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 09:32:07 | D:/Projects/ponder-all/ponder-api/model/main.go:103 [openSQLite] SQL_DSN not set, using SQLite as database 
[FATAL] 2025/07/26 - 09:32:07 | D:/Projects/ponder-all/ponder-api/model/main.go:115 [InitDB] failed to initialize database: Binary was compiled with 'CGO_ENABLED=0', go-sqlite3 requires cgo to work. This is a stub 
[INFO] 2025/07/26 - 09:41:53 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 09:41:53 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[FATAL] 2025/07/26 - 09:41:53 | D:/Projects/ponder-all/ponder-api/model/main.go:115 [InitDB] failed to initialize database: Error 1045 (28000): Access denied for user 'root'@'172.18.0.1' (using password: NO) 
[INFO] 2025/07/26 - 09:43:20 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 09:43:20 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[FATAL] 2025/07/26 - 09:43:20 | D:/Projects/ponder-all/ponder-api/model/main.go:115 [InitDB] failed to initialize database: Error 1044 (42000): Access denied for user 'oneapi'@'%' to database 'ponder_api' 
[INFO] 2025/07/26 - 09:57:12 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 09:57:12 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[FATAL] 2025/07/26 - 09:57:12 | D:/Projects/ponder-all/ponder-api/model/main.go:115 [InitDB] failed to initialize database: Error 1049 (42000): Unknown database 'ponder_api' 
[INFO] 2025/07/26 - 09:57:40 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 09:57:40 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[INFO] 2025/07/26 - 09:57:40 | D:/Projects/ponder-all/ponder-api/model/main.go:129 [InitDB] database migration started 
[INFO] 2025/07/26 - 09:57:40 | D:/Projects/ponder-all/ponder-api/model/main.go:134 [InitDB] database migrated 
[INFO] 2025/07/26 - 09:57:40 | D:/Projects/ponder-all/ponder-api/model/main.go:28 [CreateRootAccountIfNeed] no user exists, creating a root user for you: username is root, password is 123456 
[INFO] 2025/07/26 - 09:57:40 | D:/Projects/ponder-all/ponder-api/model/main.go:48 [CreateRootAccountIfNeed] creating initial root token as requested 
[INFO] 2025/07/26 - 09:57:40 | D:/Projects/ponder-all/ponder-api/common/redis.go:20 [InitRedisClient] REDIS_CONN_STRING not set, Redis is not enabled 
[INFO] 2025/07/26 - 09:57:40 | D:/Projects/ponder-all/ponder-api/main.go:65 [main] using theme default 
[INFO] 2025/07/26 - 09:57:40 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:23 [InitTokenEncoders] initializing token encoders 
[INFO] 2025/07/26 - 09:57:41 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:49 [InitTokenEncoders] token encoders initialized 
[INFO] 2025/07/26 - 09:57:41 | D:/Projects/ponder-all/ponder-api/main.go:119 [main] server started on http://localhost:3000 
[GIN] 2025/07/26 - 09:57:50 | 2025072609575091464640054263256 | 200 |            0s |             ::1 |     GET /test-auth.html?redirect_uri=vscode://test&state=test123
[GIN] 2025/07/26 - 09:57:50 | 2025072609575094584910051193305 | 200 |            0s |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 09:57:58 | 2025072609575853329410016365967 | 200 |       518.3µs |             ::1 |     GET /
[GIN] 2025/07/26 - 09:57:58 | 2025072609575856022230031815301 | 200 |            0s |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 09:58:00 | 202507260958006165600080026732 | 200 |            0s |             ::1 |     GET /
[GIN] 2025/07/26 - 09:58:00 | 202507260958008007690009029314 | 200 |            0s |             ::1 |     GET /favicon.ico
[INFO] 2025/07/26 - 10:17:52 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 10:17:52 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[INFO] 2025/07/26 - 10:17:52 | D:/Projects/ponder-all/ponder-api/model/main.go:129 [InitDB] database migration started 
[INFO] 2025/07/26 - 10:17:52 | D:/Projects/ponder-all/ponder-api/model/main.go:134 [InitDB] database migrated 
[INFO] 2025/07/26 - 10:17:52 | D:/Projects/ponder-all/ponder-api/common/redis.go:20 [InitRedisClient] REDIS_CONN_STRING not set, Redis is not enabled 
[INFO] 2025/07/26 - 10:17:52 | D:/Projects/ponder-all/ponder-api/main.go:65 [main] using theme default 
[INFO] 2025/07/26 - 10:17:52 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:23 [InitTokenEncoders] initializing token encoders 
[INFO] 2025/07/26 - 10:17:52 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:49 [InitTokenEncoders] token encoders initialized 
[INFO] 2025/07/26 - 10:17:52 | D:/Projects/ponder-all/ponder-api/main.go:119 [main] server started on http://localhost:3000 
[FATAL] 2025/07/26 - 10:17:52 | D:/Projects/ponder-all/ponder-api/main.go:122 [main] failed to start HTTP server: listen tcp :3000: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted. 
[INFO] 2025/07/26 - 10:18:51 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 10:18:51 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[INFO] 2025/07/26 - 10:18:51 | D:/Projects/ponder-all/ponder-api/model/main.go:129 [InitDB] database migration started 
[INFO] 2025/07/26 - 10:18:51 | D:/Projects/ponder-all/ponder-api/model/main.go:134 [InitDB] database migrated 
[INFO] 2025/07/26 - 10:18:51 | D:/Projects/ponder-all/ponder-api/common/redis.go:20 [InitRedisClient] REDIS_CONN_STRING not set, Redis is not enabled 
[INFO] 2025/07/26 - 10:18:51 | D:/Projects/ponder-all/ponder-api/main.go:65 [main] using theme default 
[INFO] 2025/07/26 - 10:18:51 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:23 [InitTokenEncoders] initializing token encoders 
[INFO] 2025/07/26 - 10:18:52 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:49 [InitTokenEncoders] token encoders initialized 
[INFO] 2025/07/26 - 10:18:52 | D:/Projects/ponder-all/ponder-api/main.go:119 [main] server started on http://localhost:3000 
[GIN] 2025/07/26 - 10:19:10 | 2025072610191091871900074277411 | 200 |            0s |             ::1 |     GET /
[GIN] 2025/07/26 - 10:19:10 | 2025072610191094840580083127709 | 200 |            0s |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:21:00 | 2025072610210056379370080394352 | 200 |      1.5398ms |             ::1 |     GET /
[GIN] 2025/07/26 - 10:21:00 | 2025072610210057859560041107438 | 200 |            0s |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:21:05 | 2025072610210572027590087228841 | 200 |            0s |             ::1 |     GET /index.html
[GIN] 2025/07/26 - 10:21:05 | 2025072610210574270190042882159 | 200 |            0s |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:21:07 | 2025072610210792363560072329162 | 200 |            0s |             ::1 |     GET /index
[GIN] 2025/07/26 - 10:21:07 | 2025072610210794492770056592765 | 200 |            0s |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:21:10 | 2025072610211021888980095071533 | 200 |            0s |             ::1 |     GET /
[GIN] 2025/07/26 - 10:21:10 | 2025072610211023908060026833222 | 200 |            0s |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:21:49 | 2025072610214928120420068730547 | 200 |            0s |             ::1 |     GET /
[GIN] 2025/07/26 - 10:21:49 | 2025072610214929824980020684129 | 200 |            0s |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:34:08 | 202507261034086172350028286346 | 200 |            0s |       127.0.0.1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 10:34:08 | 2025072610340811832020089108561 | 200 |            0s |             ::1 |     GET /oauth/ponder?client_id=ponder-client&redirect_uri=http://127.0.0.1:61655/callback&state=AgEAODkUmqDh&scope=read+write&response_type=code
[GIN] 2025/07/26 - 10:34:08 | 2025072610340814508110005641821 | 200 |            0s |             ::1 |     GET /favicon.ico
[INFO] 2025/07/26 - 10:37:12 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 10:37:12 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[INFO] 2025/07/26 - 10:37:12 | D:/Projects/ponder-all/ponder-api/model/main.go:129 [InitDB] database migration started 
[INFO] 2025/07/26 - 10:37:12 | D:/Projects/ponder-all/ponder-api/model/main.go:134 [InitDB] database migrated 
[INFO] 2025/07/26 - 10:37:12 | D:/Projects/ponder-all/ponder-api/common/redis.go:20 [InitRedisClient] REDIS_CONN_STRING not set, Redis is not enabled 
[INFO] 2025/07/26 - 10:37:12 | D:/Projects/ponder-all/ponder-api/main.go:65 [main] using theme default 
[INFO] 2025/07/26 - 10:37:12 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:23 [InitTokenEncoders] initializing token encoders 
[INFO] 2025/07/26 - 10:37:12 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:49 [InitTokenEncoders] token encoders initialized 
[INFO] 2025/07/26 - 10:37:12 | D:/Projects/ponder-all/ponder-api/main.go:119 [main] server started on http://localhost:3000 
[GIN] 2025/07/26 - 10:37:30 | 2025072610373071982450009581474 | 200 |            0s |             ::1 |     GET /
[GIN] 2025/07/26 - 10:37:30 | 2025072610373074693170051839230 | 200 |            0s |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:37:45 | 2025072610374582099370088267189 | 200 |            0s |             ::1 |     GET /
[GIN] 2025/07/26 - 10:37:45 | 2025072610374583706550090549522 | 200 |       502.1µs |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:37:46 | 2025072610374613626680087227890 | 200 |            0s |             ::1 |     GET /
[GIN] 2025/07/26 - 10:37:46 | 2025072610374615440570095068602 | 200 |            0s |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:37:46 | 2025072610374629290380035971865 | 200 |            0s |             ::1 |     GET /
[GIN] 2025/07/26 - 10:37:46 | 2025072610374630897420042149886 | 200 |       528.5µs |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:37:46 | 2025072610374643277650022193645 | 200 |            0s |             ::1 |     GET /
[GIN] 2025/07/26 - 10:37:46 | 2025072610374644905360080829517 | 200 |            0s |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:37:46 | 2025072610374657196650027031727 | 200 |            0s |             ::1 |     GET /
[GIN] 2025/07/26 - 10:37:46 | 2025072610374658825880091384348 | 200 |            0s |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:37:46 | 2025072610374669810180017282562 | 200 |            0s |             ::1 |     GET /
[GIN] 2025/07/26 - 10:37:46 | 2025072610374671457970071848602 | 200 |            0s |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:37:46 | 2025072610374682816930093078495 | 200 |            0s |             ::1 |     GET /
[GIN] 2025/07/26 - 10:37:46 | 2025072610374684520390043550023 | 200 |            0s |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:37:51 | 2025072610375174857100006621390 | 200 |            0s |             ::1 |     GET /.well-known/appspecific/com.chrome.devtools.json
[INFO] 2025/07/26 - 10:40:55 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 10:40:55 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[INFO] 2025/07/26 - 10:40:55 | D:/Projects/ponder-all/ponder-api/model/main.go:129 [InitDB] database migration started 
[INFO] 2025/07/26 - 10:40:55 | D:/Projects/ponder-all/ponder-api/model/main.go:134 [InitDB] database migrated 
[INFO] 2025/07/26 - 10:40:55 | D:/Projects/ponder-all/ponder-api/common/redis.go:20 [InitRedisClient] REDIS_CONN_STRING not set, Redis is not enabled 
[INFO] 2025/07/26 - 10:40:55 | D:/Projects/ponder-all/ponder-api/main.go:65 [main] using theme default 
[INFO] 2025/07/26 - 10:40:55 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:23 [InitTokenEncoders] initializing token encoders 
[INFO] 2025/07/26 - 10:40:55 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:49 [InitTokenEncoders] token encoders initialized 
[INFO] 2025/07/26 - 10:40:55 | D:/Projects/ponder-all/ponder-api/main.go:119 [main] server started on http://localhost:3000 
[GIN] 2025/07/26 - 10:41:16 | 2025072610411658497940060460552 | 200 |     28.6986ms |             ::1 |     GET /
[GIN] 2025/07/26 - 10:41:16 | 2025072610411662169670064476324 | 200 |     20.7784ms |             ::1 |     GET /static/js/main.b7b83eed.js
[GIN] 2025/07/26 - 10:41:16 | 2025072610411668816960095402387 | 200 |            0s |             ::1 |     GET /static/js/151.da0f3c02.chunk.js
[GIN] 2025/07/26 - 10:41:16 | 2025072610411670362450027230604 | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/26 - 10:41:16 | 2025072610411670881060088683859 | 200 |            0s |             ::1 |     GET /api/notice
[GIN] 2025/07/26 - 10:41:16 | 2025072610411670881060088683859 | 200 |            0s |             ::1 |     GET /api/home_page_content
[GIN] 2025/07/26 - 10:41:16 | 2025072610411672592320047227045 | 200 |       503.6µs |             ::1 |     GET /static/media/outline-icons.687a4990ea22bb1a49d4.woff2
[GIN] 2025/07/26 - 10:41:16 | 2025072610411672592320047227045 | 200 |      1.0246ms |             ::1 |     GET /static/media/brand-icons.278156e41e0ad908cf7f.woff2
[GIN] 2025/07/26 - 10:41:25 | 202507261041252200160074211553 | 200 |            0s |             ::1 |     GET /oauth/ponder?client_id=ponder-client&redirect_uri=http://127.0.0.1:61655/callback&state=AgEAODkUmqDh&scope=read+write&response_type=code
[GIN] 2025/07/26 - 10:41:25 | 202507261041259447750044820118 | 200 |            0s |             ::1 |     GET /oauth/logo.png
[GIN] 2025/07/26 - 10:41:25 | 202507261041259598590076861207 | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/26 - 10:41:30 | 2025072610413086600320010725122 | 200 |            0s |             ::1 |     GET /.well-known/appspecific/com.chrome.devtools.json
[GIN] 2025/07/26 - 10:41:30 | 2025072610413095538360048535145 | 200 |     14.5254ms |             ::1 |     GET /static/css/main.722059cf.css.map
[GIN] 2025/07/26 - 10:41:31 | 2025072610413094233360062837129 | 200 |    107.0376ms |             ::1 |     GET /static/js/main.b7b83eed.js.map
[GIN] 2025/07/26 - 10:41:34 | 2025072610413492714670051212447 | 400 |            0s |             ::1 |    POST /api/oauth/ponder/authorize
[GIN] 2025/07/26 - 10:42:18 | 2025072610421847863670000001362 | 200 |       503.8µs |       127.0.0.1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 10:42:18 | 2025072610421852472290041666218 | 200 |            0s |             ::1 |     GET /oauth/ponder?client_id=ponder-client&redirect_uri=http://127.0.0.1:62990/callback&state=880aBpaO2Opl&scope=read+write&response_type=code
[GIN] 2025/07/26 - 10:42:18 | 2025072610421860316580084771720 | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/26 - 10:42:18 | 2025072610421860740900005833721 | 200 |            0s |             ::1 |     GET /oauth/logo.png
[GIN] 2025/07/26 - 10:42:20 | 2025072610422092849610005694440 | 200 |            0s |             ::1 |     GET /.well-known/appspecific/com.chrome.devtools.json
[GIN] 2025/07/26 - 10:42:43 | 2025072610424314713650019271385 | 400 |            0s |             ::1 |    POST /api/oauth/ponder/authorize
[INFO] 2025/07/26 - 10:44:17 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 10:44:17 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[INFO] 2025/07/26 - 10:44:17 | D:/Projects/ponder-all/ponder-api/model/main.go:129 [InitDB] database migration started 
[INFO] 2025/07/26 - 10:44:17 | D:/Projects/ponder-all/ponder-api/model/main.go:134 [InitDB] database migrated 
[INFO] 2025/07/26 - 10:44:17 | D:/Projects/ponder-all/ponder-api/common/redis.go:20 [InitRedisClient] REDIS_CONN_STRING not set, Redis is not enabled 
[INFO] 2025/07/26 - 10:44:17 | D:/Projects/ponder-all/ponder-api/main.go:65 [main] using theme default 
[INFO] 2025/07/26 - 10:44:17 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:23 [InitTokenEncoders] initializing token encoders 
[INFO] 2025/07/26 - 10:44:17 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:49 [InitTokenEncoders] token encoders initialized 
[INFO] 2025/07/26 - 10:44:17 | D:/Projects/ponder-all/ponder-api/main.go:119 [main] server started on http://localhost:3000 
[GIN] 2025/07/26 - 10:44:34 | 2025072610443454646500038331048 | 200 |       504.7µs |             ::1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 10:44:34 | 2025072610443457785590060489813 | 200 |      26.421ms |             ::1 |     GET /favicon.ico
[GIN] 2025/07/26 - 10:46:00 | 2025072610460035325440077128760 | 200 |            0s |       127.0.0.1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 10:46:00 | 2025072610460040636590076135333 | 200 |         505µs |             ::1 |     GET /oauth/ponder?client_id=ponder-client&redirect_uri=http://127.0.0.1:63595/callback&state=x3FOgb6UgCLJ&scope=read+write&response_type=code
[GIN] 2025/07/26 - 10:46:00 | 2025072610460048994300027855130 | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/26 - 10:46:00 | 2025072610460049430520050493229 | 200 |            0s |             ::1 |     GET /oauth/logo.png
[GIN] 2025/07/26 - 10:46:04 | 2025072610460489130040033643950 | 200 |            0s |             ::1 |     GET /.well-known/appspecific/com.chrome.devtools.json
[GIN] 2025/07/26 - 10:46:05 | 2025072610460561980950088418391 | 400 |            0s |             ::1 |    POST /api/oauth/ponder/authorize
[INFO] 2025/07/26 - 10:47:41 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 10:47:41 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[INFO] 2025/07/26 - 10:47:41 | D:/Projects/ponder-all/ponder-api/model/main.go:129 [InitDB] database migration started 
[INFO] 2025/07/26 - 10:47:41 | D:/Projects/ponder-all/ponder-api/model/main.go:134 [InitDB] database migrated 
[INFO] 2025/07/26 - 10:47:41 | D:/Projects/ponder-all/ponder-api/common/redis.go:20 [InitRedisClient] REDIS_CONN_STRING not set, Redis is not enabled 
[INFO] 2025/07/26 - 10:47:41 | D:/Projects/ponder-all/ponder-api/main.go:65 [main] using theme default 
[INFO] 2025/07/26 - 10:47:41 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:23 [InitTokenEncoders] initializing token encoders 
[INFO] 2025/07/26 - 10:47:41 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:49 [InitTokenEncoders] token encoders initialized 
[INFO] 2025/07/26 - 10:47:41 | D:/Projects/ponder-all/ponder-api/main.go:119 [main] server started on http://localhost:3000 
[GIN] 2025/07/26 - 10:49:25 | 2025072610492519186610034064056 | 200 |            0s |       127.0.0.1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 10:49:25 | 2025072610492524976080070456558 | 200 |       518.2µs |             ::1 |     GET /oauth/ponder?client_id=ponder-client&redirect_uri=http://127.0.0.1:64170/callback&state=pKsyEcf6Mx1k&scope=read+write&response_type=code
[GIN] 2025/07/26 - 10:49:25 | 2025072610492533193060027664565 | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/26 - 10:49:25 | 2025072610492533652780029578978 | 200 |            0s |             ::1 |     GET /oauth/logo.png
[GIN] 2025/07/26 - 10:49:29 | 2025072610492961652080009627500 | 200 |            0s |             ::1 |     GET /.well-known/appspecific/com.chrome.devtools.json
[GIN] 2025/07/26 - 10:49:30 | 2025072610493026617280075782812 | 400 |            0s |             ::1 |    POST /api/oauth/ponder/authorize
[INFO] 2025/07/26 - 10:52:50 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 10:52:50 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[INFO] 2025/07/26 - 10:52:50 | D:/Projects/ponder-all/ponder-api/model/main.go:129 [InitDB] database migration started 
[INFO] 2025/07/26 - 10:52:50 | D:/Projects/ponder-all/ponder-api/model/main.go:134 [InitDB] database migrated 
[INFO] 2025/07/26 - 10:52:50 | D:/Projects/ponder-all/ponder-api/common/redis.go:20 [InitRedisClient] REDIS_CONN_STRING not set, Redis is not enabled 
[INFO] 2025/07/26 - 10:52:50 | D:/Projects/ponder-all/ponder-api/main.go:65 [main] using theme default 
[INFO] 2025/07/26 - 10:52:50 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:23 [InitTokenEncoders] initializing token encoders 
[INFO] 2025/07/26 - 10:52:50 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:49 [InitTokenEncoders] token encoders initialized 
[INFO] 2025/07/26 - 10:52:50 | D:/Projects/ponder-all/ponder-api/main.go:119 [main] server started on http://localhost:3000 
[GIN] 2025/07/26 - 10:54:19 | 2025072610541978090890082541398 | 200 |            0s |             ::1 |     GET /oauth/ponder?client_id=ponder-client&redirect_uri=http://127.0.0.1:64170/callback&state=pKsyEcf6Mx1k&scope=read+write&response_type=code
[GIN] 2025/07/26 - 10:54:19 | 2025072610541979835620077142174 | 200 |       512.5µs |             ::1 |     GET /.well-known/appspecific/com.chrome.devtools.json
[GIN] 2025/07/26 - 10:54:19 | 2025072610541979211010034785081 | 200 |     44.0879ms |             ::1 |     GET /static/js/main.c2f3dde0.js
[GIN] 2025/07/26 - 10:54:19 | 2025072610541989240500022355732 | 200 |            0s |             ::1 |     GET /oauth/logo.png
[GIN] 2025/07/26 - 10:54:19 | 2025072610541989604770058868072 | 200 |       523.6µs |             ::1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 10:54:19 | 2025072610541989657130088955976 | 200 |       516.2µs |             ::1 |     GET /api/status
[GIN] 2025/07/26 - 10:54:19 | 2025072610541987974670089804149 | 200 |     98.9894ms |             ::1 |     GET /static/js/main.c2f3dde0.js.map
[GIN] 2025/07/26 - 10:54:31 | 2025072610543175324240093027270 | 500 |     53.6177ms |             ::1 |    POST /api/oauth/ponder/authorize
[GIN] 2025/07/26 - 10:54:47 | 2025072610544777316420029493845 | 200 |            0s |       127.0.0.1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 10:54:47 | 2025072610544782560150024480175 | 200 |            0s |             ::1 |     GET /oauth/ponder?client_id=ponder-client&redirect_uri=http://127.0.0.1:65044/callback&state=GlkBDOCRx8iZ&scope=read+write&response_type=code
[GIN] 2025/07/26 - 10:54:47 | 2025072610544791507700018253146 | 200 |            0s |             ::1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 10:54:47 | 2025072610544791507700018253146 | 200 |       503.5µs |             ::1 |     GET /api/status
[GIN] 2025/07/26 - 10:54:47 | 2025072610544792865340095236076 | 200 |            0s |             ::1 |     GET /oauth/logo.png
[GIN] 2025/07/26 - 10:54:51 | 2025072610545185964250012977855 | 200 |            0s |             ::1 |     GET /.well-known/appspecific/com.chrome.devtools.json
[GIN] 2025/07/26 - 10:54:52 | 2025072610545261765330094043299 | 500 |     37.4696ms |             ::1 |    POST /api/oauth/ponder/authorize
[INFO] 2025/07/26 - 10:56:19 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 10:56:19 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[INFO] 2025/07/26 - 10:56:19 | D:/Projects/ponder-all/ponder-api/model/main.go:129 [InitDB] database migration started 
[INFO] 2025/07/26 - 10:56:19 | D:/Projects/ponder-all/ponder-api/model/main.go:134 [InitDB] database migrated 
[INFO] 2025/07/26 - 10:56:19 | D:/Projects/ponder-all/ponder-api/common/redis.go:20 [InitRedisClient] REDIS_CONN_STRING not set, Redis is not enabled 
[INFO] 2025/07/26 - 10:56:19 | D:/Projects/ponder-all/ponder-api/main.go:65 [main] using theme default 
[INFO] 2025/07/26 - 10:56:19 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:23 [InitTokenEncoders] initializing token encoders 
[INFO] 2025/07/26 - 10:56:20 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:49 [InitTokenEncoders] token encoders initialized 
[INFO] 2025/07/26 - 10:56:20 | D:/Projects/ponder-all/ponder-api/main.go:119 [main] server started on http://localhost:3000 
[GIN] 2025/07/26 - 10:57:23 | 2025072610572394134200022643127 | 200 |            0s |       127.0.0.1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 10:57:23 | 2025072610572399563050053918341 | 200 |         780µs |             ::1 |     GET /oauth/ponder?client_id=ponder-client&redirect_uri=http://127.0.0.1:65436/callback&state=qqiaVvkz7jCG&scope=read+write&response_type=code
[GIN] 2025/07/26 - 10:57:24 | 202507261057247008150089667729 | 200 |            0s |             ::1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 10:57:24 | 202507261057247008150089667729 | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/26 - 10:57:24 | 202507261057247351150054503642 | 200 |            0s |             ::1 |     GET /oauth/logo.png
[GIN] 2025/07/26 - 10:57:25 | 2025072610572578361490085536523 | 200 |      1.0003ms |             ::1 |     GET /.well-known/appspecific/com.chrome.devtools.json
[GIN] 2025/07/26 - 10:57:29 | 2025072610572968184720057570192 | 500 |     48.9426ms |             ::1 |    POST /api/oauth/ponder/authorize
[INFO] 2025/07/26 - 10:59:35 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 10:59:35 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[INFO] 2025/07/26 - 10:59:35 | D:/Projects/ponder-all/ponder-api/model/main.go:129 [InitDB] database migration started 
[INFO] 2025/07/26 - 10:59:35 | D:/Projects/ponder-all/ponder-api/model/main.go:134 [InitDB] database migrated 
[INFO] 2025/07/26 - 10:59:35 | D:/Projects/ponder-all/ponder-api/common/redis.go:20 [InitRedisClient] REDIS_CONN_STRING not set, Redis is not enabled 
[INFO] 2025/07/26 - 10:59:35 | D:/Projects/ponder-all/ponder-api/main.go:65 [main] using theme default 
[INFO] 2025/07/26 - 10:59:35 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:23 [InitTokenEncoders] initializing token encoders 
[INFO] 2025/07/26 - 10:59:35 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:49 [InitTokenEncoders] token encoders initialized 
[INFO] 2025/07/26 - 10:59:35 | D:/Projects/ponder-all/ponder-api/main.go:119 [main] server started on http://localhost:3000 
[GIN] 2025/07/26 - 11:00:11 | 2025072611001143804380016988572 | 200 |            0s |       127.0.0.1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 11:00:11 | 2025072611001149684150001520275 | 200 |            0s |             ::1 |     GET /oauth/ponder?client_id=ponder-client&redirect_uri=http://127.0.0.1:49585/callback&state=Twz483FqYY3q&scope=read+write&response_type=code
[GIN] 2025/07/26 - 11:00:11 | 2025072611001157582670086981680 | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/26 - 11:00:11 | 2025072611001157582670086981680 | 200 |            0s |             ::1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 11:00:11 | 2025072611001158083640005489081 | 200 |       760.2µs |             ::1 |     GET /oauth/logo.png
[GIN] 2025/07/26 - 11:00:13 | 2025072611001335059250008891032 | 200 |            0s |             ::1 |     GET /.well-known/appspecific/com.chrome.devtools.json
[GIN] 2025/07/26 - 11:00:17 | 2025072611001783817120056963987 | 500 |     38.0405ms |             ::1 |    POST /api/oauth/ponder/authorize
[INFO] 2025/07/26 - 11:02:38 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 11:02:38 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[INFO] 2025/07/26 - 11:02:38 | D:/Projects/ponder-all/ponder-api/model/main.go:129 [InitDB] database migration started 
[INFO] 2025/07/26 - 11:02:38 | D:/Projects/ponder-all/ponder-api/model/main.go:134 [InitDB] database migrated 
[INFO] 2025/07/26 - 11:02:38 | D:/Projects/ponder-all/ponder-api/common/redis.go:20 [InitRedisClient] REDIS_CONN_STRING not set, Redis is not enabled 
[INFO] 2025/07/26 - 11:02:38 | D:/Projects/ponder-all/ponder-api/main.go:65 [main] using theme default 
[INFO] 2025/07/26 - 11:02:38 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:23 [InitTokenEncoders] initializing token encoders 
[INFO] 2025/07/26 - 11:02:38 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:49 [InitTokenEncoders] token encoders initialized 
[INFO] 2025/07/26 - 11:02:38 | D:/Projects/ponder-all/ponder-api/main.go:119 [main] server started on http://localhost:3000 
[GIN] 2025/07/26 - 11:03:42 | 2025072611034217131360019667140 | 200 |            0s |       127.0.0.1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 11:03:42 | 2025072611034222731110018653404 | 200 |       504.5µs |             ::1 |     GET /oauth/ponder?client_id=ponder-client&redirect_uri=http://127.0.0.1:50764/callback&state=5hek3B8KVCEF&scope=read+write&response_type=code
[GIN] 2025/07/26 - 11:03:42 | 2025072611034230237590040563984 | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/26 - 11:03:42 | 2025072611034230237590040563984 | 200 |            0s |             ::1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 11:03:42 | 2025072611034230555980071215737 | 200 |            0s |             ::1 |     GET /oauth/logo.png
[GIN] 2025/07/26 - 11:03:48 | 2025072611034823584880012640706 | 200 |            0s |             ::1 |     GET /.well-known/appspecific/com.chrome.devtools.json
[GIN] 2025/07/26 - 11:03:49 | 2025072611034969114190023649975 | 500 |     48.6777ms |             ::1 |    POST /api/oauth/ponder/authorize
[INFO] 2025/07/26 - 11:07:18 | D:/Projects/ponder-all/ponder-api/main.go:32 [main] One API v0.0.0 started 
[INFO] 2025/07/26 - 11:07:18 | D:/Projects/ponder-all/ponder-api/model/main.go:95 [openMySQL] using MySQL as database 
[INFO] 2025/07/26 - 11:07:18 | D:/Projects/ponder-all/ponder-api/model/main.go:129 [InitDB] database migration started 
[INFO] 2025/07/26 - 11:07:18 | D:/Projects/ponder-all/ponder-api/model/main.go:134 [InitDB] database migrated 
[INFO] 2025/07/26 - 11:07:18 | D:/Projects/ponder-all/ponder-api/common/redis.go:20 [InitRedisClient] REDIS_CONN_STRING not set, Redis is not enabled 
[INFO] 2025/07/26 - 11:07:18 | D:/Projects/ponder-all/ponder-api/main.go:65 [main] using theme default 
[INFO] 2025/07/26 - 11:07:18 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:23 [InitTokenEncoders] initializing token encoders 
[INFO] 2025/07/26 - 11:07:18 | D:/Projects/ponder-all/ponder-api/relay/adaptor/openai/token.go:49 [InitTokenEncoders] token encoders initialized 
[INFO] 2025/07/26 - 11:07:18 | D:/Projects/ponder-all/ponder-api/main.go:119 [main] server started on http://localhost:3000 
[GIN] 2025/07/26 - 11:07:50 | 2025072611075057387310009069432 | 200 |            0s |       127.0.0.1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 11:07:50 | 2025072611075062905060090425861 | 200 |            0s |             ::1 |     GET /oauth/ponder?client_id=ponder-client&redirect_uri=http://127.0.0.1:51569/callback&state=MN20jIjCMZWj&scope=read+write&response_type=code
[GIN] 2025/07/26 - 11:07:50 | 2025072611075070443030049158453 | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/26 - 11:07:50 | 2025072611075070443030042828822 | 200 |            0s |             ::1 |     GET /api/oauth/ponder/state
[GIN] 2025/07/26 - 11:07:50 | 2025072611075071381280068487295 | 200 |            0s |             ::1 |     GET /oauth/logo.png
[GIN] 2025/07/26 - 11:07:54 | 2025072611075464869650083374803 | 200 |            0s |             ::1 |     GET /.well-known/appspecific/com.chrome.devtools.json
[GIN] 2025/07/26 - 11:07:55 | 2025072611075522615260072533471 | 200 |     37.8296ms |             ::1 |    POST /api/oauth/ponder/authorize
[GIN] 2025/07/26 - 11:07:55 | 2025072611075528038660074826244 | 200 |      2.1943ms |       127.0.0.1 |    POST /api/oauth/ponder/token
[GIN] 2025/07/26 - 11:08:28 | 2025072611082838050580084147145 | 200 |     28.5361ms |             ::1 |     GET /
[GIN] 2025/07/26 - 11:08:28 | 2025072611082848529370015742656 | 200 |            0s |             ::1 |     GET /api/status
[GIN] 2025/07/26 - 11:08:28 | 2025072611082849232460095870447 | 200 |            0s |             ::1 |     GET /api/notice
[GIN] 2025/07/26 - 11:08:28 | 2025072611082849232460095870447 | 200 |            0s |             ::1 |     GET /api/home_page_content
[GIN] 2025/07/26 - 11:08:31 | 2025072611083143514750058228451 | 200 |            0s |             ::1 |     GET /.well-known/appspecific/com.chrome.devtools.json
[GIN] 2025/07/26 - 11:08:31 | 2025072611083151747270074021381 | 200 |            0s |             ::1 |     GET /static/js/151.da0f3c02.chunk.js.map
[GIN] 2025/07/26 - 11:08:34 | 2025072611083478450830075908297 | 200 |     47.9065ms |             ::1 |    POST /api/user/login
[GIN] 2025/07/26 - 11:08:34 | 2025072611083484041930083450602 | 200 |      1.8418ms |             ::1 |     GET /api/user/self
