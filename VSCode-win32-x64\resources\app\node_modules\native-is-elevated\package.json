{"name": "native-is-elevated", "version": "0.7.0", "description": "Native module for checking if the process is being run with elevated privileges", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/arkon/native-is-elevated.git"}, "bugs": {"url": "https://github.com/arkon/native-is-elevated/issues"}, "homepage": "https://github.com/arkon/native-is-elevated#readme", "main": "index.js", "types": "index.d.ts", "engine": {"node": ">=12.0.0"}, "scripts": {"configure": "node-gyp configure", "build": "node-gyp build"}}