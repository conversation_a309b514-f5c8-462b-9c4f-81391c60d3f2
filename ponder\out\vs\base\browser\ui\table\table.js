/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
export class TableError extends Error {
    constructor(user, message) {
        super(`TableError [${user}] ${message}`);
    }
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidGFibGUuanMiLCJzb3VyY2VSb290IjoiZmlsZTovLy9EOi9Qcm9qZWN0cy9wb25kZXItYWxsL3BvbmRlci9zcmMvIiwic291cmNlcyI6WyJ2cy9iYXNlL2Jyb3dzZXIvdWkvdGFibGUvdGFibGUudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7OztnR0FHZ0c7QUErQmhHLE1BQU0sT0FBTyxVQUFXLFNBQVEsS0FBSztJQUVwQyxZQUFZLElBQVksRUFBRSxPQUFlO1FBQ3hDLEtBQUssQ0FBQyxlQUFlLElBQUksS0FBSyxPQUFPLEVBQUUsQ0FBQyxDQUFDO0lBQzFDLENBQUM7Q0FDRCJ9