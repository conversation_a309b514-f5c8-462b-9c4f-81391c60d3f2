# Ponder IDE 扩展市场配置

## 概述

Ponder IDE 现已配置扩展市场功能，用户可以搜索、安装和管理来自 Visual Studio Code 市场的扩展。

## 配置详情

扩展市场配置位于 `product.json` 文件中的 `extensionsGallery` 部分：

```json
{
  "extensionsGallery": {
    "serviceUrl": "https://marketplace.visualstudio.com/_apis/public/gallery",
    "controlUrl": "https://az764295.vo.msecnd.net/extensions/marketplace.json",
    "mcpUrl": "https://az764295.vo.msecnd.net/extensions/marketplace.json",
    "extensionUrlTemplate": "https://marketplace.visualstudio.com/_apis/public/gallery/publishers/{publisher}/vsextensions/{name}/{version}/vspackage",
    "resourceUrlTemplate": "https://marketplace.visualstudio.com/_apis/public/gallery/publisher/{publisher}/extension/{name}/{version}/assetbyname/{target}",
    "nlsBaseUrl": "https://www.vscode-unpkg.net/_pkg"
  }
}
```

## 使用方法

### 搜索扩展

1. 启动 Ponder IDE
2. 使用快捷键 `Ctrl+Shift+X` 打开扩展视图
3. 在搜索框中输入扩展名称或关键词
4. 浏览搜索结果并选择需要的扩展

### 安装扩展

1. 在扩展搜索结果中找到目标扩展
2. 点击扩展卡片上的"安装"按钮
3. 等待安装完成
4. 根据需要重新加载窗口以激活扩展

### 管理扩展

- **查看已安装扩展**：在扩展视图中切换到"已安装"标签
- **启用/禁用扩展**：点击扩展右侧的齿轮图标
- **卸载扩展**：在扩展详情中选择卸载选项
- **更新扩展**：系统会自动检查并提示更新

## 网络要求

扩展市场功能需要网络连接以访问以下服务：

- `marketplace.visualstudio.com` - 主要的扩展市场 API
- `az764295.vo.msecnd.net` - 扩展控制和元数据服务
- `www.vscode-unpkg.net` - 本地化资源服务

## 故障排除

### 无法搜索扩展

1. **检查网络连接**：确保能够访问上述服务 URL
2. **检查防火墙设置**：确保防火墙允许 Ponder IDE 访问网络
3. **检查代理设置**：如果使用代理，请确保正确配置

### 扩展安装失败

1. **检查磁盘空间**：确保有足够的磁盘空间
2. **检查权限**：确保 Ponder IDE 有写入扩展目录的权限
3. **重试安装**：某些网络问题可能导致临时失败

### 扩展无法加载

1. **重新加载窗口**：使用 `Ctrl+Shift+P` 打开命令面板，运行"重新加载窗口"
2. **检查扩展兼容性**：确保扩展与当前 Ponder IDE 版本兼容
3. **查看开发者控制台**：按 `F12` 查看是否有错误信息

## 自定义配置

如果需要使用自定义扩展市场，可以修改 `product.json` 中的相应 URL：

```json
{
  "extensionsGallery": {
    "serviceUrl": "https://your-custom-marketplace.com/api",
    "controlUrl": "https://your-custom-marketplace.com/control",
    "mcpUrl": "https://your-custom-marketplace.com/mcp",
    "extensionUrlTemplate": "https://your-custom-marketplace.com/extensions/{publisher}/{name}/{version}",
    "resourceUrlTemplate": "https://your-custom-marketplace.com/resources/{publisher}/{name}/{version}/{target}",
    "nlsBaseUrl": "https://your-custom-marketplace.com/nls"
  }
}
```

## 安全注意事项

- 只从可信的扩展市场安装扩展
- 定期检查和更新已安装的扩展
- 注意扩展请求的权限和访问范围
- 对于企业环境，考虑使用内部扩展市场

## 支持

如果遇到扩展市场相关问题，请：

1. 查看本文档的故障排除部分
2. 检查 Ponder IDE 的日志文件
3. 在项目仓库中提交 issue 并提供详细信息
