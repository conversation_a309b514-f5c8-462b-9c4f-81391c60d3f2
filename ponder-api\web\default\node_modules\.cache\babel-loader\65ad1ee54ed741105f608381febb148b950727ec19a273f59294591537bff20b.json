{"ast": null, "code": "import React,{useEffect,useState}from'react';import{useSearchParams,useNavigate}from'react-router-dom';import{Segment,Dimmer,Loader,Form,Button,Message,Header,Container,Grid,Card}from'semantic-ui-react';import{API}from'../helpers';import{showError,showSuccess}from'../helpers';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PonderOAuth=()=>{const[searchParams]=useSearchParams();const navigate=useNavigate();const[loading,setLoading]=useState(false);const[prompt,setPrompt]=useState('');const[showLoginForm,setShowLoginForm]=useState(true);const[formData,setFormData]=useState({username:'',password:''});const[currentState,setCurrentState]=useState('');// OAuth parameters from URL\nconst clientId=searchParams.get('client_id');const redirectUri=searchParams.get('redirect_uri');const originalState=searchParams.get('state');const scope=searchParams.get('scope');useEffect(()=>{// Validate OAuth parameters\nif(!clientId||!redirectUri||!originalState){setPrompt('无效的OAuth参数');setShowLoginForm(false);return;}if(clientId!=='ponder-client'){setPrompt('无效的客户端ID');setShowLoginForm(false);return;}// Get a fresh state from the server to ensure session consistency\nAPI.get('/api/oauth/ponder/state').then(response=>{if(response.data.success){setCurrentState(response.data.data);setPrompt('请登录以授权Ponder访问您的账户');}else{setPrompt('获取授权状态失败');setShowLoginForm(false);}}).catch(error=>{console.error('Failed to get OAuth state:',error);setPrompt('获取授权状态失败');setShowLoginForm(false);});},[clientId,redirectUri,originalState]);const handleInputChange=(e,_ref)=>{let{name,value}=_ref;setFormData(prev=>({...prev,[name]:value}));};const handleSubmit=async e=>{e.preventDefault();if(!formData.username||!formData.password){showError('请输入用户名和密码');return;}setLoading(true);setPrompt('正在验证...');try{const response=await API.post('/api/oauth/ponder/authorize',{username:formData.username,password:formData.password,client_id:clientId,redirect_uri:redirectUri,state:currentState,scope:scope||'read write'});if(response.data.success){const authCode=response.data.code;setPrompt('授权成功，正在跳转...');// Redirect to callback URL with authorization code\nconst callbackUrl=new URL(redirectUri);callbackUrl.searchParams.set('code',authCode);callbackUrl.searchParams.set('state',originalState);// Use original state for callback\nwindow.location.href=callbackUrl.toString();}else{showError(response.data.message||'授权失败');setLoading(false);setPrompt('请登录以授权Ponder访问您的账户');}}catch(error){console.error('Authorization error:',error);showError('授权过程中发生错误，请重试');setLoading(false);setPrompt('请登录以授权Ponder访问您的账户');}};const handleCancel=()=>{// Redirect back with error\nif(redirectUri){const callbackUrl=new URL(redirectUri);callbackUrl.searchParams.set('error','access_denied');callbackUrl.searchParams.set('state',originalState||'');window.location.href=callbackUrl.toString();}else{navigate('/login');}};if(!showLoginForm){return/*#__PURE__*/_jsx(Container,{style:{marginTop:'2em'},children:/*#__PURE__*/_jsx(Grid,{centered:true,children:/*#__PURE__*/_jsx(Grid.Column,{style:{maxWidth:450},children:/*#__PURE__*/_jsx(Card,{fluid:true,children:/*#__PURE__*/_jsxs(Card.Content,{children:[/*#__PURE__*/_jsx(Header,{as:\"h2\",textAlign:\"center\",color:\"red\",children:\"\\u6388\\u6743\\u5931\\u8D25\"}),/*#__PURE__*/_jsxs(Message,{error:true,children:[/*#__PURE__*/_jsx(Message.Header,{children:\"\\u9519\\u8BEF\"}),/*#__PURE__*/_jsx(\"p\",{children:prompt})]}),/*#__PURE__*/_jsx(Button,{fluid:true,color:\"blue\",onClick:()=>navigate('/login'),children:\"\\u8FD4\\u56DE\\u767B\\u5F55\\u9875\\u9762\"})]})})})})});}return/*#__PURE__*/_jsxs(Container,{style:{marginTop:'2em'},children:[/*#__PURE__*/_jsx(Grid,{centered:true,children:/*#__PURE__*/_jsx(Grid.Column,{style:{maxWidth:450},children:/*#__PURE__*/_jsx(Card,{fluid:true,children:/*#__PURE__*/_jsxs(Card.Content,{children:[/*#__PURE__*/_jsx(Header,{as:\"h2\",textAlign:\"center\",color:\"blue\",children:\"Ponder \\u6388\\u6743\"}),/*#__PURE__*/_jsxs(Message,{info:true,children:[/*#__PURE__*/_jsx(Message.Header,{children:\"\\u6388\\u6743\\u8BF7\\u6C42\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Ponder \\u5BA2\\u6237\\u7AEF\\u8BF7\\u6C42\\u8BBF\\u95EE\\u60A8\\u7684\\u8D26\\u6237\\u3002\\u8BF7\\u767B\\u5F55\\u4EE5\\u786E\\u8BA4\\u6388\\u6743\\u3002\"}),scope&&/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u8BF7\\u6C42\\u6743\\u9650:\"}),\" \",scope]})]}),/*#__PURE__*/_jsxs(Form,{onSubmit:handleSubmit,loading:loading,children:[/*#__PURE__*/_jsx(Form.Input,{fluid:true,icon:\"user\",iconPosition:\"left\",placeholder:\"\\u7528\\u6237\\u540D\",name:\"username\",value:formData.username,onChange:handleInputChange,required:true}),/*#__PURE__*/_jsx(Form.Input,{fluid:true,icon:\"lock\",iconPosition:\"left\",placeholder:\"\\u5BC6\\u7801\",type:\"password\",name:\"password\",value:formData.password,onChange:handleInputChange,required:true}),/*#__PURE__*/_jsxs(Grid,{columns:2,children:[/*#__PURE__*/_jsx(Grid.Column,{children:/*#__PURE__*/_jsx(Button,{type:\"button\",fluid:true,onClick:handleCancel,disabled:loading,children:\"\\u53D6\\u6D88\"})}),/*#__PURE__*/_jsx(Grid.Column,{children:/*#__PURE__*/_jsx(Button,{color:\"blue\",fluid:true,type:\"submit\",disabled:loading,children:\"\\u6388\\u6743\"})})]})]}),prompt&&/*#__PURE__*/_jsx(Message,{style:{marginTop:'1em'},children:prompt})]})})})}),loading&&/*#__PURE__*/_jsx(Dimmer,{active:true,children:/*#__PURE__*/_jsx(Loader,{size:\"large\",children:\"\\u5904\\u7406\\u4E2D...\"})})]});};export default PonderOAuth;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useSearchParams", "useNavigate", "Segment", "<PERSON><PERSON>", "Loader", "Form", "<PERSON><PERSON>", "Message", "Header", "Container", "Grid", "Card", "API", "showError", "showSuccess", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON><PERSON><PERSON>", "searchParams", "navigate", "loading", "setLoading", "prompt", "setPrompt", "showLoginForm", "setShowLoginForm", "formData", "setFormData", "username", "password", "currentState", "setCurrentState", "clientId", "get", "redirectUri", "originalState", "scope", "then", "response", "data", "success", "catch", "error", "console", "handleInputChange", "e", "_ref", "name", "value", "prev", "handleSubmit", "preventDefault", "post", "client_id", "redirect_uri", "state", "authCode", "code", "callbackUrl", "URL", "set", "window", "location", "href", "toString", "message", "handleCancel", "style", "marginTop", "children", "centered", "Column", "max<PERSON><PERSON><PERSON>", "fluid", "Content", "as", "textAlign", "color", "onClick", "info", "onSubmit", "Input", "icon", "iconPosition", "placeholder", "onChange", "required", "type", "columns", "disabled", "active", "size"], "sources": ["D:/Projects/ponder-all/ponder-api/web/default/src/components/PonderOAuth.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useSearchParams, useNavigate } from 'react-router-dom';\nimport { Segment, Dimmer, Loader, Form, Button, Message, Header, Container, Grid, Card } from 'semantic-ui-react';\nimport { API } from '../helpers';\nimport { showError, showSuccess } from '../helpers';\n\nconst PonderOAuth = () => {\n  const [searchParams] = useSearchParams();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [prompt, setPrompt] = useState('');\n  const [showLoginForm, setShowLoginForm] = useState(true);\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [currentState, setCurrentState] = useState('');\n\n  // OAuth parameters from URL\n  const clientId = searchParams.get('client_id');\n  const redirectUri = searchParams.get('redirect_uri');\n  const originalState = searchParams.get('state');\n  const scope = searchParams.get('scope');\n\n  useEffect(() => {\n    // Validate OAuth parameters\n    if (!clientId || !redirectUri || !originalState) {\n      setPrompt('无效的OAuth参数');\n      setShowLoginForm(false);\n      return;\n    }\n\n    if (clientId !== 'ponder-client') {\n      setPrompt('无效的客户端ID');\n      setShowLoginForm(false);\n      return;\n    }\n\n    // Get a fresh state from the server to ensure session consistency\n    API.get('/api/oauth/ponder/state')\n      .then(response => {\n        if (response.data.success) {\n          setCurrentState(response.data.data);\n          setPrompt('请登录以授权Ponder访问您的账户');\n        } else {\n          setPrompt('获取授权状态失败');\n          setShowLoginForm(false);\n        }\n      })\n      .catch(error => {\n        console.error('Failed to get OAuth state:', error);\n        setPrompt('获取授权状态失败');\n        setShowLoginForm(false);\n      });\n  }, [clientId, redirectUri, originalState]);\n\n  const handleInputChange = (e, { name, value }) => {\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!formData.username || !formData.password) {\n      showError('请输入用户名和密码');\n      return;\n    }\n\n    setLoading(true);\n    setPrompt('正在验证...');\n\n    try {\n      const response = await API.post('/api/oauth/ponder/authorize', {\n        username: formData.username,\n        password: formData.password,\n        client_id: clientId,\n        redirect_uri: redirectUri,\n        state: currentState,\n        scope: scope || 'read write'\n      });\n\n      if (response.data.success) {\n        const authCode = response.data.code;\n        setPrompt('授权成功，正在跳转...');\n        \n        // Redirect to callback URL with authorization code\n        const callbackUrl = new URL(redirectUri);\n        callbackUrl.searchParams.set('code', authCode);\n        callbackUrl.searchParams.set('state', originalState); // Use original state for callback\n        \n        window.location.href = callbackUrl.toString();\n      } else {\n        showError(response.data.message || '授权失败');\n        setLoading(false);\n        setPrompt('请登录以授权Ponder访问您的账户');\n      }\n    } catch (error) {\n      console.error('Authorization error:', error);\n      showError('授权过程中发生错误，请重试');\n      setLoading(false);\n      setPrompt('请登录以授权Ponder访问您的账户');\n    }\n  };\n\n  const handleCancel = () => {\n    // Redirect back with error\n    if (redirectUri) {\n      const callbackUrl = new URL(redirectUri);\n      callbackUrl.searchParams.set('error', 'access_denied');\n      callbackUrl.searchParams.set('state', originalState || '');\n      window.location.href = callbackUrl.toString();\n    } else {\n      navigate('/login');\n    }\n  };\n\n  if (!showLoginForm) {\n    return (\n      <Container style={{ marginTop: '2em' }}>\n        <Grid centered>\n          <Grid.Column style={{ maxWidth: 450 }}>\n            <Card fluid>\n              <Card.Content>\n                <Header as='h2' textAlign='center' color='red'>\n                  授权失败\n                </Header>\n                <Message error>\n                  <Message.Header>错误</Message.Header>\n                  <p>{prompt}</p>\n                </Message>\n                <Button \n                  fluid \n                  color='blue' \n                  onClick={() => navigate('/login')}\n                >\n                  返回登录页面\n                </Button>\n              </Card.Content>\n            </Card>\n          </Grid.Column>\n        </Grid>\n      </Container>\n    );\n  }\n\n  return (\n    <Container style={{ marginTop: '2em' }}>\n      <Grid centered>\n        <Grid.Column style={{ maxWidth: 450 }}>\n          <Card fluid>\n            <Card.Content>\n              <Header as='h2' textAlign='center' color='blue'>\n                Ponder 授权\n              </Header>\n              \n              <Message info>\n                <Message.Header>授权请求</Message.Header>\n                <p>Ponder 客户端请求访问您的账户。请登录以确认授权。</p>\n                {scope && (\n                  <p><strong>请求权限:</strong> {scope}</p>\n                )}\n              </Message>\n\n              <Form onSubmit={handleSubmit} loading={loading}>\n                <Form.Input\n                  fluid\n                  icon='user'\n                  iconPosition='left'\n                  placeholder='用户名'\n                  name='username'\n                  value={formData.username}\n                  onChange={handleInputChange}\n                  required\n                />\n                <Form.Input\n                  fluid\n                  icon='lock'\n                  iconPosition='left'\n                  placeholder='密码'\n                  type='password'\n                  name='password'\n                  value={formData.password}\n                  onChange={handleInputChange}\n                  required\n                />\n\n                <Grid columns={2}>\n                  <Grid.Column>\n                    <Button \n                      type='button'\n                      fluid\n                      onClick={handleCancel}\n                      disabled={loading}\n                    >\n                      取消\n                    </Button>\n                  </Grid.Column>\n                  <Grid.Column>\n                    <Button \n                      color='blue' \n                      fluid \n                      type='submit'\n                      disabled={loading}\n                    >\n                      授权\n                    </Button>\n                  </Grid.Column>\n                </Grid>\n              </Form>\n\n              {prompt && (\n                <Message style={{ marginTop: '1em' }}>\n                  {prompt}\n                </Message>\n              )}\n            </Card.Content>\n          </Card>\n        </Grid.Column>\n      </Grid>\n      \n      {loading && (\n        <Dimmer active>\n          <Loader size='large'>处理中...</Loader>\n        </Dimmer>\n      )}\n    </Container>\n  );\n};\n\nexport default PonderOAuth;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,eAAe,CAAEC,WAAW,KAAQ,kBAAkB,CAC/D,OAASC,OAAO,CAAEC,MAAM,CAAEC,MAAM,CAAEC,IAAI,CAAEC,MAAM,CAAEC,OAAO,CAAEC,MAAM,CAAEC,SAAS,CAAEC,IAAI,CAAEC,IAAI,KAAQ,mBAAmB,CACjH,OAASC,GAAG,KAAQ,YAAY,CAChC,OAASC,SAAS,CAAEC,WAAW,KAAQ,YAAY,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpD,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAACC,YAAY,CAAC,CAAGpB,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAqB,QAAQ,CAAGpB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACqB,OAAO,CAAEC,UAAU,CAAC,CAAGxB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACyB,MAAM,CAAEC,SAAS,CAAC,CAAG1B,QAAQ,CAAC,EAAE,CAAC,CACxC,KAAM,CAAC2B,aAAa,CAAEC,gBAAgB,CAAC,CAAG5B,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAAC6B,QAAQ,CAAEC,WAAW,CAAC,CAAG9B,QAAQ,CAAC,CACvC+B,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,EACZ,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CAEpD;AACA,KAAM,CAAAmC,QAAQ,CAAGd,YAAY,CAACe,GAAG,CAAC,WAAW,CAAC,CAC9C,KAAM,CAAAC,WAAW,CAAGhB,YAAY,CAACe,GAAG,CAAC,cAAc,CAAC,CACpD,KAAM,CAAAE,aAAa,CAAGjB,YAAY,CAACe,GAAG,CAAC,OAAO,CAAC,CAC/C,KAAM,CAAAG,KAAK,CAAGlB,YAAY,CAACe,GAAG,CAAC,OAAO,CAAC,CAEvCrC,SAAS,CAAC,IAAM,CACd;AACA,GAAI,CAACoC,QAAQ,EAAI,CAACE,WAAW,EAAI,CAACC,aAAa,CAAE,CAC/CZ,SAAS,CAAC,YAAY,CAAC,CACvBE,gBAAgB,CAAC,KAAK,CAAC,CACvB,OACF,CAEA,GAAIO,QAAQ,GAAK,eAAe,CAAE,CAChCT,SAAS,CAAC,UAAU,CAAC,CACrBE,gBAAgB,CAAC,KAAK,CAAC,CACvB,OACF,CAEA;AACAf,GAAG,CAACuB,GAAG,CAAC,yBAAyB,CAAC,CAC/BI,IAAI,CAACC,QAAQ,EAAI,CAChB,GAAIA,QAAQ,CAACC,IAAI,CAACC,OAAO,CAAE,CACzBT,eAAe,CAACO,QAAQ,CAACC,IAAI,CAACA,IAAI,CAAC,CACnChB,SAAS,CAAC,oBAAoB,CAAC,CACjC,CAAC,IAAM,CACLA,SAAS,CAAC,UAAU,CAAC,CACrBE,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAAC,CACDgB,KAAK,CAACC,KAAK,EAAI,CACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CAClDnB,SAAS,CAAC,UAAU,CAAC,CACrBE,gBAAgB,CAAC,KAAK,CAAC,CACzB,CAAC,CAAC,CACN,CAAC,CAAE,CAACO,QAAQ,CAAEE,WAAW,CAAEC,aAAa,CAAC,CAAC,CAE1C,KAAM,CAAAS,iBAAiB,CAAGA,CAACC,CAAC,CAAAC,IAAA,GAAsB,IAApB,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAAF,IAAA,CAC3CnB,WAAW,CAACsB,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP,CAACF,IAAI,EAAGC,KACV,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAE,YAAY,CAAG,KAAO,CAAAL,CAAC,EAAK,CAChCA,CAAC,CAACM,cAAc,CAAC,CAAC,CAElB,GAAI,CAACzB,QAAQ,CAACE,QAAQ,EAAI,CAACF,QAAQ,CAACG,QAAQ,CAAE,CAC5ClB,SAAS,CAAC,WAAW,CAAC,CACtB,OACF,CAEAU,UAAU,CAAC,IAAI,CAAC,CAChBE,SAAS,CAAC,SAAS,CAAC,CAEpB,GAAI,CACF,KAAM,CAAAe,QAAQ,CAAG,KAAM,CAAA5B,GAAG,CAAC0C,IAAI,CAAC,6BAA6B,CAAE,CAC7DxB,QAAQ,CAAEF,QAAQ,CAACE,QAAQ,CAC3BC,QAAQ,CAAEH,QAAQ,CAACG,QAAQ,CAC3BwB,SAAS,CAAErB,QAAQ,CACnBsB,YAAY,CAAEpB,WAAW,CACzBqB,KAAK,CAAEzB,YAAY,CACnBM,KAAK,CAAEA,KAAK,EAAI,YAClB,CAAC,CAAC,CAEF,GAAIE,QAAQ,CAACC,IAAI,CAACC,OAAO,CAAE,CACzB,KAAM,CAAAgB,QAAQ,CAAGlB,QAAQ,CAACC,IAAI,CAACkB,IAAI,CACnClC,SAAS,CAAC,cAAc,CAAC,CAEzB;AACA,KAAM,CAAAmC,WAAW,CAAG,GAAI,CAAAC,GAAG,CAACzB,WAAW,CAAC,CACxCwB,WAAW,CAACxC,YAAY,CAAC0C,GAAG,CAAC,MAAM,CAAEJ,QAAQ,CAAC,CAC9CE,WAAW,CAACxC,YAAY,CAAC0C,GAAG,CAAC,OAAO,CAAEzB,aAAa,CAAC,CAAE;AAEtD0B,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAGL,WAAW,CAACM,QAAQ,CAAC,CAAC,CAC/C,CAAC,IAAM,CACLrD,SAAS,CAAC2B,QAAQ,CAACC,IAAI,CAAC0B,OAAO,EAAI,MAAM,CAAC,CAC1C5C,UAAU,CAAC,KAAK,CAAC,CACjBE,SAAS,CAAC,oBAAoB,CAAC,CACjC,CACF,CAAE,MAAOmB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C/B,SAAS,CAAC,eAAe,CAAC,CAC1BU,UAAU,CAAC,KAAK,CAAC,CACjBE,SAAS,CAAC,oBAAoB,CAAC,CACjC,CACF,CAAC,CAED,KAAM,CAAA2C,YAAY,CAAGA,CAAA,GAAM,CACzB;AACA,GAAIhC,WAAW,CAAE,CACf,KAAM,CAAAwB,WAAW,CAAG,GAAI,CAAAC,GAAG,CAACzB,WAAW,CAAC,CACxCwB,WAAW,CAACxC,YAAY,CAAC0C,GAAG,CAAC,OAAO,CAAE,eAAe,CAAC,CACtDF,WAAW,CAACxC,YAAY,CAAC0C,GAAG,CAAC,OAAO,CAAEzB,aAAa,EAAI,EAAE,CAAC,CAC1D0B,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAGL,WAAW,CAACM,QAAQ,CAAC,CAAC,CAC/C,CAAC,IAAM,CACL7C,QAAQ,CAAC,QAAQ,CAAC,CACpB,CACF,CAAC,CAED,GAAI,CAACK,aAAa,CAAE,CAClB,mBACEV,IAAA,CAACP,SAAS,EAAC4D,KAAK,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAE,CAAAC,QAAA,cACrCvD,IAAA,CAACN,IAAI,EAAC8D,QAAQ,MAAAD,QAAA,cACZvD,IAAA,CAACN,IAAI,CAAC+D,MAAM,EAACJ,KAAK,CAAE,CAAEK,QAAQ,CAAE,GAAI,CAAE,CAAAH,QAAA,cACpCvD,IAAA,CAACL,IAAI,EAACgE,KAAK,MAAAJ,QAAA,cACTrD,KAAA,CAACP,IAAI,CAACiE,OAAO,EAAAL,QAAA,eACXvD,IAAA,CAACR,MAAM,EAACqE,EAAE,CAAC,IAAI,CAACC,SAAS,CAAC,QAAQ,CAACC,KAAK,CAAC,KAAK,CAAAR,QAAA,CAAC,0BAE/C,CAAQ,CAAC,cACTrD,KAAA,CAACX,OAAO,EAACqC,KAAK,MAAA2B,QAAA,eACZvD,IAAA,CAACT,OAAO,CAACC,MAAM,EAAA+D,QAAA,CAAC,cAAE,CAAgB,CAAC,cACnCvD,IAAA,MAAAuD,QAAA,CAAI/C,MAAM,CAAI,CAAC,EACR,CAAC,cACVR,IAAA,CAACV,MAAM,EACLqE,KAAK,MACLI,KAAK,CAAC,MAAM,CACZC,OAAO,CAAEA,CAAA,GAAM3D,QAAQ,CAAC,QAAQ,CAAE,CAAAkD,QAAA,CACnC,sCAED,CAAQ,CAAC,EACG,CAAC,CACX,CAAC,CACI,CAAC,CACV,CAAC,CACE,CAAC,CAEhB,CAEA,mBACErD,KAAA,CAACT,SAAS,EAAC4D,KAAK,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAE,CAAAC,QAAA,eACrCvD,IAAA,CAACN,IAAI,EAAC8D,QAAQ,MAAAD,QAAA,cACZvD,IAAA,CAACN,IAAI,CAAC+D,MAAM,EAACJ,KAAK,CAAE,CAAEK,QAAQ,CAAE,GAAI,CAAE,CAAAH,QAAA,cACpCvD,IAAA,CAACL,IAAI,EAACgE,KAAK,MAAAJ,QAAA,cACTrD,KAAA,CAACP,IAAI,CAACiE,OAAO,EAAAL,QAAA,eACXvD,IAAA,CAACR,MAAM,EAACqE,EAAE,CAAC,IAAI,CAACC,SAAS,CAAC,QAAQ,CAACC,KAAK,CAAC,MAAM,CAAAR,QAAA,CAAC,qBAEhD,CAAQ,CAAC,cAETrD,KAAA,CAACX,OAAO,EAAC0E,IAAI,MAAAV,QAAA,eACXvD,IAAA,CAACT,OAAO,CAACC,MAAM,EAAA+D,QAAA,CAAC,0BAAI,CAAgB,CAAC,cACrCvD,IAAA,MAAAuD,QAAA,CAAG,uIAA4B,CAAG,CAAC,CAClCjC,KAAK,eACJpB,KAAA,MAAAqD,QAAA,eAAGvD,IAAA,WAAAuD,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAACjC,KAAK,EAAI,CACrC,EACM,CAAC,cAEVpB,KAAA,CAACb,IAAI,EAAC6E,QAAQ,CAAE9B,YAAa,CAAC9B,OAAO,CAAEA,OAAQ,CAAAiD,QAAA,eAC7CvD,IAAA,CAACX,IAAI,CAAC8E,KAAK,EACTR,KAAK,MACLS,IAAI,CAAC,MAAM,CACXC,YAAY,CAAC,MAAM,CACnBC,WAAW,CAAC,oBAAK,CACjBrC,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEtB,QAAQ,CAACE,QAAS,CACzByD,QAAQ,CAAEzC,iBAAkB,CAC5B0C,QAAQ,MACT,CAAC,cACFxE,IAAA,CAACX,IAAI,CAAC8E,KAAK,EACTR,KAAK,MACLS,IAAI,CAAC,MAAM,CACXC,YAAY,CAAC,MAAM,CACnBC,WAAW,CAAC,cAAI,CAChBG,IAAI,CAAC,UAAU,CACfxC,IAAI,CAAC,UAAU,CACfC,KAAK,CAAEtB,QAAQ,CAACG,QAAS,CACzBwD,QAAQ,CAAEzC,iBAAkB,CAC5B0C,QAAQ,MACT,CAAC,cAEFtE,KAAA,CAACR,IAAI,EAACgF,OAAO,CAAE,CAAE,CAAAnB,QAAA,eACfvD,IAAA,CAACN,IAAI,CAAC+D,MAAM,EAAAF,QAAA,cACVvD,IAAA,CAACV,MAAM,EACLmF,IAAI,CAAC,QAAQ,CACbd,KAAK,MACLK,OAAO,CAAEZ,YAAa,CACtBuB,QAAQ,CAAErE,OAAQ,CAAAiD,QAAA,CACnB,cAED,CAAQ,CAAC,CACE,CAAC,cACdvD,IAAA,CAACN,IAAI,CAAC+D,MAAM,EAAAF,QAAA,cACVvD,IAAA,CAACV,MAAM,EACLyE,KAAK,CAAC,MAAM,CACZJ,KAAK,MACLc,IAAI,CAAC,QAAQ,CACbE,QAAQ,CAAErE,OAAQ,CAAAiD,QAAA,CACnB,cAED,CAAQ,CAAC,CACE,CAAC,EACV,CAAC,EACH,CAAC,CAEN/C,MAAM,eACLR,IAAA,CAACT,OAAO,EAAC8D,KAAK,CAAE,CAAEC,SAAS,CAAE,KAAM,CAAE,CAAAC,QAAA,CAClC/C,MAAM,CACA,CACV,EACW,CAAC,CACX,CAAC,CACI,CAAC,CACV,CAAC,CAENF,OAAO,eACNN,IAAA,CAACb,MAAM,EAACyF,MAAM,MAAArB,QAAA,cACZvD,IAAA,CAACZ,MAAM,EAACyF,IAAI,CAAC,OAAO,CAAAtB,QAAA,CAAC,uBAAM,CAAQ,CAAC,CAC9B,CACT,EACQ,CAAC,CAEhB,CAAC,CAED,cAAe,CAAApD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}