/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
// THIS IS A GENERATED FILE. DO NOT EDIT DIRECTLY.
const _allApiProposals = {
    activeComment: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.activeComment.d.ts',
    },
    aiRelatedInformation: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.aiRelatedInformation.d.ts',
    },
    aiSettingsSearch: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.aiSettingsSearch.d.ts',
    },
    aiTextSearchProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.aiTextSearchProvider.d.ts',
        version: 2
    },
    authIssuers: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.authIssuers.d.ts',
    },
    authLearnMore: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.authLearnMore.d.ts',
    },
    authProviderSpecific: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.authProviderSpecific.d.ts',
    },
    authSession: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.authSession.d.ts',
    },
    canonicalUriProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.canonicalUriProvider.d.ts',
    },
    chatEditing: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.chatEditing.d.ts',
    },
    chatParticipantAdditions: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.chatParticipantAdditions.d.ts',
    },
    chatParticipantPrivate: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.chatParticipantPrivate.d.ts',
        version: 9
    },
    chatProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.chatProvider.d.ts',
    },
    chatReferenceBinaryData: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.chatReferenceBinaryData.d.ts',
    },
    chatReferenceDiagnostic: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.chatReferenceDiagnostic.d.ts',
    },
    chatSessionsProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.chatSessionsProvider.d.ts',
    },
    chatStatusItem: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.chatStatusItem.d.ts',
    },
    chatTab: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.chatTab.d.ts',
    },
    codeActionAI: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.codeActionAI.d.ts',
    },
    codeActionRanges: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.codeActionRanges.d.ts',
    },
    codiconDecoration: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.codiconDecoration.d.ts',
    },
    commentReactor: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.commentReactor.d.ts',
    },
    commentReveal: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.commentReveal.d.ts',
    },
    commentThreadApplicability: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.commentThreadApplicability.d.ts',
    },
    commentingRangeHint: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.commentingRangeHint.d.ts',
    },
    commentsDraftState: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.commentsDraftState.d.ts',
    },
    contribAccessibilityHelpContent: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribAccessibilityHelpContent.d.ts',
    },
    contribCommentEditorActionsMenu: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribCommentEditorActionsMenu.d.ts',
    },
    contribCommentPeekContext: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribCommentPeekContext.d.ts',
    },
    contribCommentThreadAdditionalMenu: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribCommentThreadAdditionalMenu.d.ts',
    },
    contribCommentsViewThreadMenus: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribCommentsViewThreadMenus.d.ts',
    },
    contribDebugCreateConfiguration: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribDebugCreateConfiguration.d.ts',
    },
    contribDiffEditorGutterToolBarMenus: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribDiffEditorGutterToolBarMenus.d.ts',
    },
    contribEditSessions: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribEditSessions.d.ts',
    },
    contribEditorContentMenu: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribEditorContentMenu.d.ts',
    },
    contribLabelFormatterWorkspaceTooltip: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribLabelFormatterWorkspaceTooltip.d.ts',
    },
    contribLanguageModelToolSets: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribLanguageModelToolSets.d.ts',
    },
    contribMenuBarHome: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribMenuBarHome.d.ts',
    },
    contribMergeEditorMenus: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribMergeEditorMenus.d.ts',
    },
    contribMultiDiffEditorMenus: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribMultiDiffEditorMenus.d.ts',
    },
    contribNotebookStaticPreloads: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribNotebookStaticPreloads.d.ts',
    },
    contribRemoteHelp: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribRemoteHelp.d.ts',
    },
    contribShareMenu: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribShareMenu.d.ts',
    },
    contribSourceControlHistoryItemMenu: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribSourceControlHistoryItemMenu.d.ts',
    },
    contribSourceControlHistoryTitleMenu: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribSourceControlHistoryTitleMenu.d.ts',
    },
    contribSourceControlInputBoxMenu: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribSourceControlInputBoxMenu.d.ts',
    },
    contribSourceControlTitleMenu: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribSourceControlTitleMenu.d.ts',
    },
    contribStatusBarItems: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribStatusBarItems.d.ts',
    },
    contribViewContainerTitle: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribViewContainerTitle.d.ts',
    },
    contribViewsRemote: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribViewsRemote.d.ts',
    },
    contribViewsWelcome: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.contribViewsWelcome.d.ts',
    },
    customEditorMove: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.customEditorMove.d.ts',
    },
    dataChannels: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.dataChannels.d.ts',
    },
    debugVisualization: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.debugVisualization.d.ts',
    },
    defaultChatParticipant: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.defaultChatParticipant.d.ts',
        version: 4
    },
    diffCommand: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.diffCommand.d.ts',
    },
    diffContentOptions: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.diffContentOptions.d.ts',
    },
    documentFiltersExclusive: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.documentFiltersExclusive.d.ts',
    },
    editSessionIdentityProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.editSessionIdentityProvider.d.ts',
    },
    editorHoverVerbosityLevel: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.editorHoverVerbosityLevel.d.ts',
    },
    editorInsets: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.editorInsets.d.ts',
    },
    embeddings: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.embeddings.d.ts',
    },
    extensionRuntime: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.extensionRuntime.d.ts',
    },
    extensionsAny: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.extensionsAny.d.ts',
    },
    externalUriOpener: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.externalUriOpener.d.ts',
    },
    fileSearchProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.fileSearchProvider.d.ts',
    },
    fileSearchProvider2: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.fileSearchProvider2.d.ts',
    },
    findFiles2: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.findFiles2.d.ts',
        version: 2
    },
    findTextInFiles: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.findTextInFiles.d.ts',
    },
    findTextInFiles2: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.findTextInFiles2.d.ts',
    },
    fsChunks: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.fsChunks.d.ts',
    },
    idToken: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.idToken.d.ts',
    },
    inlineCompletionsAdditions: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.inlineCompletionsAdditions.d.ts',
    },
    interactive: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.interactive.d.ts',
    },
    interactiveWindow: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.interactiveWindow.d.ts',
    },
    ipc: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.ipc.d.ts',
    },
    languageModelCapabilities: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.languageModelCapabilities.d.ts',
    },
    languageModelDataPart: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.languageModelDataPart.d.ts',
        version: 3
    },
    languageModelSystem: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.languageModelSystem.d.ts',
    },
    languageStatusText: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.languageStatusText.d.ts',
    },
    mappedEditsProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.mappedEditsProvider.d.ts',
    },
    multiDocumentHighlightProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.multiDocumentHighlightProvider.d.ts',
    },
    nativeWindowHandle: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.nativeWindowHandle.d.ts',
    },
    newSymbolNamesProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.newSymbolNamesProvider.d.ts',
    },
    notebookCellExecution: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.notebookCellExecution.d.ts',
    },
    notebookControllerAffinityHidden: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.notebookControllerAffinityHidden.d.ts',
    },
    notebookDeprecated: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.notebookDeprecated.d.ts',
    },
    notebookExecution: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.notebookExecution.d.ts',
    },
    notebookKernelSource: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.notebookKernelSource.d.ts',
    },
    notebookLiveShare: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.notebookLiveShare.d.ts',
    },
    notebookMessaging: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.notebookMessaging.d.ts',
    },
    notebookMime: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.notebookMime.d.ts',
    },
    notebookReplDocument: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.notebookReplDocument.d.ts',
    },
    notebookVariableProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.notebookVariableProvider.d.ts',
    },
    portsAttributes: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.portsAttributes.d.ts',
    },
    profileContentHandlers: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.profileContentHandlers.d.ts',
    },
    quickDiffProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.quickDiffProvider.d.ts',
    },
    quickInputButtonLocation: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.quickInputButtonLocation.d.ts',
    },
    quickPickItemTooltip: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.quickPickItemTooltip.d.ts',
    },
    quickPickSortByLabel: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.quickPickSortByLabel.d.ts',
    },
    remoteCodingAgents: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.remoteCodingAgents.d.ts',
    },
    resolvers: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.resolvers.d.ts',
    },
    scmActionButton: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.scmActionButton.d.ts',
    },
    scmHistoryProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.scmHistoryProvider.d.ts',
    },
    scmMultiDiffEditor: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.scmMultiDiffEditor.d.ts',
    },
    scmProviderOptions: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.scmProviderOptions.d.ts',
    },
    scmSelectedProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.scmSelectedProvider.d.ts',
    },
    scmTextDocument: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.scmTextDocument.d.ts',
    },
    scmValidation: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.scmValidation.d.ts',
    },
    secretStorageKeys: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.secretStorageKeys.d.ts',
    },
    shareProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.shareProvider.d.ts',
    },
    speech: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.speech.d.ts',
    },
    statusBarItemTooltip: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.statusBarItemTooltip.d.ts',
    },
    tabInputMultiDiff: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.tabInputMultiDiff.d.ts',
    },
    tabInputTextMerge: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.tabInputTextMerge.d.ts',
    },
    taskExecutionTerminal: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.taskExecutionTerminal.d.ts',
    },
    taskPresentationGroup: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.taskPresentationGroup.d.ts',
    },
    taskProblemMatcherStatus: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.taskProblemMatcherStatus.d.ts',
    },
    telemetry: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.telemetry.d.ts',
    },
    terminalCompletionProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.terminalCompletionProvider.d.ts',
    },
    terminalDataWriteEvent: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.terminalDataWriteEvent.d.ts',
    },
    terminalDimensions: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.terminalDimensions.d.ts',
    },
    terminalExecuteCommandEvent: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.terminalExecuteCommandEvent.d.ts',
    },
    terminalQuickFixProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.terminalQuickFixProvider.d.ts',
    },
    terminalSelection: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.terminalSelection.d.ts',
    },
    terminalShellEnv: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.terminalShellEnv.d.ts',
    },
    testObserver: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.testObserver.d.ts',
    },
    testRelatedCode: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.testRelatedCode.d.ts',
    },
    textDocumentChangeReason: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.textDocumentChangeReason.d.ts',
    },
    textEditorDiffInformation: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.textEditorDiffInformation.d.ts',
    },
    textSearchComplete2: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.textSearchComplete2.d.ts',
    },
    textSearchProvider: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.textSearchProvider.d.ts',
    },
    textSearchProvider2: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.textSearchProvider2.d.ts',
    },
    timeline: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.timeline.d.ts',
    },
    tokenInformation: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.tokenInformation.d.ts',
    },
    toolProgress: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.toolProgress.d.ts',
    },
    treeViewActiveItem: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.treeViewActiveItem.d.ts',
    },
    treeViewMarkdownMessage: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.treeViewMarkdownMessage.d.ts',
    },
    treeViewReveal: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.treeViewReveal.d.ts',
    },
    tunnelFactory: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.tunnelFactory.d.ts',
    },
    tunnels: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.tunnels.d.ts',
    },
    valueSelectionInQuickPick: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.valueSelectionInQuickPick.d.ts',
    },
    workspaceTrust: {
        proposal: 'https://raw.githubusercontent.com/microsoft/vscode/main/src/vscode-dts/vscode.proposed.workspaceTrust.d.ts',
    }
};
export const allApiProposals = Object.freeze(_allApiProposals);
//# sourceMappingURL=data:application/json;base64,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