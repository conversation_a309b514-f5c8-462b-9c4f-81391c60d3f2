/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import * as nls from '../../../../nls.js';
export const SHOW_OR_FOCUS_HOVER_ACTION_ID = 'editor.action.showHover';
export const SHOW_DEFINITION_PREVIEW_HOVER_ACTION_ID = 'editor.action.showDefinitionPreviewHover';
export const HIDE_HOVER_ACTION_ID = 'editor.action.hideHover';
export const SCROLL_UP_HOVER_ACTION_ID = 'editor.action.scrollUpHover';
export const SCROLL_DOWN_HOVER_ACTION_ID = 'editor.action.scrollDownHover';
export const SCROLL_LEFT_HOVER_ACTION_ID = 'editor.action.scrollLeftHover';
export const SCROLL_RIGHT_HOVER_ACTION_ID = 'editor.action.scrollRightHover';
export const PAGE_UP_HOVER_ACTION_ID = 'editor.action.pageUpHover';
export const PAGE_DOWN_HOVER_ACTION_ID = 'editor.action.pageDownHover';
export const GO_TO_TOP_HOVER_ACTION_ID = 'editor.action.goToTopHover';
export const GO_TO_BOTTOM_HOVER_ACTION_ID = 'editor.action.goToBottomHover';
export const INCREASE_HOVER_VERBOSITY_ACTION_ID = 'editor.action.increaseHoverVerbosityLevel';
export const INCREASE_HOVER_VERBOSITY_ACCESSIBLE_ACTION_ID = 'editor.action.increaseHoverVerbosityLevelFromAccessibleView';
export const INCREASE_HOVER_VERBOSITY_ACTION_LABEL = nls.localize({ key: 'increaseHoverVerbosityLevel', comment: ['Label for action that will increase the hover verbosity level.'] }, "Increase Hover Verbosity Level");
export const DECREASE_HOVER_VERBOSITY_ACTION_ID = 'editor.action.decreaseHoverVerbosityLevel';
export const DECREASE_HOVER_VERBOSITY_ACCESSIBLE_ACTION_ID = 'editor.action.decreaseHoverVerbosityLevelFromAccessibleView';
export const DECREASE_HOVER_VERBOSITY_ACTION_LABEL = nls.localize({ key: 'decreaseHoverVerbosityLevel', comment: ['Label for action that will decrease the hover verbosity level.'] }, "Decrease Hover Verbosity Level");
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaG92ZXJBY3Rpb25JZHMuanMiLCJzb3VyY2VSb290IjoiZmlsZTovLy9EOi9Qcm9qZWN0cy9wb25kZXItYWxsL3BvbmRlci9zcmMvIiwic291cmNlcyI6WyJ2cy9lZGl0b3IvY29udHJpYi9ob3Zlci9icm93c2VyL2hvdmVyQWN0aW9uSWRzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Z0dBR2dHO0FBQ2hHLE9BQU8sS0FBSyxHQUFHLE1BQU0sb0JBQW9CLENBQUM7QUFFMUMsTUFBTSxDQUFDLE1BQU0sNkJBQTZCLEdBQUcseUJBQXlCLENBQUM7QUFDdkUsTUFBTSxDQUFDLE1BQU0sdUNBQXVDLEdBQUcsMENBQTBDLENBQUM7QUFDbEcsTUFBTSxDQUFDLE1BQU0sb0JBQW9CLEdBQUcseUJBQXlCLENBQUM7QUFDOUQsTUFBTSxDQUFDLE1BQU0seUJBQXlCLEdBQUcsNkJBQTZCLENBQUM7QUFDdkUsTUFBTSxDQUFDLE1BQU0sMkJBQTJCLEdBQUcsK0JBQStCLENBQUM7QUFDM0UsTUFBTSxDQUFDLE1BQU0sMkJBQTJCLEdBQUcsK0JBQStCLENBQUM7QUFDM0UsTUFBTSxDQUFDLE1BQU0sNEJBQTRCLEdBQUcsZ0NBQWdDLENBQUM7QUFDN0UsTUFBTSxDQUFDLE1BQU0sdUJBQXVCLEdBQUcsMkJBQTJCLENBQUM7QUFDbkUsTUFBTSxDQUFDLE1BQU0seUJBQXlCLEdBQUcsNkJBQTZCLENBQUM7QUFDdkUsTUFBTSxDQUFDLE1BQU0seUJBQXlCLEdBQUcsNEJBQTRCLENBQUM7QUFDdEUsTUFBTSxDQUFDLE1BQU0sNEJBQTRCLEdBQUcsK0JBQStCLENBQUM7QUFDNUUsTUFBTSxDQUFDLE1BQU0sa0NBQWtDLEdBQUcsMkNBQTJDLENBQUM7QUFDOUYsTUFBTSxDQUFDLE1BQU0sNkNBQTZDLEdBQUcsNkRBQTZELENBQUM7QUFDM0gsTUFBTSxDQUFDLE1BQU0scUNBQXFDLEdBQUcsR0FBRyxDQUFDLFFBQVEsQ0FBQyxFQUFFLEdBQUcsRUFBRSw2QkFBNkIsRUFBRSxPQUFPLEVBQUUsQ0FBQyxnRUFBZ0UsQ0FBQyxFQUFFLEVBQUUsZ0NBQWdDLENBQUMsQ0FBQztBQUN6TixNQUFNLENBQUMsTUFBTSxrQ0FBa0MsR0FBRywyQ0FBMkMsQ0FBQztBQUM5RixNQUFNLENBQUMsTUFBTSw2Q0FBNkMsR0FBRyw2REFBNkQsQ0FBQztBQUMzSCxNQUFNLENBQUMsTUFBTSxxQ0FBcUMsR0FBRyxHQUFHLENBQUMsUUFBUSxDQUFDLEVBQUUsR0FBRyxFQUFFLDZCQUE2QixFQUFFLE9BQU8sRUFBRSxDQUFDLGdFQUFnRSxDQUFDLEVBQUUsRUFBRSxnQ0FBZ0MsQ0FBQyxDQUFDIn0=