[{"D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\index.js": "1", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\i18n.js": "2", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\App.js": "3", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\Header.js": "4", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\Footer.js": "5", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\context\\User\\index.js": "6", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\context\\Status\\index.js": "7", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\LoginForm.js": "8", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\PrivateRoute.js": "9", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\RegisterForm.js": "10", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\GitHubOAuth.js": "11", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\Loading.js": "12", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\LarkOAuth.js": "13", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\PasswordResetConfirm.js": "14", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\PonderOAuth.js": "15", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\User\\EditUser.js": "16", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\PasswordResetForm.js": "17", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Redemption\\EditRedemption.js": "18", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\User\\AddUser.js": "19", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Token\\EditToken.js": "20", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Token\\index.js": "21", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Channel\\EditChannel.js": "22", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\User\\index.js": "23", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Redemption\\index.js": "24", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Channel\\index.js": "25", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\helpers\\index.js": "26", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\TopUp\\index.js": "27", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Setting\\index.js": "28", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Log\\index.js": "29", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\NotFound\\index.js": "30", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Chat\\index.js": "31", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\About\\index.js": "32", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Home\\index.js": "33", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\context\\Status\\reducer.js": "34", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Dashboard\\index.js": "35", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\context\\User\\reducer.js": "36", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\utils.js": "37", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\ChannelsTable.js": "38", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\helpers\\utils.js": "39", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\helpers\\render.js": "40", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\TokensTable.js": "41", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\helpers\\api.js": "42", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\OtherSetting.js": "43", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\UsersTable.js": "44", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\helpers\\history.js": "45", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\RedemptionsTable.js": "46", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\helpers\\auth-header.js": "47", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\SystemSetting.js": "48", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\PersonalSetting.js": "49", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\constants\\index.js": "50", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\LogsTable.js": "51", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\OperationSetting.js": "52", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\constants\\user.constants.js": "53", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\helpers\\helper.js": "54", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\constants\\toast.constants.js": "55", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\constants\\channel.constants.js": "56", "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\constants\\common.constant.js": "57"}, {"size": 1008, "mtime": 1753461287552, "results": "58", "hashOfConfig": "59"}, {"size": 677, "mtime": 1753461287552, "results": "60", "hashOfConfig": "59"}, {"size": 8713, "mtime": 1753492893269, "results": "61", "hashOfConfig": "59"}, {"size": 8685, "mtime": 1753461287548, "results": "62", "hashOfConfig": "59"}, {"size": 1756, "mtime": 1753461287547, "results": "63", "hashOfConfig": "59"}, {"size": 457, "mtime": 1753461287550, "results": "64", "hashOfConfig": "59"}, {"size": 468, "mtime": 1753461287550, "results": "65", "hashOfConfig": "59"}, {"size": 9554, "mtime": 1753461287549, "results": "66", "hashOfConfig": "59"}, {"size": 297, "mtime": 1753461287549, "results": "67", "hashOfConfig": "59"}, {"size": 8401, "mtime": 1753461287550, "results": "68", "hashOfConfig": "59"}, {"size": 1939, "mtime": 1753461287547, "results": "69", "hashOfConfig": "59"}, {"size": 354, "mtime": 1753461287548, "results": "70", "hashOfConfig": "59"}, {"size": 1931, "mtime": 1753461287548, "results": "71", "hashOfConfig": "59"}, {"size": 4720, "mtime": 1753461287549, "results": "72", "hashOfConfig": "59"}, {"size": 7190, "mtime": 1753498268977, "results": "73", "hashOfConfig": "59"}, {"size": 6609, "mtime": 1753461287555, "results": "74", "hashOfConfig": "59"}, {"size": 4896, "mtime": 1753461287549, "results": "75", "hashOfConfig": "59"}, {"size": 4482, "mtime": 1753461287554, "results": "76", "hashOfConfig": "59"}, {"size": 2630, "mtime": 1753461287555, "results": "77", "hashOfConfig": "59"}, {"size": 9290, "mtime": 1753461287555, "results": "78", "hashOfConfig": "59"}, {"size": 551, "mtime": 1753461287555, "results": "79", "hashOfConfig": "59"}, {"size": 24436, "mtime": 1753461287553, "results": "80", "hashOfConfig": "59"}, {"size": 545, "mtime": 1753461287555, "results": "81", "hashOfConfig": "59"}, {"size": 583, "mtime": 1753461287554, "results": "82", "hashOfConfig": "59"}, {"size": 563, "mtime": 1753461287553, "results": "83", "hashOfConfig": "59"}, {"size": 108, "mtime": 1753461287552, "results": "84", "hashOfConfig": "59"}, {"size": 8584, "mtime": 1753461287555, "results": "85", "hashOfConfig": "59"}, {"size": 1777, "mtime": 1753461287555, "results": "86", "hashOfConfig": "59"}, {"size": 541, "mtime": 1753461287554, "results": "87", "hashOfConfig": "59"}, {"size": 300, "mtime": 1753461287554, "results": "88", "hashOfConfig": "59"}, {"size": 265, "mtime": 1753461287553, "results": "89", "hashOfConfig": "59"}, {"size": 2236, "mtime": 1753461287553, "results": "90", "hashOfConfig": "59"}, {"size": 12228, "mtime": 1753461287554, "results": "91", "hashOfConfig": "59"}, {"size": 361, "mtime": 1753461287550, "results": "92", "hashOfConfig": "59"}, {"size": 15228, "mtime": 1753461287554, "results": "93", "hashOfConfig": "59"}, {"size": 355, "mtime": 1753461287550, "results": "94", "hashOfConfig": "59"}, {"size": 883, "mtime": 1753461287550, "results": "95", "hashOfConfig": "59"}, {"size": 23473, "mtime": 1753461287547, "results": "96", "hashOfConfig": "59"}, {"size": 5715, "mtime": 1753461287552, "results": "97", "hashOfConfig": "59"}, {"size": 2872, "mtime": 1753461287552, "results": "98", "hashOfConfig": "59"}, {"size": 17346, "mtime": 1753461287550, "results": "99", "hashOfConfig": "59"}, {"size": 295, "mtime": 1753461287551, "results": "100", "hashOfConfig": "59"}, {"size": 8211, "mtime": 1753461287549, "results": "101", "hashOfConfig": "59"}, {"size": 13503, "mtime": 1753461287550, "results": "102", "hashOfConfig": "59"}, {"size": 97, "mtime": 1753461287551, "results": "103", "hashOfConfig": "59"}, {"size": 11746, "mtime": 1753461287549, "results": "104", "hashOfConfig": "59"}, {"size": 277, "mtime": 1753461287551, "results": "105", "hashOfConfig": "59"}, {"size": 23447, "mtime": 1753461287550, "results": "106", "hashOfConfig": "59"}, {"size": 13722, "mtime": 1753461287549, "results": "107", "hashOfConfig": "59"}, {"size": 143, "mtime": 1753461287550, "results": "108", "hashOfConfig": "59"}, {"size": 18221, "mtime": 1753461287549, "results": "109", "hashOfConfig": "59"}, {"size": 15809, "mtime": 1753461287549, "results": "110", "hashOfConfig": "59"}, {"size": 626, "mtime": 1753461287550, "results": "111", "hashOfConfig": "59"}, {"size": 341, "mtime": 1753461287551, "results": "112", "hashOfConfig": "59"}, {"size": 164, "mtime": 1753461287550, "results": "113", "hashOfConfig": "59"}, {"size": 4949, "mtime": 1753461287550, "results": "114", "hashOfConfig": "59"}, {"size": 95, "mtime": 1753461287550, "results": "115", "hashOfConfig": "59"}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "a3q9aq", {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 2, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 4, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\index.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\i18n.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\App.js", ["287", "288", "289"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\Header.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\Footer.js", ["290", "291", "292"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\context\\User\\index.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\context\\Status\\index.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\LoginForm.js", ["293", "294", "295", "296", "297"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\PrivateRoute.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\RegisterForm.js", ["298"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\GitHubOAuth.js", ["299", "300", "301", "302", "303"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\Loading.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\LarkOAuth.js", ["304", "305", "306", "307", "308"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\PasswordResetConfirm.js", ["309", "310"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\PonderOAuth.js", ["311", "312"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\User\\EditUser.js", ["313", "314", "315"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\PasswordResetForm.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Redemption\\EditRedemption.js", ["316", "317"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\User\\AddUser.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Token\\EditToken.js", ["318", "319", "320"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Token\\index.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Channel\\EditChannel.js", ["321", "322", "323"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\User\\index.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Redemption\\index.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Channel\\index.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\helpers\\index.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\TopUp\\index.js", ["324"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Setting\\index.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Log\\index.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\NotFound\\index.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Chat\\index.js", ["325"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\About\\index.js", ["326", "327"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Home\\index.js", ["328", "329", "330", "331", "332"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\context\\Status\\reducer.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\pages\\Dashboard\\index.js", ["333", "334"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\context\\User\\reducer.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\utils.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\ChannelsTable.js", ["335", "336", "337", "338"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\helpers\\utils.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\helpers\\render.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\TokensTable.js", ["339", "340", "341", "342", "343", "344"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\helpers\\api.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\OtherSetting.js", ["345", "346", "347"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\UsersTable.js", ["348"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\helpers\\history.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\RedemptionsTable.js", ["349", "350", "351"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\helpers\\auth-header.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\SystemSetting.js", ["352", "353", "354", "355", "356", "357"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\PersonalSetting.js", ["358"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\constants\\index.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\LogsTable.js", ["359", "360", "361", "362", "363", "364", "365", "366"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\components\\OperationSetting.js", ["367"], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\constants\\user.constants.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\helpers\\helper.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\constants\\toast.constants.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\constants\\channel.constants.js", [], [], "D:\\Projects\\ponder-all\\ponder-api\\web\\default\\src\\constants\\common.constant.js", [], [], {"ruleId": "368", "severity": 1, "message": "369", "line": 35, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 35, "endColumn": 19}, {"ruleId": "368", "severity": 1, "message": "372", "line": 36, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 36, "endColumn": 21}, {"ruleId": "373", "severity": 1, "message": "374", "line": 94, "column": 6, "nodeType": "375", "endLine": 94, "endColumn": 8, "suggestions": "376"}, {"ruleId": "373", "severity": 1, "message": "377", "line": 29, "column": 6, "nodeType": "375", "endLine": 29, "endColumn": 8, "suggestions": "378"}, {"ruleId": "379", "severity": 1, "message": "380", "line": 41, "column": 13, "nodeType": "381", "messageId": "382", "endLine": 41, "endColumn": 79, "fix": "383"}, {"ruleId": "379", "severity": 1, "message": "380", "line": 45, "column": 13, "nodeType": "381", "messageId": "382", "endLine": 45, "endColumn": 71, "fix": "384"}, {"ruleId": "368", "severity": 1, "message": "385", "line": 11, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 11, "endColumn": 10}, {"ruleId": "368", "severity": 1, "message": "386", "line": 28, "column": 24, "nodeType": "370", "messageId": "371", "endLine": 28, "endColumn": 39}, {"ruleId": "368", "severity": 1, "message": "387", "line": 29, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 29, "endColumn": 19}, {"ruleId": "368", "severity": 1, "message": "369", "line": 31, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 31, "endColumn": 19}, {"ruleId": "373", "severity": 1, "message": "388", "line": 45, "column": 6, "nodeType": "375", "endLine": 45, "endColumn": 8, "suggestions": "389"}, {"ruleId": "373", "severity": 1, "message": "390", "line": 40, "column": 3, "nodeType": "370", "endLine": 40, "endColumn": 12, "suggestions": "391"}, {"ruleId": "368", "severity": 1, "message": "386", "line": 8, "column": 24, "nodeType": "370", "messageId": "371", "endLine": 8, "endColumn": 39}, {"ruleId": "368", "severity": 1, "message": "369", "line": 10, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 10, "endColumn": 19}, {"ruleId": "368", "severity": 1, "message": "392", "line": 12, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 12, "endColumn": 20}, {"ruleId": "368", "severity": 1, "message": "393", "line": 12, "column": 22, "nodeType": "370", "messageId": "371", "endLine": 12, "endColumn": 35}, {"ruleId": "373", "severity": 1, "message": "394", "line": 47, "column": 6, "nodeType": "375", "endLine": 47, "endColumn": 8, "suggestions": "395"}, {"ruleId": "368", "severity": 1, "message": "386", "line": 8, "column": 24, "nodeType": "370", "messageId": "371", "endLine": 8, "endColumn": 39}, {"ruleId": "368", "severity": 1, "message": "369", "line": 10, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 10, "endColumn": 19}, {"ruleId": "368", "severity": 1, "message": "392", "line": 12, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 12, "endColumn": 20}, {"ruleId": "368", "severity": 1, "message": "393", "line": 12, "column": 22, "nodeType": "370", "messageId": "371", "endLine": 12, "endColumn": 35}, {"ruleId": "373", "severity": 1, "message": "394", "line": 47, "column": 6, "nodeType": "375", "endLine": 47, "endColumn": 8, "suggestions": "396"}, {"ruleId": "368", "severity": 1, "message": "386", "line": 29, "column": 24, "nodeType": "370", "messageId": "371", "endLine": 29, "endColumn": 39}, {"ruleId": "373", "severity": 1, "message": "397", "line": 37, "column": 6, "nodeType": "375", "endLine": 37, "endColumn": 8, "suggestions": "398"}, {"ruleId": "368", "severity": 1, "message": "385", "line": 3, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 3, "endColumn": 17}, {"ruleId": "368", "severity": 1, "message": "399", "line": 5, "column": 21, "nodeType": "370", "messageId": "371", "endLine": 5, "endColumn": 32}, {"ruleId": "368", "severity": 1, "message": "400", "line": 6, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 6, "endColumn": 21}, {"ruleId": "368", "severity": 1, "message": "401", "line": 32, "column": 5, "nodeType": "370", "messageId": "371", "endLine": 32, "endColumn": 10}, {"ruleId": "373", "severity": 1, "message": "402", "line": 76, "column": 6, "nodeType": "375", "endLine": 76, "endColumn": 8, "suggestions": "403"}, {"ruleId": "368", "severity": 1, "message": "400", "line": 6, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 6, "endColumn": 21}, {"ruleId": "373", "severity": 1, "message": "404", "line": 45, "column": 6, "nodeType": "375", "endLine": 45, "endColumn": 8, "suggestions": "405"}, {"ruleId": "368", "severity": 1, "message": "406", "line": 6, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 6, "endColumn": 9}, {"ruleId": "368", "severity": 1, "message": "385", "line": 8, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 8, "endColumn": 10}, {"ruleId": "373", "severity": 1, "message": "407", "line": 118, "column": 6, "nodeType": "375", "endLine": 118, "endColumn": 8, "suggestions": "408"}, {"ruleId": "373", "severity": 1, "message": "409", "line": 168, "column": 6, "nodeType": "375", "endLine": 168, "endColumn": 8, "suggestions": "410"}, {"ruleId": "379", "severity": 1, "message": "380", "line": 307, "column": 19, "nodeType": "381", "messageId": "382", "endLine": 310, "endColumn": 20, "fix": "411"}, {"ruleId": "379", "severity": 1, "message": "380", "line": 408, "column": 17, "nodeType": "381", "messageId": "382", "endLine": 411, "endColumn": 18, "fix": "412"}, {"ruleId": "368", "severity": 1, "message": "413", "line": 9, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 9, "endColumn": 10}, {"ruleId": "414", "severity": 1, "message": "415", "line": 7, "column": 5, "nodeType": "381", "endLine": 10, "endColumn": 7}, {"ruleId": "373", "severity": 1, "message": "416", "line": 32, "column": 6, "nodeType": "375", "endLine": 32, "endColumn": 8, "suggestions": "417"}, {"ruleId": "414", "severity": 1, "message": "415", "line": 52, "column": 13, "nodeType": "381", "endLine": 55, "endColumn": 15}, {"ruleId": "368", "severity": 1, "message": "418", "line": 8, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 8, "endColumn": 14}, {"ruleId": "368", "severity": 1, "message": "419", "line": 12, "column": 23, "nodeType": "370", "messageId": "371", "endLine": 12, "endColumn": 37}, {"ruleId": "373", "severity": 1, "message": "420", "line": 58, "column": 6, "nodeType": "375", "endLine": 58, "endColumn": 8, "suggestions": "421"}, {"ruleId": "379", "severity": 1, "message": "380", "line": 135, "column": 27, "nodeType": "381", "messageId": "382", "endLine": 139, "endColumn": 28, "fix": "422"}, {"ruleId": "414", "severity": 1, "message": "415", "line": 283, "column": 13, "nodeType": "381", "endLine": 286, "endColumn": 15}, {"ruleId": "368", "severity": 1, "message": "423", "line": 59, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 59, "endColumn": 21}, {"ruleId": "373", "severity": 1, "message": "424", "line": 67, "column": 6, "nodeType": "375", "endLine": 67, "endColumn": 8, "suggestions": "425"}, {"ruleId": "368", "severity": 1, "message": "426", "line": 87, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 87, "endColumn": 25}, {"ruleId": "373", "severity": 1, "message": "427", "line": 161, "column": 6, "nodeType": "375", "endLine": 161, "endColumn": 8, "suggestions": "428"}, {"ruleId": "429", "severity": 1, "message": "430", "line": 166, "column": 5, "nodeType": "431", "messageId": "432", "endLine": 195, "endColumn": 6}, {"ruleId": "368", "severity": 1, "message": "433", "line": 377, "column": 9, "nodeType": "370", "messageId": "371", "endLine": 377, "endColumn": 33}, {"ruleId": "368", "severity": 1, "message": "434", "line": 87, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 87, "endColumn": 24}, {"ruleId": "368", "severity": 1, "message": "435", "line": 87, "column": 26, "nodeType": "370", "messageId": "371", "endLine": 87, "endColumn": 43}, {"ruleId": "368", "severity": 1, "message": "436", "line": 88, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 88, "endColumn": 24}, {"ruleId": "368", "severity": 1, "message": "437", "line": 88, "column": 26, "nodeType": "370", "messageId": "371", "endLine": 88, "endColumn": 43}, {"ruleId": "373", "severity": 1, "message": "438", "line": 220, "column": 6, "nodeType": "375", "endLine": 220, "endColumn": 15, "suggestions": "439"}, {"ruleId": "429", "severity": 1, "message": "430", "line": 225, "column": 5, "nodeType": "431", "messageId": "432", "endLine": 237, "endColumn": 6}, {"ruleId": "368", "severity": 1, "message": "440", "line": 13, "column": 39, "nodeType": "370", "messageId": "371", "endLine": 13, "endColumn": 49}, {"ruleId": "373", "severity": 1, "message": "441", "line": 52, "column": 6, "nodeType": "375", "endLine": 52, "endColumn": 8, "suggestions": "442"}, {"ruleId": "368", "severity": 1, "message": "443", "line": 101, "column": 9, "nodeType": "370", "messageId": "371", "endLine": 101, "endColumn": 20}, {"ruleId": "373", "severity": 1, "message": "444", "line": 80, "column": 6, "nodeType": "375", "endLine": 80, "endColumn": 15, "suggestions": "445"}, {"ruleId": "368", "severity": 1, "message": "446", "line": 16, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 16, "endColumn": 11}, {"ruleId": "373", "severity": 1, "message": "447", "line": 99, "column": 6, "nodeType": "375", "endLine": 99, "endColumn": 8, "suggestions": "448"}, {"ruleId": "429", "severity": 1, "message": "430", "line": 104, "column": 5, "nodeType": "431", "messageId": "432", "endLine": 116, "endColumn": 6}, {"ruleId": "368", "severity": 1, "message": "449", "line": 209, "column": 9, "nodeType": "370", "messageId": "371", "endLine": 209, "endColumn": 28}, {"ruleId": "368", "severity": 1, "message": "450", "line": 260, "column": 9, "nodeType": "370", "messageId": "371", "endLine": 260, "endColumn": 34}, {"ruleId": "379", "severity": 1, "message": "380", "line": 506, "column": 13, "nodeType": "381", "messageId": "382", "endLine": 506, "endColumn": 78, "fix": "451"}, {"ruleId": "379", "severity": 1, "message": "380", "line": 543, "column": 15, "nodeType": "381", "messageId": "382", "endLine": 543, "endColumn": 68, "fix": "452"}, {"ruleId": "379", "severity": 1, "message": "380", "line": 583, "column": 15, "nodeType": "381", "messageId": "382", "endLine": 586, "endColumn": 16, "fix": "453"}, {"ruleId": "379", "severity": 1, "message": "380", "line": 629, "column": 15, "nodeType": "381", "messageId": "382", "endLine": 629, "endColumn": 70, "fix": "454"}, {"ruleId": "368", "severity": 1, "message": "455", "line": 18, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 18, "endColumn": 13}, {"ruleId": "368", "severity": 1, "message": "385", "line": 8, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 8, "endColumn": 10}, {"ruleId": "368", "severity": 1, "message": "456", "line": 11, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 11, "endColumn": 8}, {"ruleId": "368", "severity": 1, "message": "457", "line": 45, "column": 7, "nodeType": "370", "messageId": "371", "endLine": 45, "endColumn": 19}, {"ruleId": "368", "severity": 1, "message": "458", "line": 139, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 139, "endColumn": 19}, {"ruleId": "373", "severity": 1, "message": "459", "line": 264, "column": 6, "nodeType": "375", "endLine": 264, "endColumn": 15, "suggestions": "460"}, {"ruleId": "368", "severity": 1, "message": "461", "line": 266, "column": 9, "nodeType": "370", "messageId": "371", "endLine": 266, "endColumn": 19}, {"ruleId": "368", "severity": 1, "message": "462", "line": 285, "column": 9, "nodeType": "370", "messageId": "371", "endLine": 285, "endColumn": 28}, {"ruleId": "463", "severity": 1, "message": "464", "line": 298, "column": 30, "nodeType": "465", "messageId": "466", "endLine": 298, "endColumn": 32}, {"ruleId": "429", "severity": 1, "message": "430", "line": 98, "column": 5, "nodeType": "431", "messageId": "432", "endLine": 169, "endColumn": 6}, "no-unused-vars", "'userState' is assigned a value but never used.", "Identifier", "unusedVar", "'statusState' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadStatus' and 'loadUser'. Either include them or remove the dependency array.", "ArrayExpression", ["467"], "React Hook useEffect has a missing dependency: 'remainCheckTimes'. Either include it or remove the dependency array.", ["468"], "react/jsx-no-target-blank", "Using target=\"_blank\" without rel=\"noreferrer\" (which implies rel=\"noopener\") is a security risk in older browsers: see https://mathiasbynens.github.io/rel-noopener/#recommendations", "JSXOpeningElement", "noTargetBlankWithoutNoreferrer", {"range": "469", "text": "470"}, {"range": "471", "text": "470"}, "'Segment' is defined but never used.", "'setSearchParams' is assigned a value but never used.", "'submitted' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'searchParams' and 't'. Either include them or remove the dependency array.", ["472"], "React Hook useEffect contains a call to 'setShowEmailVerification'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [] as a second argument to the useEffect Hook.", ["473"], "'processing' is assigned a value but never used.", "'setProcessing' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'searchParams' and 'sendCode'. Either include them or remove the dependency array.", ["474"], ["475"], "React Hook useEffect has a missing dependency: 'searchParams'. Either include it or remove the dependency array.", ["476"], "'showSuccess' is defined but never used.", "'renderQuota' is defined but never used.", "'group' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'loadUser' and 'userId'. Either include them or remove the dependency array.", ["477"], "React Hook useEffect has missing dependencies: 'isEdit' and 'loadRedemption'. Either include them or remove the dependency array.", ["478"], "'Header' is defined but never used.", "React Hook useEffect has missing dependencies: 'isEdit' and 'loadToken'. Either include them or remove the dependency array.", ["479"], "React Hook useEffect has missing dependencies: 'inputs.type', 'isEdit', and 'loadChannel'. Either include them or remove the dependency array.", ["480"], {"range": "481", "text": "470"}, {"range": "482", "text": "470"}, "'Divider' is defined but never used.", "jsx-a11y/iframe-has-title", "<iframe> elements must have a unique title property.", "React Hook useEffect has a missing dependency: 'displayAbout'. Either include it or remove the dependency array.", ["483"], "'Link' is defined but never used.", "'statusDispatch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'displayHomePageContent'. Either include it or remove the dependency array.", ["484"], {"range": "485", "text": "470"}, "'summaryData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", ["486"], "'updatingBalance' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadChannels'. Either include it or remove the dependency array.", ["487"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'updateAllChannelsBalance' is assigned a value but never used.", "'showTopUpModal' is assigned a value but never used.", "'setShowTopUpModal' is assigned a value but never used.", "'targetTokenIdx' is assigned a value but never used.", "'setTargetTokenIdx' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadTokens'. Either include it or remove the dependency array.", ["488"], "'verifyJSON' is defined but never used.", "React Hook useEffect has a missing dependency: 'getOptions'. Either include it or remove the dependency array.", ["489"], "'checkUpdate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["490"], "'showInfo' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadRedemptions'. Either include it or remove the dependency array.", ["491"], "'submitMessagePusher' is assigned a value but never used.", "'submitNewRestrictedDomain' is assigned a value but never used.", {"range": "492", "text": "470"}, {"range": "493", "text": "470"}, {"range": "494", "text": "470"}, {"range": "495", "text": "470"}, "'showNotice' is defined but never used.", "'Popup' is defined but never used.", "'MODE_OPTIONS' is assigned a value but never used.", "'searching' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["496"], "'searchLogs' is assigned a value but never used.", "'handleKeywordChange' is assigned a value but never used.", "array-callback-return", "Array.prototype.sort() expects a value to be returned at the end of arrow function.", "ArrowFunctionExpression", "expectedAtEnd", {"desc": "497", "fix": "498"}, {"desc": "499", "fix": "500"}, [1226, 1226], " rel=\"noreferrer\"", [1422, 1422], {"desc": "501", "fix": "502"}, {"desc": "503", "fix": "504"}, {"desc": "505", "fix": "506"}, {"desc": "505", "fix": "507"}, {"desc": "508", "fix": "509"}, {"desc": "510", "fix": "511"}, {"desc": "512", "fix": "513"}, {"desc": "514", "fix": "515"}, {"desc": "516", "fix": "517"}, [9812, 9812], [13643, 13643], {"desc": "518", "fix": "519"}, {"desc": "520", "fix": "521"}, [5501, 5501], {"desc": "522", "fix": "523"}, {"desc": "524", "fix": "525"}, {"desc": "526", "fix": "527"}, {"desc": "528", "fix": "529"}, {"desc": "530", "fix": "531"}, {"desc": "532", "fix": "533"}, [17330, 17330], [18826, 18826], [20463, 20463], [22209, 22209], {"desc": "534", "fix": "535"}, "Update the dependencies array to be: [loadStatus, loadUser]", {"range": "536", "text": "537"}, "Update the dependencies array to be: [remainCheckTimes]", {"range": "538", "text": "539"}, "Update the dependencies array to be: [searchParams, t]", {"range": "540", "text": "541"}, "Add dependencies array: []", {"range": "542", "text": "543"}, "Update the dependencies array to be: [searchParams, sendCode]", {"range": "544", "text": "545"}, {"range": "546", "text": "545"}, "Update the dependencies array to be: [searchParams]", {"range": "547", "text": "548"}, "Update the dependencies array to be: [loadUser, userId]", {"range": "549", "text": "550"}, "Update the dependencies array to be: [isEdit, loadRedemption]", {"range": "551", "text": "552"}, "Update the dependencies array to be: [isEdit, loadToken]", {"range": "553", "text": "554"}, "Update the dependencies array to be: [inputs.type, isEdit, loadChannel]", {"range": "555", "text": "556"}, "Update the dependencies array to be: [displayAbout]", {"range": "557", "text": "558"}, "Update the dependencies array to be: [displayHomePageContent]", {"range": "559", "text": "560"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "561", "text": "562"}, "Update the dependencies array to be: [loadChannels]", {"range": "563", "text": "564"}, "Update the dependencies array to be: [loadTokens, orderBy]", {"range": "565", "text": "566"}, "Update the dependencies array to be: [getOptions]", {"range": "567", "text": "568"}, "Update the dependencies array to be: [loadUsers, orderBy]", {"range": "569", "text": "570"}, "Update the dependencies array to be: [loadRedemptions]", {"range": "571", "text": "572"}, "Update the dependencies array to be: [logType, refresh]", {"range": "573", "text": "574"}, [3566, 3568], "[loadStatus, loadUser]", [827, 829], "[remainCheckTimes]", [1349, 1351], "[searchP<PERSON><PERSON>, t]", [1608, 1608], ", []", [1645, 1647], "[searchPara<PERSON>, sendCode]", [1639, 1641], [1009, 1011], "[searchParams]", [1979, 1981], "[loadUser, userId]", [1379, 1381], "[isEdit, loadRedemption]", [3378, 3380], "[isEdit, loadToken]", [4870, 4872], "[inputs.type, isEdit, loadChannel]", [989, 991], "[displayAbout]", [2047, 2049], "[displayHomePageContent]", [1315, 1317], "[fetchDashboardData]", [4756, 4758], "[loadChannels]", [6382, 6391], "[loadTokens, orderBy]", [1266, 1268], "[getOptions]", [2204, 2213], "[loadUsers, orderBy]", [2466, 2468], "[loadRedemptions]", [7010, 7019], "[logType, refresh]"]