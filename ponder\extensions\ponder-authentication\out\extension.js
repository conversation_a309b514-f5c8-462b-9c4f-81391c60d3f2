"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const ponderAuthProvider_1 = require("./ponderAuthProvider");
function activate(context) {
    const uriHandler = new ponderAuthProvider_1.UriEventHandler();
    context.subscriptions.push(uriHandler);
    context.subscriptions.push(vscode.window.registerUriHandler(uriHandler));
    const ponderAuthProvider = new ponderAuthProvider_1.PonderAuthenticationProvider(context, uriHandler);
    context.subscriptions.push(ponderAuthProvider);
    context.subscriptions.push(vscode.authentication.registerAuthenticationProvider('ponder', 'Ponder', ponderAuthProvider, { supportsMultipleAccounts: false }));
    // 注册测试命令
    const testCommand = vscode.commands.registerCommand('ponder.testAuthentication', async () => {
        try {
            vscode.window.showInformationMessage('Starting Ponder authentication...');
            const session = await vscode.authentication.getSession('ponder', ['read', 'write'], { createIfNone: true });
            vscode.window.showInformationMessage(`Authentication successful! User: ${session.account.label}`);
        }
        catch (error) {
            vscode.window.showErrorMessage(`Authentication failed: ${error}`);
        }
    });
    context.subscriptions.push(testCommand);
}
function deactivate() {
    // Nothing to do
}
//# sourceMappingURL=extension.js.map