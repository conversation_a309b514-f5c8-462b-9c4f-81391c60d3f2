/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import assert from 'assert';
import { SmoothScrollingOperation } from '../../common/scrollable.js';
import { ensureNoDisposablesAreLeakedInTestSuite } from './utils.js';
class TestSmoothScrollingOperation extends SmoothScrollingOperation {
    constructor(from, to, viewportSize, startTime, duration) {
        duration = duration + 10;
        startTime = startTime - 10;
        super({ scrollLeft: 0, scrollTop: from, width: 0, height: viewportSize }, { scrollLeft: 0, scrollTop: to, width: 0, height: viewportSize }, startTime, duration);
    }
    testTick(now) {
        return this._tick(now);
    }
}
suite('SmoothScrollingOperation', () => {
    const VIEWPORT_HEIGHT = 800;
    const ANIMATION_DURATION = 125;
    const LINE_HEIGHT = 20;
    ensureNoDisposablesAreLeakedInTestSuite();
    function extractLines(scrollable, now) {
        const scrollTop = scrollable.testTick(now).scrollTop;
        const scrollBottom = scrollTop + VIEWPORT_HEIGHT;
        const startLineNumber = Math.floor(scrollTop / LINE_HEIGHT);
        const endLineNumber = Math.ceil(scrollBottom / LINE_HEIGHT);
        return [startLineNumber, endLineNumber];
    }
    function simulateSmoothScroll(from, to) {
        const scrollable = new TestSmoothScrollingOperation(from, to, VIEWPORT_HEIGHT, 0, ANIMATION_DURATION);
        const result = [];
        let resultLen = 0;
        result[resultLen++] = extractLines(scrollable, 0);
        result[resultLen++] = extractLines(scrollable, 25);
        result[resultLen++] = extractLines(scrollable, 50);
        result[resultLen++] = extractLines(scrollable, 75);
        result[resultLen++] = extractLines(scrollable, 100);
        result[resultLen++] = extractLines(scrollable, 125);
        return result;
    }
    function assertSmoothScroll(from, to, expected) {
        const actual = simulateSmoothScroll(from, to);
        assert.deepStrictEqual(actual, expected);
    }
    test('scroll 25 lines (40 fit)', () => {
        assertSmoothScroll(0, 500, [
            [5, 46],
            [14, 55],
            [20, 61],
            [23, 64],
            [24, 65],
            [25, 65],
        ]);
    });
    test('scroll 75 lines (40 fit)', () => {
        assertSmoothScroll(0, 1500, [
            [15, 56],
            [44, 85],
            [62, 103],
            [71, 112],
            [74, 115],
            [75, 115],
        ]);
    });
    test('scroll 100 lines (40 fit)', () => {
        assertSmoothScroll(0, 2000, [
            [20, 61],
            [59, 100],
            [82, 123],
            [94, 135],
            [99, 140],
            [100, 140],
        ]);
    });
    test('scroll 125 lines (40 fit)', () => {
        assertSmoothScroll(0, 2500, [
            [16, 57],
            [29, 70],
            [107, 148],
            [119, 160],
            [124, 165],
            [125, 165],
        ]);
    });
    test('scroll 500 lines (40 fit)', () => {
        assertSmoothScroll(0, 10000, [
            [16, 57],
            [29, 70],
            [482, 523],
            [494, 535],
            [499, 540],
            [500, 540],
        ]);
    });
});
//# sourceMappingURL=data:application/json;base64,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