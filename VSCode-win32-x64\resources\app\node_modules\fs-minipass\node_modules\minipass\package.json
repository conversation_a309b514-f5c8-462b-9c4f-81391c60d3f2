{"name": "minipass", "version": "3.1.3", "description": "minimal implementation of a PassThrough stream", "main": "index.js", "dependencies": {"yallist": "^4.0.0"}, "devDependencies": {"end-of-stream": "^1.4.0", "tap": "^14.6.5", "through2": "^2.0.3"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish --tag=next", "postpublish": "git push origin --follow-tags"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/minipass.git"}, "keywords": ["passthrough", "stream"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "files": ["index.js"], "tap": {"check-coverage": true}, "engines": {"node": ">=8"}}