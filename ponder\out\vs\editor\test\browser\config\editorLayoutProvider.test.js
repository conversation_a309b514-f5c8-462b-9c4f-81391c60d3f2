/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import assert from 'assert';
import { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../base/test/common/utils.js';
import { ComputedEditorOptions } from '../../../browser/config/editorConfiguration.js';
import { EditorLayoutInfoComputer, EditorOptions } from '../../../common/config/editorOptions.js';
suite('Editor ViewLayout - EditorLayoutProvider', () => {
    ensureNoDisposablesAreLeakedInTestSuite();
    function doTest(input, expected) {
        const options = new ComputedEditorOptions();
        options._write(66 /* EditorOption.glyphMargin */, input.showGlyphMargin);
        options._write(77 /* EditorOption.lineNumbersMinChars */, input.lineNumbersMinChars);
        options._write(74 /* EditorOption.lineDecorationsWidth */, input.lineDecorationsWidth);
        options._write(52 /* EditorOption.folding */, false);
        options._write(95 /* EditorOption.padding */, { top: 0, bottom: 0 });
        const minimapOptions = {
            enabled: input.minimap,
            autohide: 'none',
            size: input.minimapSize || 'proportional',
            side: input.minimapSide,
            renderCharacters: input.minimapRenderCharacters,
            maxColumn: input.minimapMaxColumn,
            showSlider: 'mouseover',
            scale: 1,
            showRegionSectionHeaders: true,
            showMarkSectionHeaders: true,
            sectionHeaderFontSize: 9,
            sectionHeaderLetterSpacing: 1,
            markSectionHeaderRegex: '\\bMARK:\\s*(?<separator>\-?)\\s*(?<label>.*)$',
        };
        options._write(81 /* EditorOption.minimap */, minimapOptions);
        const scrollbarOptions = {
            arrowSize: input.scrollbarArrowSize,
            vertical: EditorOptions.scrollbar.defaultValue.vertical,
            horizontal: EditorOptions.scrollbar.defaultValue.horizontal,
            useShadows: EditorOptions.scrollbar.defaultValue.useShadows,
            verticalHasArrows: input.verticalScrollbarHasArrows,
            horizontalHasArrows: false,
            handleMouseWheel: EditorOptions.scrollbar.defaultValue.handleMouseWheel,
            alwaysConsumeMouseWheel: true,
            horizontalScrollbarSize: input.horizontalScrollbarHeight,
            horizontalSliderSize: EditorOptions.scrollbar.defaultValue.horizontalSliderSize,
            verticalScrollbarSize: input.verticalScrollbarWidth,
            verticalSliderSize: EditorOptions.scrollbar.defaultValue.verticalSliderSize,
            scrollByPage: EditorOptions.scrollbar.defaultValue.scrollByPage,
            ignoreHorizontalScrollbarInContentHeight: false,
        };
        options._write(116 /* EditorOption.scrollbar */, scrollbarOptions);
        const lineNumbersOptions = {
            renderType: input.showLineNumbers ? 1 /* RenderLineNumbersType.On */ : 0 /* RenderLineNumbersType.Off */,
            renderFn: null
        };
        options._write(76 /* EditorOption.lineNumbers */, lineNumbersOptions);
        options._write(148 /* EditorOption.wordWrap */, 'off');
        options._write(151 /* EditorOption.wordWrapColumn */, 80);
        options._write(152 /* EditorOption.wordWrapOverride1 */, 'inherit');
        options._write(153 /* EditorOption.wordWrapOverride2 */, 'inherit');
        options._write(2 /* EditorOption.accessibilitySupport */, 'auto');
        const actual = EditorLayoutInfoComputer.computeLayout(options, {
            memory: null,
            outerWidth: input.outerWidth,
            outerHeight: input.outerHeight,
            isDominatedByLongLines: false,
            lineHeight: input.lineHeight,
            viewLineCount: input.maxLineNumber || Math.pow(10, input.lineNumbersDigitCount) - 1,
            lineNumbersDigitCount: input.lineNumbersDigitCount,
            typicalHalfwidthCharacterWidth: input.typicalHalfwidthCharacterWidth,
            maxDigitWidth: input.maxDigitWidth,
            pixelRatio: input.pixelRatio,
            glyphMarginDecorationLaneCount: 1,
        });
        assert.deepStrictEqual(actual, expected);
    }
    test('EditorLayoutProvider 1', () => {
        doTest({
            outerWidth: 1000,
            outerHeight: 800,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: false,
            lineNumbersMinChars: 0,
            lineNumbersDigitCount: 1,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: false,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            pixelRatio: 1,
        }, {
            width: 1000,
            height: 800,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 0,
            decorationsLeft: 0,
            decorationsWidth: 10,
            contentLeft: 10,
            contentWidth: 990,
            minimap: {
                renderMinimap: 0 /* RenderMinimap.None */,
                minimapLeft: 0,
                minimapWidth: 0,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 1,
                minimapLineHeight: 1,
                minimapCanvasInnerWidth: 0,
                minimapCanvasInnerHeight: 800,
                minimapCanvasOuterWidth: 0,
                minimapCanvasOuterHeight: 800,
            },
            viewportColumn: 98,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 800,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 1.1', () => {
        doTest({
            outerWidth: 1000,
            outerHeight: 800,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: false,
            lineNumbersMinChars: 0,
            lineNumbersDigitCount: 1,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 11,
            horizontalScrollbarHeight: 12,
            scrollbarArrowSize: 13,
            verticalScrollbarHasArrows: true,
            minimap: false,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            pixelRatio: 1,
        }, {
            width: 1000,
            height: 800,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 0,
            decorationsLeft: 0,
            decorationsWidth: 10,
            contentLeft: 10,
            contentWidth: 990,
            minimap: {
                renderMinimap: 0 /* RenderMinimap.None */,
                minimapLeft: 0,
                minimapWidth: 0,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 1,
                minimapLineHeight: 1,
                minimapCanvasInnerWidth: 0,
                minimapCanvasInnerHeight: 800,
                minimapCanvasOuterWidth: 0,
                minimapCanvasOuterHeight: 800,
            },
            viewportColumn: 97,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 11,
            horizontalScrollbarHeight: 12,
            overviewRuler: {
                top: 13,
                width: 11,
                height: (800 - 2 * 13),
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 2', () => {
        doTest({
            outerWidth: 900,
            outerHeight: 800,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: false,
            lineNumbersMinChars: 0,
            lineNumbersDigitCount: 1,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: false,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            pixelRatio: 1,
        }, {
            width: 900,
            height: 800,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 0,
            decorationsLeft: 0,
            decorationsWidth: 10,
            contentLeft: 10,
            contentWidth: 890,
            minimap: {
                renderMinimap: 0 /* RenderMinimap.None */,
                minimapLeft: 0,
                minimapWidth: 0,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 1,
                minimapLineHeight: 1,
                minimapCanvasInnerWidth: 0,
                minimapCanvasInnerHeight: 800,
                minimapCanvasOuterWidth: 0,
                minimapCanvasOuterHeight: 800,
            },
            viewportColumn: 88,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 800,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 3', () => {
        doTest({
            outerWidth: 900,
            outerHeight: 900,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: false,
            lineNumbersMinChars: 0,
            lineNumbersDigitCount: 1,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: false,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            pixelRatio: 1,
        }, {
            width: 900,
            height: 900,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 0,
            decorationsLeft: 0,
            decorationsWidth: 10,
            contentLeft: 10,
            contentWidth: 890,
            minimap: {
                renderMinimap: 0 /* RenderMinimap.None */,
                minimapLeft: 0,
                minimapWidth: 0,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 1,
                minimapLineHeight: 1,
                minimapCanvasInnerWidth: 0,
                minimapCanvasInnerHeight: 900,
                minimapCanvasOuterWidth: 0,
                minimapCanvasOuterHeight: 900,
            },
            viewportColumn: 88,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 900,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 4', () => {
        doTest({
            outerWidth: 900,
            outerHeight: 900,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: false,
            lineNumbersMinChars: 5,
            lineNumbersDigitCount: 1,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: false,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            pixelRatio: 1,
        }, {
            width: 900,
            height: 900,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 0,
            decorationsLeft: 0,
            decorationsWidth: 10,
            contentLeft: 10,
            contentWidth: 890,
            minimap: {
                renderMinimap: 0 /* RenderMinimap.None */,
                minimapLeft: 0,
                minimapWidth: 0,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 1,
                minimapLineHeight: 1,
                minimapCanvasInnerWidth: 0,
                minimapCanvasInnerHeight: 900,
                minimapCanvasOuterWidth: 0,
                minimapCanvasOuterHeight: 900,
            },
            viewportColumn: 88,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 900,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 5', () => {
        doTest({
            outerWidth: 900,
            outerHeight: 900,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: true,
            lineNumbersMinChars: 5,
            lineNumbersDigitCount: 1,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: false,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            pixelRatio: 1,
        }, {
            width: 900,
            height: 900,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 50,
            decorationsLeft: 50,
            decorationsWidth: 10,
            contentLeft: 60,
            contentWidth: 840,
            minimap: {
                renderMinimap: 0 /* RenderMinimap.None */,
                minimapLeft: 0,
                minimapWidth: 0,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 1,
                minimapLineHeight: 1,
                minimapCanvasInnerWidth: 0,
                minimapCanvasInnerHeight: 900,
                minimapCanvasOuterWidth: 0,
                minimapCanvasOuterHeight: 900,
            },
            viewportColumn: 83,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 900,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 6', () => {
        doTest({
            outerWidth: 900,
            outerHeight: 900,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: true,
            lineNumbersMinChars: 5,
            lineNumbersDigitCount: 5,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: false,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            pixelRatio: 1,
        }, {
            width: 900,
            height: 900,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 50,
            decorationsLeft: 50,
            decorationsWidth: 10,
            contentLeft: 60,
            contentWidth: 840,
            minimap: {
                renderMinimap: 0 /* RenderMinimap.None */,
                minimapLeft: 0,
                minimapWidth: 0,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 1,
                minimapLineHeight: 1,
                minimapCanvasInnerWidth: 0,
                minimapCanvasInnerHeight: 900,
                minimapCanvasOuterWidth: 0,
                minimapCanvasOuterHeight: 900,
            },
            viewportColumn: 83,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 900,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 7', () => {
        doTest({
            outerWidth: 900,
            outerHeight: 900,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: true,
            lineNumbersMinChars: 5,
            lineNumbersDigitCount: 6,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: false,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            pixelRatio: 1,
        }, {
            width: 900,
            height: 900,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 60,
            decorationsLeft: 60,
            decorationsWidth: 10,
            contentLeft: 70,
            contentWidth: 830,
            minimap: {
                renderMinimap: 0 /* RenderMinimap.None */,
                minimapLeft: 0,
                minimapWidth: 0,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 1,
                minimapLineHeight: 1,
                minimapCanvasInnerWidth: 0,
                minimapCanvasInnerHeight: 900,
                minimapCanvasOuterWidth: 0,
                minimapCanvasOuterHeight: 900,
            },
            viewportColumn: 82,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 900,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 8', () => {
        doTest({
            outerWidth: 900,
            outerHeight: 900,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: true,
            lineNumbersMinChars: 5,
            lineNumbersDigitCount: 6,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 5,
            maxDigitWidth: 5,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: false,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            pixelRatio: 1,
        }, {
            width: 900,
            height: 900,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 30,
            decorationsLeft: 30,
            decorationsWidth: 10,
            contentLeft: 40,
            contentWidth: 860,
            minimap: {
                renderMinimap: 0 /* RenderMinimap.None */,
                minimapLeft: 0,
                minimapWidth: 0,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 1,
                minimapLineHeight: 1,
                minimapCanvasInnerWidth: 0,
                minimapCanvasInnerHeight: 900,
                minimapCanvasOuterWidth: 0,
                minimapCanvasOuterHeight: 900,
            },
            viewportColumn: 171,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 900,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 8 - rounds floats', () => {
        doTest({
            outerWidth: 900,
            outerHeight: 900,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: true,
            lineNumbersMinChars: 5,
            lineNumbersDigitCount: 6,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 5.05,
            maxDigitWidth: 5.05,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: false,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            pixelRatio: 1,
        }, {
            width: 900,
            height: 900,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 30,
            decorationsLeft: 30,
            decorationsWidth: 10,
            contentLeft: 40,
            contentWidth: 860,
            minimap: {
                renderMinimap: 0 /* RenderMinimap.None */,
                minimapLeft: 0,
                minimapWidth: 0,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 1,
                minimapLineHeight: 1,
                minimapCanvasInnerWidth: 0,
                minimapCanvasInnerHeight: 900,
                minimapCanvasOuterWidth: 0,
                minimapCanvasOuterHeight: 900,
            },
            viewportColumn: 169,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 900,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 9 - render minimap', () => {
        doTest({
            outerWidth: 1000,
            outerHeight: 800,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: false,
            lineNumbersMinChars: 0,
            lineNumbersDigitCount: 1,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: true,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            pixelRatio: 1,
        }, {
            width: 1000,
            height: 800,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 0,
            decorationsLeft: 0,
            decorationsWidth: 10,
            contentLeft: 10,
            contentWidth: 893,
            minimap: {
                renderMinimap: 1 /* RenderMinimap.Text */,
                minimapLeft: 903,
                minimapWidth: 97,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 1,
                minimapLineHeight: 2,
                minimapCanvasInnerWidth: 97,
                minimapCanvasInnerHeight: 800,
                minimapCanvasOuterWidth: 97,
                minimapCanvasOuterHeight: 800,
            },
            viewportColumn: 89,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 800,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 9 - render minimap with pixelRatio = 2', () => {
        doTest({
            outerWidth: 1000,
            outerHeight: 800,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: false,
            lineNumbersMinChars: 0,
            lineNumbersDigitCount: 1,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: true,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            pixelRatio: 2,
        }, {
            width: 1000,
            height: 800,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 0,
            decorationsLeft: 0,
            decorationsWidth: 10,
            contentLeft: 10,
            contentWidth: 893,
            minimap: {
                renderMinimap: 1 /* RenderMinimap.Text */,
                minimapLeft: 903,
                minimapWidth: 97,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 2,
                minimapLineHeight: 4,
                minimapCanvasInnerWidth: 194,
                minimapCanvasInnerHeight: 1600,
                minimapCanvasOuterWidth: 97,
                minimapCanvasOuterHeight: 800,
            },
            viewportColumn: 89,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 800,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 9 - render minimap with pixelRatio = 4', () => {
        doTest({
            outerWidth: 1000,
            outerHeight: 800,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: false,
            lineNumbersMinChars: 0,
            lineNumbersDigitCount: 1,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: true,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            pixelRatio: 4,
        }, {
            width: 1000,
            height: 800,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 0,
            decorationsLeft: 0,
            decorationsWidth: 10,
            contentLeft: 10,
            contentWidth: 935,
            minimap: {
                renderMinimap: 1 /* RenderMinimap.Text */,
                minimapLeft: 945,
                minimapWidth: 55,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 2,
                minimapLineHeight: 4,
                minimapCanvasInnerWidth: 220,
                minimapCanvasInnerHeight: 3200,
                minimapCanvasOuterWidth: 55,
                minimapCanvasOuterHeight: 800,
            },
            viewportColumn: 93,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 800,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 10 - render minimap to left', () => {
        doTest({
            outerWidth: 1000,
            outerHeight: 800,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: false,
            lineNumbersMinChars: 0,
            lineNumbersDigitCount: 1,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: true,
            minimapSide: 'left',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            pixelRatio: 4,
        }, {
            width: 1000,
            height: 800,
            glyphMarginLeft: 55,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 55,
            lineNumbersWidth: 0,
            decorationsLeft: 55,
            decorationsWidth: 10,
            contentLeft: 65,
            contentWidth: 935,
            minimap: {
                renderMinimap: 1 /* RenderMinimap.Text */,
                minimapLeft: 0,
                minimapWidth: 55,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 2,
                minimapLineHeight: 4,
                minimapCanvasInnerWidth: 220,
                minimapCanvasInnerHeight: 3200,
                minimapCanvasOuterWidth: 55,
                minimapCanvasOuterHeight: 800,
            },
            viewportColumn: 93,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 800,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 11 - minimap mode cover without sampling', () => {
        doTest({
            outerWidth: 1000,
            outerHeight: 800,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: false,
            lineNumbersMinChars: 0,
            lineNumbersDigitCount: 3,
            maxLineNumber: 120,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: true,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            minimapSize: 'fill',
            pixelRatio: 2,
        }, {
            width: 1000,
            height: 800,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 0,
            decorationsLeft: 0,
            decorationsWidth: 10,
            contentLeft: 10,
            contentWidth: 893,
            minimap: {
                renderMinimap: 1 /* RenderMinimap.Text */,
                minimapLeft: 903,
                minimapWidth: 97,
                minimapHeightIsEditorHeight: true,
                minimapIsSampling: false,
                minimapScale: 3,
                minimapLineHeight: 13,
                minimapCanvasInnerWidth: 291,
                minimapCanvasInnerHeight: 1560,
                minimapCanvasOuterWidth: 97,
                minimapCanvasOuterHeight: 800,
            },
            viewportColumn: 89,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 800,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 12 - minimap mode cover with sampling', () => {
        doTest({
            outerWidth: 1000,
            outerHeight: 800,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: false,
            lineNumbersMinChars: 0,
            lineNumbersDigitCount: 4,
            maxLineNumber: 2500,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: true,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            minimapSize: 'fill',
            pixelRatio: 2,
        }, {
            width: 1000,
            height: 800,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 0,
            decorationsLeft: 0,
            decorationsWidth: 10,
            contentLeft: 10,
            contentWidth: 935,
            minimap: {
                renderMinimap: 1 /* RenderMinimap.Text */,
                minimapLeft: 945,
                minimapWidth: 55,
                minimapHeightIsEditorHeight: true,
                minimapIsSampling: true,
                minimapScale: 1,
                minimapLineHeight: 1,
                minimapCanvasInnerWidth: 110,
                minimapCanvasInnerHeight: 1600,
                minimapCanvasOuterWidth: 55,
                minimapCanvasOuterHeight: 800,
            },
            viewportColumn: 93,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 800,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 13 - minimap mode contain without sampling', () => {
        doTest({
            outerWidth: 1000,
            outerHeight: 800,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: false,
            lineNumbersMinChars: 0,
            lineNumbersDigitCount: 3,
            maxLineNumber: 120,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: true,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            minimapSize: 'fit',
            pixelRatio: 2,
        }, {
            width: 1000,
            height: 800,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 0,
            decorationsLeft: 0,
            decorationsWidth: 10,
            contentLeft: 10,
            contentWidth: 893,
            minimap: {
                renderMinimap: 1 /* RenderMinimap.Text */,
                minimapLeft: 903,
                minimapWidth: 97,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 2,
                minimapLineHeight: 4,
                minimapCanvasInnerWidth: 194,
                minimapCanvasInnerHeight: 1600,
                minimapCanvasOuterWidth: 97,
                minimapCanvasOuterHeight: 800,
            },
            viewportColumn: 89,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 800,
                right: 0
            }
        });
    });
    test('EditorLayoutProvider 14 - minimap mode contain with sampling', () => {
        doTest({
            outerWidth: 1000,
            outerHeight: 800,
            showGlyphMargin: false,
            lineHeight: 16,
            showLineNumbers: false,
            lineNumbersMinChars: 0,
            lineNumbersDigitCount: 4,
            maxLineNumber: 2500,
            lineDecorationsWidth: 10,
            typicalHalfwidthCharacterWidth: 10,
            maxDigitWidth: 10,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            scrollbarArrowSize: 0,
            verticalScrollbarHasArrows: false,
            minimap: true,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 150,
            minimapSize: 'fit',
            pixelRatio: 2,
        }, {
            width: 1000,
            height: 800,
            glyphMarginLeft: 0,
            glyphMarginWidth: 0,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 0,
            lineNumbersWidth: 0,
            decorationsLeft: 0,
            decorationsWidth: 10,
            contentLeft: 10,
            contentWidth: 935,
            minimap: {
                renderMinimap: 1 /* RenderMinimap.Text */,
                minimapLeft: 945,
                minimapWidth: 55,
                minimapHeightIsEditorHeight: true,
                minimapIsSampling: true,
                minimapScale: 1,
                minimapLineHeight: 1,
                minimapCanvasInnerWidth: 110,
                minimapCanvasInnerHeight: 1600,
                minimapCanvasOuterWidth: 55,
                minimapCanvasOuterHeight: 800,
            },
            viewportColumn: 93,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 0,
            horizontalScrollbarHeight: 0,
            overviewRuler: {
                top: 0,
                width: 0,
                height: 800,
                right: 0
            }
        });
    });
    test('issue #31312: When wrapping, leave 2px for the cursor', () => {
        doTest({
            outerWidth: 1201,
            outerHeight: 422,
            showGlyphMargin: true,
            lineHeight: 30,
            showLineNumbers: true,
            lineNumbersMinChars: 3,
            lineNumbersDigitCount: 1,
            lineDecorationsWidth: 26,
            typicalHalfwidthCharacterWidth: 12.04296875,
            maxDigitWidth: 12.04296875,
            verticalScrollbarWidth: 14,
            horizontalScrollbarHeight: 10,
            scrollbarArrowSize: 11,
            verticalScrollbarHasArrows: false,
            minimap: true,
            minimapSide: 'right',
            minimapRenderCharacters: true,
            minimapMaxColumn: 120,
            pixelRatio: 2
        }, {
            width: 1201,
            height: 422,
            glyphMarginLeft: 0,
            glyphMarginWidth: 30,
            glyphMarginDecorationLaneCount: 1,
            lineNumbersLeft: 30,
            lineNumbersWidth: 36,
            decorationsLeft: 66,
            decorationsWidth: 26,
            contentLeft: 92,
            contentWidth: 1018,
            minimap: {
                renderMinimap: 1 /* RenderMinimap.Text */,
                minimapLeft: 1096,
                minimapWidth: 91,
                minimapHeightIsEditorHeight: false,
                minimapIsSampling: false,
                minimapScale: 2,
                minimapLineHeight: 4,
                minimapCanvasInnerWidth: 182,
                minimapCanvasInnerHeight: 844,
                minimapCanvasOuterWidth: 91,
                minimapCanvasOuterHeight: 422,
            },
            viewportColumn: 83,
            isWordWrapMinified: false,
            isViewportWrapping: false,
            wrappingColumn: -1,
            verticalScrollbarWidth: 14,
            horizontalScrollbarHeight: 10,
            overviewRuler: {
                top: 0,
                width: 14,
                height: 422,
                right: 0
            }
        });
    });
});
//# sourceMappingURL=data:application/json;base64,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