/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
export var BackgroundTokenizationState;
(function (BackgroundTokenizationState) {
    BackgroundTokenizationState[BackgroundTokenizationState["InProgress"] = 1] = "InProgress";
    BackgroundTokenizationState[BackgroundTokenizationState["Completed"] = 2] = "Completed";
})(BackgroundTokenizationState || (BackgroundTokenizationState = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidG9rZW5pemF0aW9uVGV4dE1vZGVsUGFydC5qcyIsInNvdXJjZVJvb3QiOiJmaWxlOi8vL0Q6L1Byb2plY3RzL3BvbmRlci1hbGwvcG9uZGVyL3NyYy8iLCJzb3VyY2VzIjpbInZzL2VkaXRvci9jb21tb24vdG9rZW5pemF0aW9uVGV4dE1vZGVsUGFydC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7O2dHQUdnRztBQStGaEcsTUFBTSxDQUFOLElBQWtCLDJCQUdqQjtBQUhELFdBQWtCLDJCQUEyQjtJQUM1Qyx5RkFBYyxDQUFBO0lBQ2QsdUZBQWEsQ0FBQTtBQUNkLENBQUMsRUFIaUIsMkJBQTJCLEtBQTNCLDJCQUEyQixRQUc1QyJ9